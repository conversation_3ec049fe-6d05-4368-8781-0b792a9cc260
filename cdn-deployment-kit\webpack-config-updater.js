/**
 * Webpack 配置更新器
 * 负责动态设置 webpack 的 publicPath 为 CDN URL
 */

const fs = require('fs')
const path = require('path')

// 导入统一日志系统
const { logger } = require('./logger')

// 创建带上下文的日志器
const log = logger.child('webpack-config-updater')

class WebpackConfigUpdater {
  constructor (projectRoot = process.cwd()) {
    this.projectRoot = projectRoot
    this.possibleConfigPaths = [
      'build/webpack.prod.conf.js',
      'webpack.config.js',
      'config/webpack.prod.js',
      'webpack.prod.config.js'
    ]
  }

  /**
   * 动态更新 Webpack 配置的 publicPath
   * @param {object} webpackConfig - Webpack 配置对象
   * @param {string} cdnUrl - CDN 基础 URL
   * @returns {object} 更新后的配置
   */
  updatePublicPath (webpackConfig, cdnUrl) {
    log.config('更新 Webpack publicPath...')

    try {
      // 标准化 CDN URL
      const normalizedCdnUrl = this.normalizeCdnUrl(cdnUrl)

      // 直接修改配置，避免深拷贝导致的问题
      if (!webpackConfig.output) {
        webpackConfig.output = {}
      }

      const originalPublicPath = webpackConfig.output.publicPath
      webpackConfig.output.publicPath = normalizedCdnUrl

      log.success(`publicPath 已更新: ${originalPublicPath || '(未设置)'} -> ${normalizedCdnUrl}`)

      return webpackConfig
    } catch (error) {
      log.error('更新 publicPath 失败:', error.message)
      throw error
    }
  }

  /**
   * 标准化 CDN URL
   * @param {string} cdnUrl - 原始 CDN URL
   * @returns {string} 标准化后的 CDN URL
   */
  normalizeCdnUrl (cdnUrl) {
    if (!cdnUrl || typeof cdnUrl !== 'string') {
      throw new Error('CDN URL 不能为空')
    }

    // 确保 URL 以 / 结尾
    return cdnUrl.endsWith('/') ? cdnUrl : cdnUrl + '/'
  }

  /**
   * 更新插件配置中的 publicPath 相关设置
   * @param {object} config - Webpack 配置
   * @param {string} cdnUrl - CDN URL
   */
  updatePluginConfigs (config, cdnUrl) {
    if (!config.plugins || !Array.isArray(config.plugins)) {
      return
    }

    let updatedPlugins = 0

    config.plugins.forEach((plugin, index) => {
      if (!plugin || !plugin.constructor) {
        return
      }

      const pluginName = plugin.constructor.name

      switch (pluginName) {
        case 'HtmlWebpackPlugin':
          this.updateHtmlWebpackPlugin(plugin, cdnUrl)
          updatedPlugins++
          break

        case 'ExtractTextPlugin':
        case 'MiniCssExtractPlugin':
          this.updateCssExtractPlugin(plugin, cdnUrl)
          updatedPlugins++
          break

        case 'ManifestPlugin':
          this.updateManifestPlugin(plugin, cdnUrl)
          updatedPlugins++
          break

        default:
          // 检查是否有 publicPath 相关配置
          if (plugin.options && typeof plugin.options === 'object') {
            this.updateGenericPluginPublicPath(plugin, cdnUrl)
          }
          break
      }
    })

    if (updatedPlugins > 0) {
      log.debug(`更新了 ${updatedPlugins} 个插件的 publicPath 配置`)
    }
  }

  /**
   * 更新 HtmlWebpackPlugin 配置
   * @param {object} plugin - 插件实例
   * @param {string} cdnUrl - CDN URL
   */
  updateHtmlWebpackPlugin (plugin, cdnUrl) {
    if (plugin.options) {
      // 设置 publicPath，这将影响资源引用
      plugin.options.publicPath = cdnUrl
      log.debug('已更新 HtmlWebpackPlugin publicPath')
    }
  }

  /**
   * 更新 CSS 提取插件配置
   * @param {object} plugin - 插件实例
   * @param {string} cdnUrl - CDN URL
   */
  updateCssExtractPlugin (plugin, cdnUrl) {
    if (plugin.options) {
      // 更新 CSS 文件的 publicPath
      if (plugin.options.filename) {
        // 确保 CSS 文件路径正确
        plugin.options.publicPath = cdnUrl
      }
      log.debug('已更新 CSS 提取插件 publicPath')
    }
  }

  /**
   * 更新 Manifest 插件配置
   * @param {object} plugin - 插件实例
   * @param {string} cdnUrl - CDN URL
   */
  updateManifestPlugin (plugin, cdnUrl) {
    if (plugin.options) {
      // 更新 manifest 中的 publicPath
      plugin.options.publicPath = cdnUrl
      log.debug('已更新 ManifestPlugin publicPath')
    }
  }

  /**
   * 更新通用插件的 publicPath 配置
   * @param {object} plugin - 插件实例
   * @param {string} cdnUrl - CDN URL
   */
  updateGenericPluginPublicPath (plugin, cdnUrl) {
    if (plugin.options.publicPath !== undefined) {
      plugin.options.publicPath = cdnUrl
      log.debug(`已更新 ${plugin.constructor.name} publicPath`)
    }
  }

  /**
   * 创建运行时 publicPath 设置
   * @param {string} cdnUrl - CDN URL
   * @returns {string} 运行时代码
   */
  createRuntimePublicPath (cdnUrl) {
    return `
// 动态设置 webpack publicPath
if (typeof __webpack_public_path__ !== 'undefined') {
  __webpack_public_path__ = '${cdnUrl}';
}

// 设置全局 CDN URL
if (typeof window !== 'undefined') {
  window.CDN_URL = '${cdnUrl}';
}
`
  }

  /**
   * 添加运行时 publicPath 插件
   * @param {object} config - Webpack 配置
   * @param {string} cdnUrl - CDN URL
   */
  addRuntimePublicPathPlugin (config, cdnUrl) {
    try {
      const webpack = require('webpack')

      if (!config.plugins) {
        config.plugins = []
      }

      // 检查是否已经存在相同的插件，避免重复添加
      const hasDefinePlugin = config.plugins.some(plugin =>
        plugin && plugin.constructor && plugin.constructor.name === 'DefinePlugin'
      )

      if (!hasDefinePlugin) {
        // 添加 DefinePlugin 来设置全局变量
        const definePlugin = new webpack.DefinePlugin({
          'process.env.CDN_URL': JSON.stringify(cdnUrl),
          '__CDN_URL__': JSON.stringify(cdnUrl)
        })

        config.plugins.push(definePlugin)
        log.debug('已添加 DefinePlugin')
      } else {
        log.debug('DefinePlugin 已存在，跳过添加')
      }

      // 暂时禁用 BannerPlugin，因为它可能导致兼容性问题
      // const bannerPlugin = new webpack.BannerPlugin({
      //   banner: this.createRuntimePublicPath(cdnUrl),
      //   raw: true,
      //   entryOnly: true
      // })
      // config.plugins.push(bannerPlugin)

      log.success('已添加运行时 publicPath 设置')
    } catch (error) {
      log.warn('添加运行时插件失败:', error.message)
      log.debug('将跳过运行时插件添加，继续构建过程')
    }
  }

  /**
   * 验证配置更新
   * @param {object} config - 更新后的配置
   * @param {string} expectedCdnUrl - 期望的 CDN URL
   */
  validateConfigUpdate (config, expectedCdnUrl) {
    log.config('验证配置更新...')

    const issues = []

    // 检查 output.publicPath
    if (!config.output || config.output.publicPath !== expectedCdnUrl) {
      issues.push('output.publicPath 未正确设置')
    }

    // 检查插件配置
    if (config.plugins) {
      config.plugins.forEach((plugin, index) => {
        if (plugin && plugin.options && plugin.options.publicPath) {
          if (plugin.options.publicPath !== expectedCdnUrl) {
            issues.push(`插件 ${plugin.constructor.name} 的 publicPath 未正确设置`)
          }
        }
      })
    }

    if (issues.length === 0) {
      log.success('配置验证通过')
    } else {
      log.warn('配置验证发现问题:')
      issues.forEach(issue => log.warn(`  - ${issue}`))
    }

    return issues.length === 0
  }

  /**
   * 生成配置更新报告
   * @param {object} originalConfig - 原始配置
   * @param {object} updatedConfig - 更新后的配置
   * @param {string} cdnUrl - CDN URL
   * @returns {object} 更新报告
   */
  generateUpdateReport (originalConfig, updatedConfig, cdnUrl) {
    const report = {
      timestamp: new Date().toISOString(),
      cdnUrl: cdnUrl,
      changes: {
        publicPath: {
          before: originalConfig.output?.publicPath || '(未设置)',
          after: updatedConfig.output?.publicPath
        },
        pluginsUpdated: 0,
        runtimeInjection: false
      }
    }

    // 统计插件更新数量
    if (updatedConfig.plugins) {
      updatedConfig.plugins.forEach(plugin => {
        if (plugin && plugin.options && plugin.options.publicPath === cdnUrl) {
          report.changes.pluginsUpdated++
        }
      })
    }

    // 检查是否添加了运行时注入
    if (updatedConfig.plugins) {
      const hasDefinePlugin = updatedConfig.plugins.some(plugin =>
        plugin && plugin.constructor.name === 'DefinePlugin'
      )
      const hasBannerPlugin = updatedConfig.plugins.some(plugin =>
        plugin && plugin.constructor.name === 'BannerPlugin'
      )
      report.changes.runtimeInjection = hasDefinePlugin && hasBannerPlugin
    }

    return report
  }
}

module.exports = { WebpackConfigUpdater }
