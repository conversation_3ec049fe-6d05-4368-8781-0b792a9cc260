<template>
  <div class="page">
    <div class="search" style="min-height: 60px;">
      <div>
        <el-form :model="form" inline size="mini">
          <el-form-item label="样例编号">
            <el-input v-model="form.sampleCode" type="textarea" clearable placeholder="请输入样例编号用逗号分隔"></el-input>
          </el-form-item>
          <el-form-item label="Patient ID">
            <el-input v-model="form.patientId" type="textarea" clearable placeholder="请输入Patient ID用逗号分隔"></el-input>
          </el-form-item>
          <el-form-item label="混合探针">
            <el-input v-model="form.probe" type="textarea" clearable placeholder="请输入混合探针用逗号分隔"></el-input>
          </el-form-item>
          <el-form-item label="样例状态">
            <el-select v-model="form.sampleStatus" multiple collapse-tags filterable size="mini" clearable
              class="input-width">
              <el-option v-for="(item, index) in sampleStatusOptions" :key="index" :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleChange">{{ showAdvancedSearch ? '隐藏' : '高级' }}查询</el-button>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" size="mini" plain @click="handleSearch">查询</el-button>
            <el-button size="mini" plain @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <!--高级查询-->
    <el-collapse-transition>
      <div ref="advance-search" v-show="showAdvancedSearch" class="search advance-search" style="height: auto;">
        <div style="width: 100%">
          <el-form :model="form" label-width="130px" label-position="right" inline size="mini">
            <el-form-item label="原始编号">
              <el-input v-model.trim="form.originalCode" clearable class="input-width"></el-input>
            </el-form-item>
            <!-- <el-form-item label="送检单位">
              <el-input v-model.trim="form.inspectionUnit" clearable class="input-width"></el-input>
            </el-form-item> -->
            <el-form-item label="订单联系人1">
              <el-input v-model.trim="form.inspectionSales" clearable class="input-width"></el-input>
            </el-form-item>
            <el-form-item label="探针订购组织类型">
              <el-select v-model="form.probeOrganizationTags" multiple collapse-tags filterable size="mini" clearable
                class="input-width">
                <el-option v-for="(item, index) in probeOrganizationTagsOptions" :key="index" :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="项目名称">
              <el-input v-model.trim="form.productName" clearable class="input-width"></el-input>
            </el-form-item>

            <el-form-item label="样本标签">
              <el-select v-model="form.sampleLabel" multiple collapse-tags filterable size="mini" clearable
                class="input-width">
                <el-option v-for="(item, index) in sampleLabelOptions" :key="index" :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="流转方式">
              <el-select v-model="form.clinicalResearch" filterable size="mini" clearable
                class="input-width">
                <el-option label="全部" :value="2"></el-option>
                <el-option label="非自动" :value="0"></el-option>
                <el-option label="自动" :value="1"></el-option>
                <el-option label="科研A" :value="3"></el-option>
                <el-option label="科研B" :value="4"></el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="探针内部环节">
              <el-select v-model="form.probeInternalLink" multiple collapse-tags filterable size="mini" clearable
                class="input-width">
                <el-option v-for="(item, index) in probeInternalLinkOptions" :key="index" :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <div slot="label">
                <el-dropdown @command="(command) => handleCommand(command,1)">
                  <span class="el-dropdown-link">
                    {{ timeKeyOne }}<i class="el-icon-arrow-down el-icon--right"></i>
                  </span>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item command="报告时间">报告时间</el-dropdown-item>
                    <el-dropdown-item command="预交付时间">预交付时间</el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </div>
              <el-date-picker v-model="form.reportTime" value-format="yyyy-MM-dd HH:mm:ss"
                :default-time="['00:00:00', '23:59:59']" type="daterange" size="mini" class="date" placeholder="选择日期">
              </el-date-picker>
            </el-form-item>
            <el-form-item>
              <el-dropdown @command="(command) => handleCommand(command,2)">
                <span class="el-dropdown-link">
                  {{ timeKeyTwo }}<i class="el-icon-arrow-down el-icon--right"></i>
                </span>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="送检时间">送检时间</el-dropdown-item>
                  <el-dropdown-item command="到样时间">到样时间</el-dropdown-item>
                  <el-dropdown-item command="开启时间">开启时间</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
              <el-date-picker v-model="form.expectedDeliveryTime" value-format="yyyy-MM-dd HH:mm:ss"
                :default-time="['00:00:00', '23:59:59']" type="daterange" size="mini" class="date" placeholder="选择日期">
              </el-date-picker>
            </el-form-item>

            <el-form-item label="是否已订购探针">
              <el-select v-model="form.isSubscribe" multiple filterable size="mini" clearable class="input-width">
                <el-option label="否" :value="0"></el-option>
                <el-option label="是" :value="1"></el-option>
              </el-select>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </el-collapse-transition>
    <div class="content">
      <!--      功能按钮-->
      <div class="buttonGroup">
        <el-button v-if="$setAuthority('009008001', 'buttons')" :loading="exportLoading" type="primary" size="mini"
          plain @click="handleRemark">
          <i class="el-icon-edit"></i>
          备注
        </el-button>
        <el-button v-if="$setAuthority('009008002', 'buttons')" :loading="exportLoading" type="primary" size="mini"
          plain @click="handleExport">
          <i class="el-icon-share"></i>
          导出
        </el-button>
        <el-button v-if="$setAuthority('009008003', 'buttons')" :loading="exportLoading" type="primary" size="mini"
          plain @click="handleExportAll">
          <i class="el-icon-share"></i>
          筛选导出
        </el-button>
        <el-button v-if="$setAuthority('009008004', 'buttons')" :loading="exportLoading" type="primary" size="mini"
          plain @click="handleLinkDataBoard">
          <i class="el-icon-s-data"></i>
          数据看板
        </el-button>
      </div>
      <!--      列表-->
      <div>
        <el-table ref="table" :data="tableData"
          :height="`calc(100vh - 80px - 45px - 45px - 86px - ${advancedSearchHeight}px)`" :cell-style="handleRowStyle"
          class="table" size="mini" border style="width: 100%" @select="handleSelectTable" @row-click="handleRowClick"
          @sort-change="handleSortChange" @select-all="handleSelectAll">
          <el-table-column type="selection" width="45" fixed="left"></el-table-column>
          <el-table-column type="index" label="序号" width="50" fixed="left"></el-table-column>
          <el-table-column prop="sampleSign" label="样例标签" width="180">
            <template slot-scope="scope">
              <el-tag v-if="item !== '-'" v-for="(item, index) in scope.row.sampleSign" :key="'tag' + index"
                style="margin-right: 3px" size="mini">{{ item }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="probeOrganizationTags" label="探针订购组织类型" width="120"
            show-overflow-tooltip></el-table-column>
          <el-table-column prop="sampleNum" label="样例编号" width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="previousSampleCode" label="既往组织" width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="patientID" label="Patient ID" width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="isSubscribe" label="是否已订购探针" width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="fhybirdProbeName" label="混合探针名称" width="160" show-overflow-tooltip></el-table-column>
          <el-table-column prop="experimentStatusText" label="样例状态" width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="currentStep" label="检测环节" width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="originNum" label="原始编号" width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="productName" label="项目名称" width="140" show-overflow-tooltip></el-table-column>
          <!-- <el-table-column prop="inspectionUnit" label="送检单位" width="180" show-overflow-tooltip></el-table-column> -->
          <el-table-column prop="orderEntryPerson" label="订单联系人1" width="120" show-overflow-tooltip></el-table-column>
          <el-table-column sortable="custom" prop="switchingTimes" label="开启时间" width="180"
            show-overflow-tooltip></el-table-column>
          <el-table-column sortable="custom" prop="sampleConfirmTime" label="到样时间" width="180"
            show-overflow-tooltip></el-table-column>
          <el-table-column sortable="custom" prop="inspectionTime" label="送检日期" width="180"
            show-overflow-tooltip></el-table-column>
          <el-table-column sortable="custom" prop="preDeliverDate" label="预交付时间" width="180"
            show-overflow-tooltip></el-table-column>
          <el-table-column sortable="custom" prop="reportDate" label="报告时间" width="180"
            show-overflow-tooltip></el-table-column>
          <el-table-column prop="remainderTATDay" label="理论TAT" width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="realTATDay" label="实际TAT" width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="remark" label="备注" width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="tissueSampleCode" label="组织编号" width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="tissueExperimentStatusText" label="组织样例状态" width="120"
            show-overflow-tooltip></el-table-column>
          <el-table-column prop="tissueCurrentStep" label="组织检测环节" width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="tissueSampleConfirmTime" label="组织到样时间" width="180"
            show-overflow-tooltip></el-table-column>
          <el-table-column prop="tissueReportDate" label="组织报告时间" width="180" show-overflow-tooltip></el-table-column>
          <el-table-column prop="fprobeStatusText" label="探针内部环节" width="180" show-overflow-tooltip></el-table-column>
          <el-table-column sortable="custom" prop="fsubscribeTime" label="探针订购时间" width="180"
            show-overflow-tooltip></el-table-column>
          <el-table-column sortable="custom" prop="fdesignTime" label="探针设计时间" width="180"
            show-overflow-tooltip></el-table-column>
          <el-table-column sortable="custom" prop="fsyntheticEndTime" label="探针合成时间" width="180"
            show-overflow-tooltip></el-table-column>
          <el-table-column sortable="custom" prop="fconsignTime" label="探针发货时间" width="180"
            show-overflow-tooltip></el-table-column>
          <el-table-column sortable="custom" prop="fsignTime" label="探针到货时间" width="180"
            show-overflow-tooltip></el-table-column>
          <el-table-column prop="fconfigTime" label="探针配置时间" width="180" show-overflow-tooltip></el-table-column>
          <el-table-column prop="fremainTatTime" label="探针剩余TAT" width="180">
            <template slot-scope="scope">
              <span :style="`color: ${scope.row.remainingTATColor}`">{{ scope.row.remainingTATText }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="crossDate" label="探针杂交时间" width="180" show-overflow-tooltip></el-table-column>
          <el-table-column prop="libNum" label="建库文库编号" width="180" show-overflow-tooltip></el-table-column>
          <el-table-column prop="extractSum" label="提取总量" width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="nonCpcrQubit" label="建库浓度" width="120" show-overflow-tooltip></el-table-column>
        </el-table>
        <el-pagination :page-sizes="pageSizes" :page-size="pageSize" :current-page.sync="currentPage" :total="totalPage"
          layout="total, sizes, prev, pager, next, jumper, slot" @size-change="handleSizeChange"
          @current-change="handleCurrentChange">
          <button @click="handleRefresh">
            <icon-svg icon-class="icon-refresh" />
          </button>
        </el-pagination>
      </div>
    </div>
    <remark-dialog :pvisible.sync="remarkVisible" :sample-num="sampleNum" @dialogConfirmEvent="getData"></remark-dialog>
  </div>
</template>

<script>
import mixins from '../../../../util/mixins'
import util from '../../../../util/util'
import remarkDialog from './components/remarkDialog'

export default {
  name: 'patientInfoDetail',
  mixins: [mixins.tablePaginationCommonData],
  components: {
    remarkDialog
  },
  mounted () {
    this.handleInitDate()
    this.handleSearch()
  },
  data () {
    return {
      remarkVisible: false,
      showAdvancedSearch: false,
      advancedSearchHeight: 0,
      exportLoading: false,
      selectedRows: new Map(),
      sampleNum: '',
      sortForm: {},
      sampleLabelOptions: [
        {
          label: '延期',
          value: 1
        },
        {
          label: '加急',
          value: 2
        },
        {
          label: '未发报告',
          value: 3
        },
        {
          label: '已发报告',
          value: 4
        }
      ],
      clinicalResearchOptions: [
        {
          label: '全部',
          value: 2
        },
        {
          label: '非自动',
          value: 0
        },
        {
          label: '自动',
          value: 1
        },
        {
          label: '科研A',
          value: 3
        },
        {
          label: '科研B',
          value: 4
        }
      ],
      probeInternalLinkOptions: [
        {
          label: '已订购',
          value: 10
        },
        {
          label: '待设计',
          value: 0
        },
        {
          label: '设计中',
          value: 1
        },
        {
          label: '待合成',
          value: 2
        },
        {
          label: '合成中',
          value: 3
        },
        {
          label: '暂停中',
          value: 4
        },
        {
          label: '待发货',
          value: 5
        },
        {
          label: '运输中',
          value: 6
        },
        {
          label: '待签收',
          value: 7
        },
        {
          label: '已签收',
          value: 8
        },
        {
          label: '已作废',
          value: 9
        }
      ],
      probeOrganizationTagsOptions: [
        {
          label: '本次送检1021组织',
          value: 0
        },
        {
          label: '既往送检组织',
          value: 1
        },
        {
          label: '吉因加院内检测组织',
          value: 2
        },
        {
          label: '非吉因加组织',
          value: 3
        },
        {
          label: '非首次血无需订购',
          value: 4
        },
        {
          label: '空值',
          value: 5
        }
      ],
      sampleStatusOptions: [
        {
          label: '待送样',
          value: 0
        },
        {
          label: '送样中',
          value: 1
        },
        {
          label: '已到样',
          value: 2
        },
        {
          label: '暂停检测',
          value: 3
        },
        {
          label: '停止检测',
          value: 4
        },
        {
          label: '检测中',
          value: 5
        },
        {
          label: '停止检测-重送样',
          value: 6
        },
        {
          label: '已重送样',
          value: 7
        },
        {
          label: '已发报告',
          value: 8
        },
        {
          label: '检测完成',
          value: 9
        }
      ],
      timeKeyOne: '报告时间',
      timeKeyTwo: '送检时间',
      form: {
        sampleCode: '',
        patientId: '',
        probe: '',
        sampleStatus: [],
        originalCode: '',
        inspectionUnit: '',
        inspectionSales: '',
        probeRemainingTAT: '',
        productName: '',
        sampleLabel: '',
        clinicalResearch: 1,
        probeInternalLink: '',
        reportTime: '',
        expectedDeliveryTime: '',
        inspectionTime: '',
        sampleArrivalTime: '',
        startTime: '',
        probeOrganizationTags: '',
        isSubscribe: []
      }
    }
  },
  methods: {
    /**
     * 切换搜索时间菜单
     * @param type 菜单类型
     */
    handleCommand (command, type) {
      type === 1 ? this.timeKeyOne = command : this.timeKeyTwo = command
    },
    handleSortChange ({prop, order}) {
      this.sortForm = {
        [prop + 'Order']: order === 'ascending' ? 1 : 0
      }
      this.handleSearch()
    },
    // 数据做备注标识。
    handleRemark () {
      if (this.selectedRows.size !== 1) {
        this.$message.error('请选择1条数据进行操作')
        return
      }
      let row = [...this.selectedRows.values()][0] || {}
      this.sampleNum = row.sampleNum
      this.remarkVisible = true
    },
    formMateTimeSearch (form) {
      const timeKeyMapping = {
        '报告时间': ['reportStartDate', 'reportEndDate'],
        '预交付时间': ['preDeliverStartDate', 'preDeliverEndDate'],
        '送检时间': ['inspectionStartTime', 'inspectionEndTime'],
        '到样时间': ['sampleConfirmStartTime', 'sampleConfirmEndTime'],
        '开启时间': ['switchingStartTime', 'switchingEndTime']
      }
      let timeSearch = [this.timeKeyOne, this.timeKeyTwo]
      let timeForm = {}
      const timeSearchValue = [
        form.reportTime,
        form.expectedDeliveryTime
      ]
      timeSearch.forEach((v, index) => {
        let keys = timeKeyMapping[v]
        let value = timeSearchValue[index] || []
        if (value.length === 2) {
          timeForm[keys[0]] = timeSearchValue[index][0]
          timeForm[keys[1]] = timeSearchValue[index][1]
        }
      })
      return timeForm
    },
    // 格式化搜索表单
    forMateForm () {
      const form = JSON.parse(JSON.stringify(this.form))
      const timeForm = this.formMateTimeSearch(form)
      const submitForm = {
        sampleNum: form.sampleCode || '',
        patientId: form.patientId || '',
        fhybirdProbeName: form.probe || '',
        experimentStatus: form.sampleStatus.join(','),
        originNum: form.originalCode,
        inspectionUnit: form.inspectionUnit,
        fprobeOrganizationTags: form.probeOrganizationTags,
        fisSubscribe: form.isSubscribe.length > 1 ? '' : form.isSubscribe.join(','),
        orderEntryPerson: form.inspectionSales,
        productName: form.productName,
        sampleSign: form.sampleLabel.join(','),
        clinicalScientific: form.clinicalResearch,
        fprobeStatus: form.probeInternalLink.join(','),
        isDelay: '',
        isExpress: '',
        isReport: '',
        ...timeForm,
        ...this.sortForm
      }
      submitForm.sampleNum = submitForm.sampleNum.replace(/\n/g, ',').replace(/\s/g, ',').replace(/，/g, ',')
      submitForm.patientId = submitForm.patientId.replace(/\n/g, ',').replace(/\s/g, ',').replace(/，/g, ',')
      submitForm.fhybirdProbeName = submitForm.fhybirdProbeName.replace(/\n/g, ',').replace(/\s/g, ',').replace(/，/g, ',')

      return util.deepCopy(submitForm)
    },
    // 导出
    handleExport () {
      if (this.selectedRows.size < 1) {
        this.$message.error('请选择需要导出的数据')
        return
      }
      this.exportLoading = true
      let rows = [...this.selectedRows.values()]
      let sampleNums = rows.map(v => v.sampleNum)
      this.$ajax({
        url: '/system/monitor/download_mrd_sample_info',
        data: sampleNums,
        responseType: 'blob'
      }).then(res => {
        util.readBlob(res.data).then(() => {
          util.downloadFile(res, true)
        }).catch(msg => {
          this.$message.error(msg)
        })
      }).finally(() => {
        this.exportLoading = false
      })
    },
    // 导出全部
    handleExportAll () {
      const submitForm = this.forMateForm()
      this.exportLoading = true
      this.$ajax({
        url: '/system/monitor/download_all_mrd_sample_info',
        data: {
          ...submitForm
        },
        responseType: 'blob'
      }).then(res => {
        util.readBlob(res.data).then(() => {
          util.downloadFile(res, true)
        }).catch(msg => {
          this.$message.error(msg)
        })
      }).finally(() => {
        this.exportLoading = false
      })
    },
    handleSearch () {
      this.submitForm = this.forMateForm()
      this.getData()
    },
    handleReset () {
      this.form = {
        sampleCode: '',
        patientId: '',
        probe: '',
        sampleStatus: [],
        originalCode: '',
        inspectionUnit: '',
        inspectionSales: '',
        probeRemainingTAT: '',
        productName: '',
        sampleLabel: [],
        clinicalResearch: 1,
        probeInternalLink: [],
        reportTime: '',
        expectedDeliveryTime: '',
        inspectionTime: '',
        sampleArrivalTime: '',
        startTime: '',
        isSubscribe: []
      }
      this.handleInitDate()
      this.timeKeyOne = '报告时间'
      this.timeKeyTwo = '送检时间'
      this.sortForm = {}
      this.currentPage = 1
      this.$refs.table.clearSort()
      this.handleSearch()
    },
    // 当时间都没值的时候默认查询送检时间近一个月的数据
    handleInitDate () {
      const now = new Date()
      const dateString = now.toISOString().split('T')[0]
      const inspectionStartTime = util.getTimeDiff(new Date().setMonth((new Date().getMonth() - 1))) + ' ' + '00:00:00'
      const inspectionEndTime = dateString + ' ' + '23:59:59'
      this.form.expectedDeliveryTime = [inspectionStartTime, inspectionEndTime]
    },
    getData () {
      this.$ajax({
        url: '/system/monitor/get_sample_mrd_list',
        loadingDom: '.table',
        data: {
          page: this.currentPage,
          rows: this.pageSize,
          ...this.submitForm
        }
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          let data = res.data || {}
          let records = data.records || []
          this.selectedRows.clear()
          this.tableData = []
          const experimentStatus = {
            0: '待送样',
            1: '送样中',
            2: '已到样',
            3: '暂停检测',
            4: '停止检测',
            5: '检测中',
            6: '停止检测-重送样',
            7: '已重送样',
            8: '已发报告',
            9: '检测完成'
          }
          this.totalPage = res.data.total
          records.forEach(record => {
            let probeInternalLinks = this.probeInternalLinkOptions.find(v => v.value + '' === record.fprobeStatus) || {}
            let item = {
              id: record.sampleNum,
              sampleProductId: record.sampleProductId,
              sampleSign: record.sampleSign && record.sampleSign.split(','),
              sampleNum: record.sampleNum,
              probeOrganizationTags: record.fprobeOrganizationTags,
              previousSampleCode: record.fpreviousSampleCode,
              isSubscribe: record.fisSubscribe === 0 ? '否' : '是',
              patientID: record.patientID,
              fhybirdProbeName: record.fhybirdProbeName,
              experimentStatus: record.experimentStatus,
              experimentStatusText: experimentStatus[record.experimentStatus],
              currentStep: record.currentStep,
              originNum: record.originNum,
              productName: record.productName,
              inspectionUnit: record.inspectionUnit,
              orderEntryPerson: record.orderEntryPerson,
              switchingTimes: record.switchingTimes,
              sampleConfirmTime: record.sampleConfirmTime,
              inspectionTime: record.inspectionTime,
              preDeliverDate: record.preDeliverDate,
              reportDate: record.reportDate,
              remainderTATDay: record.remainderTATDay,
              realTATDay: record.realTATDay,
              remark: record.sampleMrdRemark,
              tissueSampleCode: record.tissueSampleCode,
              tissueExperimentStatus: record.tissueExperimentStatus,
              tissueExperimentStatusText: experimentStatus[record.tissueExperimentStatus],
              tissueCurrentStep: record.tissueCurrentStep,
              tissueSampleConfirmTime: record.tissueSampleConfirmTime,
              tissueReportDate: record.tissueReportDate,
              fprobeStatus: record.fprobeStatus,
              fprobeStatusText: probeInternalLinks.label,
              fsubscribeTime: record.fsubscribeTime,
              fdesignTime: record.fdesignTime,
              fsyntheticEndTime: record.fsyntheticEndTime,
              fconsignTime: record.fconsignTime,
              fsignTime: record.fsignTime,
              fconfigTime: record.fconfigTime,
              fremainTatTime: record.fremainTatTime,
              crossDate: record.crossDate,
              libNum: record.libNum,
              extractSum: record.extractSum,
              nonCpcrQubit: record.nonCpcrQubit
            }
            const {text, color} = this.getRemainingTAT(record.fpredictDeliveryTime, record.fprobeStatus)
            item.remainingTATText = text
            item.remainingTATColor = color
            let realData = item
            util.setDefaultEmptyValueForObject(item)
            item.realData = realData
            this.tableData.push(item)
          })
        }
      })
    },
    // 获取剩余TAT的值
    getRemainingTAT (completionTime, probeStatus) {
      if (!probeStatus) {
        return {text: '', color: ''}
      }
      if (+probeStatus === 8) {
        return {text: '已交付', color: '#29e76a'}
      }
      const currentTime = new Date().getTime()
      const completionTimeNum = new Date(completionTime).getTime()
      if (currentTime > completionTimeNum) {
        return {text: '已超时', color: '#f73f34'}
      }
      const time = Math.floor((completionTimeNum - currentTime) / 1000 / 60 / 60 / 24) // 展示日期向下取整
      return {text: `${time}天`, color: '#539fff'}
    },
    handleChange () {
      this.showAdvancedSearch = !this.showAdvancedSearch
      this.$nextTick(() => {
        setTimeout(() => {
          this.advancedSearchHeight = document.querySelector('.advance-search').offsetHeight
        }, 300)
        // this.showAdvancedSearch ? this.advancedSearchHeight = 159 : this.advancedSearchHeight = 0
      })
      // 获取advance-search高度
    },
    handleLinkDataBoard () {
      window.open('/business/subpage/dataBoard')
    },
    // 点击行
    handleRowClick (row, c) {
      this.$refs.table.toggleRowSelection(row, !this.selectedRows.has(row.id))
      this.handleSelectTable(undefined, row)
    },
    // 选中行
    handleSelectTable (selection, row) {
      this.selectedRows.has(row.id) ? this.selectedRows.delete(row.id) : this.selectedRows.set(row.id, row)
    },
    // 全选
    handleSelectAll (selection) {
      this.selectedRows.clear()
      selection.forEach((row) => {
        this.selectedRows.set(row.id, row)
      })
    }
  }
}
</script>

<style scoped lang="scss">
.page {
  transition: all .3s;
}

.input-width {
  width: 220px;
}

.search {
  //background-color: #ffffff;
  //padding-top: 20px;
  //display: flex;
  //align-items: center;
  //transition: all .3s;

  .search-row {
    display: flex;
    width: auto;
    margin-bottom: 20px;

    & > div {
      margin-right: 30px;

      label {
        font-size: 14px;
        color: #606266;
        display: inline-block;
        width: 100px;
        text-align: right;
        padding: 0 12px 0 0;
        box-sizing: border-box;
      }

      .input {
        width: 200px;
      }
    }
  }
}

.content {
  background-color: #ffffff;

  .buttonGroup {
    height: 45px;
    display: flex;
    align-items: center;
    //margin: 0 20px;
    > > > .el-button--mini {
      padding: 5px 10px;
    }

    .iconClass {
      padding-right: 5px;
      font-size: 16px;
    }
  }
}

> > > .el-pagination {
  padding: 7px 2em;
}
</style>
