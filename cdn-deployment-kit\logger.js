/**
 * CDN部署工具包 - 统一日志系统
 * 提供标准化的日志输出，支持不同级别、颜色和格式
 */

// 日志级别定义
const LOG_LEVELS = {
  ERROR: 0,
  WARN: 1,
  INFO: 2,
  DEBUG: 3,
  TRACE: 4
}

// 日志级别名称映射
const LEVEL_NAMES = {
  [LOG_LEVELS.ERROR]: 'ERROR',
  [LOG_LEVELS.WARN]: 'WARN',
  [LOG_LEVELS.INFO]: 'INFO',
  [LOG_LEVELS.DEBUG]: 'DEBUG',
  [LOG_LEVELS.TRACE]: 'TRACE'
}

// 颜色定义（兼容无chalk环境）
const COLORS = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
  gray: '\x1b[90m'
}

// 日志级别对应的颜色和图标
const LEVEL_STYLES = {
  [LOG_LEVELS.ERROR]: { color: COLORS.red, icon: '❌', prefix: 'ERROR' },
  [LOG_LEVELS.WARN]: { color: COLORS.yellow, icon: '⚠️ ', prefix: 'WARN' },
  [LOG_LEVELS.INFO]: { color: COLORS.cyan, icon: 'ℹ️ ', prefix: 'INFO' },
  [LOG_LEVELS.DEBUG]: { color: COLORS.gray, icon: '🔍', prefix: 'DEBUG' },
  [LOG_LEVELS.TRACE]: { color: COLORS.dim, icon: '📝', prefix: 'TRACE' }
}

// 特殊消息类型的样式
const MESSAGE_STYLES = {
  success: { color: COLORS.green, icon: '✅' },
  progress: { color: COLORS.blue, icon: '🔄' },
  start: { color: COLORS.blue, icon: '🚀' },
  complete: { color: COLORS.green, icon: '🎉' },
  build: { color: COLORS.blue, icon: '🏗️ ' },
  analyze: { color: COLORS.cyan, icon: '📊' },
  upload: { color: COLORS.blue, icon: '📤' },
  deploy: { color: COLORS.blue, icon: '🌐' },
  config: { color: COLORS.yellow, icon: '⚙️ ' },
  file: { color: COLORS.white, icon: '📁' },
  time: { color: COLORS.magenta, icon: '⏱️ ' },
  size: { color: COLORS.cyan, icon: '📦' },
  performance: { color: COLORS.yellow, icon: '⚡' }
}

/**
 * 统一日志器类
 */
class Logger {
  constructor (options = {}) {
    // 从环境变量或选项中获取日志级别
    const envLevel = process.env.LOG_LEVEL?.toUpperCase()
    this.level = options.level || LOG_LEVELS[envLevel] || LOG_LEVELS.INFO

    // 其他配置选项
    this.showTimestamp = options.showTimestamp !== false
    this.showLevel = options.showLevel !== false
    this.showIcon = options.showIcon !== false
    this.colorEnabled = options.colorEnabled !== false && process.stdout.isTTY
    this.context = options.context || ''

    // 尝试加载chalk，如果失败则使用内置颜色
    this.chalk = null
    try {
      this.chalk = require('chalk')
    } catch (error) {
      // 使用内置颜色系统
    }
  }

  /**
   * 设置日志上下文
   */
  setContext (context) {
    this.context = context
    return this
  }

  /**
   * 创建子日志器
   */
  child (context) {
    return new Logger({
      level: this.level,
      showTimestamp: this.showTimestamp,
      showLevel: this.showLevel,
      showIcon: this.showIcon,
      colorEnabled: this.colorEnabled,
      context: this.context ? `${this.context}:${context}` : context
    })
  }

  /**
   * 格式化时间戳
   */
  formatTimestamp () {
    if (!this.showTimestamp) return ''

    const now = new Date()
    const timestamp = now.toISOString().replace('T', ' ').slice(0, 19)
    return this.colorEnabled ? `${COLORS.gray}[${timestamp}]${COLORS.reset} ` : `[${timestamp}] `
  }

  /**
   * 格式化上下文
   */
  formatContext () {
    if (!this.context) return ''

    return this.colorEnabled
      ? `${COLORS.magenta}[${this.context}]${COLORS.reset} `
      : `[${this.context}] `
  }

  /**
   * 应用颜色
   */
  applyColor (text, color) {
    if (!this.colorEnabled) return text

    if (this.chalk) {
      // 使用chalk库
      const colorMap = {
        [COLORS.red]: this.chalk.red,
        [COLORS.green]: this.chalk.green,
        [COLORS.yellow]: this.chalk.yellow,
        [COLORS.blue]: this.chalk.blue,
        [COLORS.cyan]: this.chalk.cyan,
        [COLORS.white]: this.chalk.white,
        [COLORS.gray]: this.chalk.gray,
        [COLORS.magenta]: this.chalk.magenta
      }
      const chalkColor = colorMap[color]
      return chalkColor ? chalkColor(text) : text
    } else {
      // 使用ANSI颜色码
      return `${color}${text}${COLORS.reset}`
    }
  }

  /**
   * 格式化日志消息
   */
  formatMessage (level, message, style = null) {
    const levelStyle = LEVEL_STYLES[level]
    const messageStyle = style || levelStyle

    let formatted = ''

    // 添加时间戳
    formatted += this.formatTimestamp()

    // 添加上下文
    formatted += this.formatContext()

    // 添加图标和级别
    if (this.showIcon && messageStyle.icon) {
      formatted += messageStyle.icon + ' '
    }

    if (this.showLevel) {
      const levelText = `[${LEVEL_NAMES[level]}]`
      formatted += this.applyColor(levelText, messageStyle.color) + ' '
    }

    // 添加消息内容
    formatted += this.applyColor(message, messageStyle.color)

    return formatted
  }

  /**
   * 通用日志方法
   */
  log (level, message, ...args) {
    if (level > this.level) return

    const formatted = this.formatMessage(level, message)
    console.log(formatted, ...args)
  }

  /**
   * 错误日志
   */
  error (message, ...args) {
    this.log(LOG_LEVELS.ERROR, message, ...args)
  }

  /**
   * 警告日志
   */
  warn (message, ...args) {
    this.log(LOG_LEVELS.WARN, message, ...args)
  }

  /**
   * 信息日志
   */
  info (message, ...args) {
    this.log(LOG_LEVELS.INFO, message, ...args)
  }

  /**
   * 调试日志
   */
  debug (message, ...args) {
    this.log(LOG_LEVELS.DEBUG, message, ...args)
  }

  /**
   * 跟踪日志
   */
  trace (message, ...args) {
    this.log(LOG_LEVELS.TRACE, message, ...args)
  }

  /**
   * 特殊消息类型的便捷方法
   */
  success (message, ...args) {
    const formatted = this.formatMessage(LOG_LEVELS.INFO, message, MESSAGE_STYLES.success)
    console.log(formatted, ...args)
  }

  progress (message, ...args) {
    const formatted = this.formatMessage(LOG_LEVELS.INFO, message, MESSAGE_STYLES.progress)
    console.log(formatted, ...args)
  }

  start (message, ...args) {
    const formatted = this.formatMessage(LOG_LEVELS.INFO, message, MESSAGE_STYLES.start)
    console.log(formatted, ...args)
  }

  complete (message, ...args) {
    const formatted = this.formatMessage(LOG_LEVELS.INFO, message, MESSAGE_STYLES.complete)
    console.log(formatted, ...args)
  }

  build (message, ...args) {
    const formatted = this.formatMessage(LOG_LEVELS.INFO, message, MESSAGE_STYLES.build)
    console.log(formatted, ...args)
  }

  analyze (message, ...args) {
    const formatted = this.formatMessage(LOG_LEVELS.INFO, message, MESSAGE_STYLES.analyze)
    console.log(formatted, ...args)
  }

  upload (message, ...args) {
    const formatted = this.formatMessage(LOG_LEVELS.INFO, message, MESSAGE_STYLES.upload)
    console.log(formatted, ...args)
  }

  deploy (message, ...args) {
    const formatted = this.formatMessage(LOG_LEVELS.INFO, message, MESSAGE_STYLES.deploy)
    console.log(formatted, ...args)
  }

  config (message, ...args) {
    const formatted = this.formatMessage(LOG_LEVELS.INFO, message, MESSAGE_STYLES.config)
    console.log(formatted, ...args)
  }

  file (message, ...args) {
    const formatted = this.formatMessage(LOG_LEVELS.INFO, message, MESSAGE_STYLES.file)
    console.log(formatted, ...args)
  }

  time (message, ...args) {
    const formatted = this.formatMessage(LOG_LEVELS.INFO, message, MESSAGE_STYLES.time)
    console.log(formatted, ...args)
  }

  size (message, ...args) {
    const formatted = this.formatMessage(LOG_LEVELS.INFO, message, MESSAGE_STYLES.size)
    console.log(formatted, ...args)
  }

  performance (message, ...args) {
    const formatted = this.formatMessage(LOG_LEVELS.INFO, message, MESSAGE_STYLES.performance)
    console.log(formatted, ...args)
  }

  /**
   * 纯文本输出（不带格式）
   */
  plain (message, ...args) {
    console.log(message, ...args)
  }

  /**
   * 分隔线
   */
  separator (char = '=', length = 50) {
    const line = char.repeat(length)
    this.plain(this.applyColor(line, COLORS.gray))
  }

  /**
   * 标题输出
   */
  title (message) {
    this.separator()
    this.info(message)
    this.separator()
  }
}

// 创建默认日志器实例
const defaultLogger = new Logger()

// 导出日志器类和默认实例
module.exports = {
  Logger,
  LOG_LEVELS,
  LEVEL_NAMES,
  logger: defaultLogger,

  // 便捷方法
  error: (...args) => defaultLogger.error(...args),
  warn: (...args) => defaultLogger.warn(...args),
  info: (...args) => defaultLogger.info(...args),
  debug: (...args) => defaultLogger.debug(...args),
  trace: (...args) => defaultLogger.trace(...args),
  success: (...args) => defaultLogger.success(...args),
  progress: (...args) => defaultLogger.progress(...args),
  start: (...args) => defaultLogger.start(...args),
  complete: (...args) => defaultLogger.complete(...args),
  build: (...args) => defaultLogger.build(...args),
  analyze: (...args) => defaultLogger.analyze(...args),
  upload: (...args) => defaultLogger.upload(...args),
  deploy: (...args) => defaultLogger.deploy(...args),
  config: (...args) => defaultLogger.config(...args),
  file: (...args) => defaultLogger.file(...args),
  time: (...args) => defaultLogger.time(...args),
  size: (...args) => defaultLogger.size(...args),
  performance: (...args) => defaultLogger.performance(...args),
  plain: (...args) => defaultLogger.plain(...args),
  separator: (...args) => defaultLogger.separator(...args),
  title: (...args) => defaultLogger.title(...args)
}
