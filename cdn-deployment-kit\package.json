{"name": "cdn-deployment-kit", "version": "1.1.0", "description": "完整的CDN部署解决方案，集成构建、优化、上传功能，针对Webpack 3.x优化", "main": "deploy-cdn-unified.js", "bin": {"cdn-deploy": "./deploy.js"}, "scripts": {"deploy": "node deploy.js deploy", "deploy:dev": "node deploy.js deploy --env development", "deploy:test": "node deploy.js deploy --env test", "deploy:prod": "node deploy.js deploy --env production", "build": "node run.js build optimized", "build:dev": "node deploy.js build --env development", "build:test": "node deploy.js build --env test", "build:prod": "node deploy.js build --env production", "analyze": "node run.js analyze", "upload": "node run.js upload", "upload:dev": "node deploy.js deploy --env development --no-build", "upload:test": "node deploy.js deploy --env test --no-build", "upload:prod": "node deploy.js deploy --env production --no-build", "setup": "node setup-cdn.js", "verify": "node verify-deployment.js", "test-env": "node test-environment-deploy.js", "help": "node deploy.js help"}, "keywords": ["cdn", "deployment", "webpack", "webpack3", "gzip", "optimization", "parallel-build", "al<PERSON>s", "tencentcos", "build", "performance", "frontend", "vue", "react", "angular"], "author": "Frontend Development Team", "license": "MIT", "engines": {"node": ">=12.0.0"}, "dependencies": {"dotenv": "^16.0.0", "chalk": "^4.1.2", "gzip-size": "^6.0.0", "ora": "^5.4.1", "rimraf": "^3.0.2", "uglifyjs-webpack-plugin": "^1.3.0"}, "peerDependencies": {"webpack": "^3.6.0"}, "optionalDependencies": {"ali-oss": "^6.17.1", "aws-sdk": "^2.1200.0", "cache-loader": "^1.2.5", "cos-nodejs-sdk-v5": "^2.11.2", "qiniu": "^7.7.0", "thread-loader": "^1.2.0"}, "devDependencies": {}, "repository": {"type": "git", "url": "https://github.com/your-org/cdn-deployment-kit.git"}, "bugs": {"url": "https://github.com/your-org/cdn-deployment-kit/issues"}, "homepage": "https://github.com/your-org/cdn-deployment-kit#readme", "files": ["*.js", "*.md", ".env.example", "package.json"]}