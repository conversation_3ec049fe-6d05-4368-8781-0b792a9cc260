<script>
import mixins from '../../../../../util/mixins'
import Cookies from 'js-cookie'
import {awaitWrap, getSessionInfo} from '../../../../../util/util'
import {addLab} from '../../../../../api/deliveryManagement'

export default {
  name: 'addLabConfigDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    projectCode: {
      type: String,
      default: ''
    },
    experimentRegionList: {
      type: Array,
      default: () => []
    }
  },
  data () {
    return {
      loading: false,
      labList: JSON.parse(Cookies.get('labOptions') || '')
        .filter(v => (getSessionInfo('currentLab') || [])
          .includes(v.value)).filter(v => v.label !== '苏州'),
      form: {
        lab: ''
      },
      rules: {
        lab: [{required: true, message: '请选择实验室', trigger: 'change'}]
      }
    }
  },
  methods: {
    handleOpen () {
      this.$refs.form.resetFields()
      // 过滤已存在的实验室
      const labs = [
        '北京实验室',
        '上海实验室',
        '深圳实验室'
      ]
      this.labList = labs.filter(v => !this.experimentRegionList.includes(v))
    },
    async handleConfirm () {
      await this.handleValidForm()
      this.loading = true
      const {res} = await awaitWrap(addLab({
        fprojectCode: this.projectCode,
        fexperimentRegion: this.form.lab
      }))
      if (res && res.code === this.SUCCESS_CODE) {
        this.$message.success('新增成功')
        this.$emit('dialogConfirmEvent')
        this.visible = false
      }
      this.loading = false
    }
  }
}
</script>

<template>
  <el-dialog
    append-to-body
    title="新增实验室"
    :visible.sync="visible"
    :close-on-click-modal="false"
    :before-close="handleClose"
    width="500px"
    @opened="handleOpen">
    <el-form ref="form" :model="form" label-width="120px" size="mini">
      <el-form-item label="实验室" prop="lab">
        <el-select v-model="form.lab" placeholder="请选择实验室" clearable style="width: 100%">
          <el-option v-for="item in labList" :key="item" :label="item" :value="item"></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button size="mini" @click="handleClose">取 消</el-button>
      <el-button :loading="loading" type="primary" size="mini" @click="handleConfirm">确 认</el-button>
    </span>
  </el-dialog>
</template>

<style scoped lang="scss">

</style>
