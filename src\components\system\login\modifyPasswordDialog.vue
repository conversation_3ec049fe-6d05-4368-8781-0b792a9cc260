<template>
  <div>
    <el-dialog
      title="修改密码"
      :visible.sync="visible"
      :close-on-click-modal="false"
      :before-close="handleClose"
      width="500px"
      @open="handleOpen">
      <el-form :model="form" ref="form" :rules="rules" size="mini" align="left" label-width="100px">
        <el-form-item label="旧密码:" align="left" prop="oldPassword">
          <el-input :type="showPasswordInputType('op')" v-model="form.oldPassword">
            <template #suffix >
              <icon-svg class="input-inner-icon" :icon-class="showPasswordIcon('op')" @click.native.stop="handlePasswordShow('op')"></icon-svg>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="新密码:" align="left" prop="newPassword">
          <el-input :type="showPasswordInputType('np1')" v-model="form.newPassword">
            <template #suffix >
              <icon-svg class="input-inner-icon" :icon-class="showPasswordIcon('np1')" @click.native.stop="handlePasswordShow('np1')"></icon-svg>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="确认密码:" align="left" prop="checkPassword">
          <el-input :type="showPasswordInputType('np2')" v-model="form.checkPassword" >
            <template #suffix >
              <icon-svg class="input-inner-icon" :icon-class="showPasswordIcon('np2')" @click.native.stop="handlePasswordShow('np2')"></icon-svg>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item align="center" label-width="0">
          <el-button type="primary" :loading="submitLoading" @click="handleConfirm">提 交</el-button>
          <el-button @click="handleClose">取 消</el-button>
        </el-form-item>
        <el-form-item label-width="0">
          <div class="password-tip">
            <div class="title"><icon-svg icon-class="icon-tishi" class="icon"></icon-svg>温馨提示</div>
            <p>1、同一账号在吉因加公司系统中的密码均相同。</p>
            <p>2、本次修改成功后，登录吉因加公司其他系统时也请记得使用新密码。</p>
            <p>3、密码规则：必须8~16位，必须数字、大写字母、小写字母和特殊字符四种中任意3种组合而成，不能使用连续或相同的三位以上数字或者字母组合，如123，456，abc，111，aaa</p>
          </div>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { awaitWrap, validatePassword } from 'Util'
import { modifyPassword } from '@/api/system/login'
import mixins from '@/util/mixins'
export default {
  name: 'modifyPasswordDialog.',
  mixins: [mixins.dialogBaseInfo],
  computed: {
    userInfo () {
      return this.$store.getters.userInfo || {}
    }
  },
  data () {
    const validateCheckPass = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请再次输入密码'))
      } else if (value !== this.form.newPassword) {
        callback(new Error('两次输入密码不一致!'))
      } else if (value === this.form.oldPassword) {
        callback(new Error('新密码不能与旧密码相同!'))
      } else {
        callback()
      }
    }
    return {
      submitLoading: false,
      showPasswordTypes: [], // 默认都是空
      form: {
        oldPassword: '',
        newPassword: '',
        checkPassword: ''
      },
      rules: {
        oldPassword: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        newPassword: [
          { required: true, message: '请填入密码', trigger: 'blur' },
          { validator: validatePassword, trigger: 'blur' }
        ],
        checkPassword: [
          { required: true, message: '请填入确认密码', trigger: 'blur' },
          { validator: validateCheckPass, trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    handleOpen () {
      this.$nextTick(() => {
        this.$refs.form.resetFields()
      })
    },
    async handleConfirm () {
      await this.validateForm()
      const params = this.setParams()
      this.submitLoading = true
      const { res } = await awaitWrap(modifyPassword(params))
      if (res.code === this.SUCCESS_CODE) {
        this.$emit('dialogConfirmEvent')
        this.visible = false
        await this.$alert('修改密码成功，请重新登录', '成功', {
          confirmButtonText: '确定',
          type: 'success',
          dangerouslyUseHTMLString: true
        })
        this.$store.dispatch('loginOut')
        this.$store.dispatch('delPath')
        this.$router.push({ path: '/login' })
      }
      this.submitLoading = false
    },
    validateForm () {
      return new Promise(resolve => {
        this.$refs.form.validate(valid => {
          if (valid) {
            resolve()
          } else {
            this.$message.error('存在必填字段未填写')
          }
        })
      })
    },
    setParams () {
      return {
        fid: this.userInfo.id,
        foldPassword: this.form.oldPassword,
        fnewPassword: this.form.newPassword
      }
    },
    showPasswordIcon (type) {
      return this.showPasswordTypes.includes(type) ? 'icon-yanjing_xianshi' : 'icon-yanjing_yincang'
    },
    showPasswordInputType (type) {
      return this.showPasswordTypes.includes(type) ? 'text' : 'password'
    },
    handlePasswordShow (type) {
      const index = this.showPasswordTypes.indexOf(type)
      index === -1 ? this.showPasswordTypes.push(type) : this.showPasswordTypes.splice(index, 1)
    }
  }
}
</script>

<style scoped>
  .el-input__inner{
    color: black;
    padding: 0 15px;
    height: 32px;
    line-height: 32px;
  }
  .password-tip{
    line-height: 1.5;
    font-size: 12px;
    color: #999999;
    margin-top: 10px;
  }
  .title {
    position: relative;
  }
  .icon {
    color: #000;
    margin-right: 10px;
  }
  .input-inner-icon{
    font-size: 20px;
  }
</style>
