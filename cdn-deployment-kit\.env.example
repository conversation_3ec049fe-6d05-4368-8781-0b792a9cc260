# CDN部署工具包环境变量配置示例
# 复制此文件为 .env 并填入真实的配置值

# ===========================================
# 阿里云OSS配置 (推荐)
# ===========================================
OSS_ACCESS_KEY_ID=your-access-key-id
OSS_ACCESS_KEY_SECRET=your-access-key-secret
OSS_BUCKET=genereadonly
OSS_REGION=oss-cn-beijing
OSS_DOMAIN=cdn.geneplus.org.cn
# OSS_ENDPOINT=oss-cn-beijing.aliyuncs.com

# ===========================================
# 腾讯云COS配置
# ===========================================
# COS_SECRET_ID=your-secret-id
# COS_SECRET_KEY=your-secret-key
# COS_BUCKET=your-bucket-name
# COS_REGION=ap-beijing
# COS_DOMAIN=your-cos-domain.com

# ===========================================
# AWS S3配置
# ===========================================
# AWS_ACCESS_KEY_ID=your-access-key-id
# AWS_SECRET_ACCESS_KEY=your-secret-access-key
# AWS_BUCKET=your-bucket-name
# AWS_REGION=us-east-1
# AWS_DOMAIN=your-s3-domain.com

# ===========================================
# 七牛云配置
# ===========================================
# QINIU_ACCESS_KEY=your-access-key
# QINIU_SECRET_KEY=your-secret-key
# QINIU_BUCKET=your-bucket-name
# QINIU_DOMAIN=your-qiniu-domain.com
# QINIU_ZONE=Zone_z0

# ===========================================
# 部署配置
# ===========================================
# 部署环境: development, test, staging, production
NODE_ENV=test

# CDN版本号 (可选，默认使用时间戳)
# CDN_VERSION=v1.0.0

# ===========================================
# 使用说明
# ===========================================
# 1. 复制此文件为 .env
# 2. 填入对应CDN提供商的真实配置
# 3. 运行部署命令:
#    node cdn-deployment-kit/run.js upload
#    node cdn-deployment-kit/run.js deploy
#
# 支持的CDN提供商:
# - aliOSS (阿里云OSS) - 推荐
# - tencentCOS (腾讯云COS)
# - awsS3 (AWS S3)
# - qiniu (七牛云)
#
# 支持的环境:
# - development (开发环境)
# - test (测试环境) - 默认
# - staging (预发布环境)
# - production (生产环境)
