/**
 * HTML 资源引用更新器
 * 负责更新 HTML 文件中的静态资源路径为 CDN 路径
 */

const fs = require('fs')
const path = require('path')

// 导入统一日志系统
const { logger } = require('./logger')

// 创建带上下文的日志器
const log = logger.child('html-updater')

class HtmlUpdater {
  constructor (projectRoot = process.cwd()) {
    this.projectRoot = projectRoot
    this.distPath = path.join(projectRoot, 'dist')
    this.indexPath = path.join(this.distPath, 'index.html')
    this.dllPath = path.join(projectRoot, 'dll', 'vendor.dll.js')
    this.dllDistPath = path.join(this.distPath, 'static', 'js', 'vendor.dll.js')
  }

  /**
   * 更新 HTML 文件中的资源引用
   * @param {string} cdnUrl - CDN 基础 URL
   * @param {object} options - 更新选项
   */
  updateHtmlReferences (cdnUrl, options = {}) {
    log.config('开始更新 HTML 资源引用...')

    try {
      // 检查 index.html 是否存在
      if (!fs.existsSync(this.indexPath)) {
        throw new Error(`HTML 文件不存在: ${this.indexPath}`)
      }

      // 读取 HTML 内容
      let content = fs.readFileSync(this.indexPath, 'utf8')
      log.debug(`读取 HTML 文件: ${this.indexPath}`)

      // 标准化 CDN URL
      const normalizedCdnUrl = this.normalizeCdnUrl(cdnUrl)
      log.debug(`标准化 CDN URL: ${normalizedCdnUrl}`)

      // 备份原始文件
      if (options.backup !== false) {
        this.backupOriginalFile()
      }

      // 执行各种资源路径替换
      content = this.replaceStaticPaths(content, normalizedCdnUrl)
      content = this.addCrossOriginAttributes(content)
      content = this.handleDllReferences(content, normalizedCdnUrl)

      // 处理其他资源类型
      if (options.updateImages !== false) {
        content = this.updateImageReferences(content, normalizedCdnUrl)
      }

      if (options.updateFonts !== false) {
        content = this.updateFontReferences(content, normalizedCdnUrl)
      }

      // 写入更新后的内容
      fs.writeFileSync(this.indexPath, content, 'utf8')
      log.success('HTML 资源引用更新完成')

      // 验证更新结果
      this.validateUpdatedHtml(normalizedCdnUrl)

      return {
        success: true,
        updatedFile: this.indexPath,
        cdnUrl: normalizedCdnUrl,
        backupCreated: options.backup !== false
      }
    } catch (error) {
      log.error('HTML 资源引用更新失败:', error.message)

      // 尝试恢复备份
      if (options.backup !== false) {
        this.restoreFromBackup()
      }

      throw error
    }
  }

  /**
   * 标准化 CDN URL
   * @param {string} cdnUrl - 原始 CDN URL
   * @returns {string} 标准化后的 CDN URL
   */
  normalizeCdnUrl (cdnUrl) {
    if (!cdnUrl || typeof cdnUrl !== 'string') {
      throw new Error('CDN URL 不能为空')
    }

    // 确保 URL 以 / 结尾
    return cdnUrl.endsWith('/') ? cdnUrl : cdnUrl + '/'
  }

  /**
   * 备份原始 HTML 文件
   */
  backupOriginalFile () {
    const backupPath = this.indexPath + '.backup'

    try {
      fs.copyFileSync(this.indexPath, backupPath)
      log.debug(`创建备份文件: ${backupPath}`)
    } catch (error) {
      log.warn('创建备份文件失败:', error.message)
    }
  }

  /**
   * 从备份恢复文件
   */
  restoreFromBackup () {
    const backupPath = this.indexPath + '.backup'

    try {
      if (fs.existsSync(backupPath)) {
        fs.copyFileSync(backupPath, this.indexPath)
        log.info('已从备份恢复 HTML 文件')
      }
    } catch (error) {
      log.error('从备份恢复失败:', error.message)
    }
  }

  /**
   * 替换静态资源路径
   * @param {string} content - HTML 内容
   * @param {string} cdnUrl - CDN URL
   * @returns {string} 更新后的内容
   */
  replaceStaticPaths (content, cdnUrl) {
    log.config('替换静态资源路径...')

    // 替换各种静态资源路径格式
    // 保持 static/ 目录结构，与 CDN 上传后的目录结构一致
    const replacements = [
      // 优先处理带引号的路径（更精确）
      { pattern: /="\/?static\//g, replacement: '="' + cdnUrl + 'static/' },
      { pattern: /='\/?static\//g, replacement: "='" + cdnUrl + 'static/' },
      { pattern: /src="\/?static\//g, replacement: 'src="' + cdnUrl + 'static/' },
      { pattern: /href="\/?static\//g, replacement: 'href="' + cdnUrl + 'static/' },
      { pattern: /src='\/?static\//g, replacement: "src='" + cdnUrl + 'static/' },
      { pattern: /href='\/?static\//g, replacement: "href='" + cdnUrl + 'static/' },

      // 处理无引号的情况，添加引号
      { pattern: /(\s)(src|href)=\/?static\//g, replacement: '$1$2="' + cdnUrl + 'static/' },
      { pattern: /^(src|href)=\/?static\//g, replacement: '$1="' + cdnUrl + 'static/' },

      // 通用处理（作为后备）
      { pattern: /=\/?static\//g, replacement: '="' + cdnUrl + 'static/' }
    ]

    let updatedContent = content
    let replacementCount = 0

    replacements.forEach(({ pattern, replacement }) => {
      const matches = updatedContent.match(pattern)
      if (matches) {
        replacementCount += matches.length
        updatedContent = updatedContent.replace(pattern, replacement)
      }
    })

    // 后处理：确保所有CDN URL都有正确的引号结束
    updatedContent = this.fixAttributeQuotes(updatedContent, cdnUrl)

    log.success(`完成 ${replacementCount} 个静态资源路径替换`)
    return updatedContent
  }

  /**
   * 修复属性引号问题
   * @param {string} content - HTML 内容
   * @param {string} cdnUrl - CDN URL
   * @returns {string} 修复后的内容
   */
  fixAttributeQuotes (content, cdnUrl) {
    log.debug('修复属性引号问题...')

    let fixedContent = content

    // 修复没有结束引号的CDN URL
    // 匹配模式：href="https://cdn.example.com/path 后面跟着空格或其他属性
    const cdnDomain = cdnUrl.split('/')[2] // 提取域名
    const fixQuotesPattern = new RegExp(`(href|src)="(https://${cdnDomain}[^"\\s>]+)\\s`, 'g')

    fixedContent = fixedContent.replace(fixQuotesPattern, (match, attr, url) => {
      log.debug(`修复引号: ${attr}="${url}"`)
      return `${attr}="${url}" `
    })

    // 修复没有开始引号的情况
    const addQuotesPattern = new RegExp(`(href|src)=(https://${cdnDomain}[^\\s>]+)`, 'g')
    fixedContent = fixedContent.replace(addQuotesPattern, (match, attr, url) => {
      log.debug(`添加引号: ${attr}="${url}"`)
      return `${attr}="${url}"`
    })

    return fixedContent
  }

  /**
   * 添加跨域属性
   * @param {string} content - HTML 内容
   * @returns {string} 更新后的内容
   */
  addCrossOriginAttributes (content) {
    log.config('添加跨域属性...')

    let updatedContent = content
    let addedCount = 0

    // 为 script 标签添加 crossorigin 属性
    updatedContent = updatedContent.replace(
      /<script([^>]*src=[^>]*\.js[^>]*?)(?!\s+crossorigin)>/g,
      (match, attributes) => {
        addedCount++
        return `<script${attributes} crossorigin="anonymous">`
      }
    )

    // 为 link 标签添加 crossorigin 属性
    updatedContent = updatedContent.replace(
      /<link([^>]*href=[^>]*\.css[^>]*?)(?!\s+crossorigin)>/g,
      (match, attributes) => {
        addedCount++
        return `<link${attributes} crossorigin="anonymous">`
      }
    )

    log.success(`为 ${addedCount} 个标签添加了跨域属性`)
    return updatedContent
  }

  /**
   * 处理 DLL 文件引用
   * @param {string} content - HTML 内容
   * @param {string} cdnUrl - CDN URL
   * @returns {string} 更新后的内容
   */
  handleDllReferences (content, cdnUrl) {
    log.config('检查 DLL 文件引用...')

    // 检查 DLL 文件是否存在
    const dllExists = fs.existsSync(this.dllPath) && fs.existsSync(this.dllDistPath)

    if (!dllExists) {
      log.debug('DLL 文件不存在，跳过 DLL 引用处理')
      return content
    }

    // 检查是否已经包含 DLL 引用
    if (content.includes('vendor.dll.js')) {
      log.debug('HTML 中已包含 DLL 引用')
      return content
    }

    // 在 manifest.js 之前添加 DLL 引用
    const dllScript = `<script type="text/javascript" src="${cdnUrl}static/js/vendor.dll.js" crossorigin="anonymous"></script>`

    const updatedContent = content.replace(
      /(<script[^>]*src=[^>]*manifest[^>]*><\/script>)/,
      dllScript + '\n$1'
    )

    if (updatedContent !== content) {
      log.success('已添加 DLL 文件引用')
    } else {
      log.warn('未找到 manifest.js 引用，无法插入 DLL 引用')
    }

    return updatedContent
  }

  /**
   * 更新图片资源引用
   * @param {string} content - HTML 内容
   * @param {string} cdnUrl - CDN URL
   * @returns {string} 更新后的内容
   */
  updateImageReferences (content, cdnUrl) {
    log.config('更新图片资源引用...')

    const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'svg', 'webp', 'ico']
    let updatedContent = content
    let updateCount = 0

    imageExtensions.forEach(ext => {
      const pattern = new RegExp(`(src|href)="\/?static\/([^"]*\\.${ext})"`, 'g')
      const matches = updatedContent.match(pattern)

      if (matches) {
        updateCount += matches.length
        updatedContent = updatedContent.replace(pattern, `$1="${cdnUrl}static/$2"`)
      }
    })

    if (updateCount > 0) {
      log.success(`更新了 ${updateCount} 个图片资源引用`)
    }

    return updatedContent
  }

  /**
   * 更新字体资源引用
   * @param {string} content - HTML 内容
   * @param {string} cdnUrl - CDN URL
   * @returns {string} 更新后的内容
   */
  updateFontReferences (content, cdnUrl) {
    log.config('更新字体资源引用...')

    const fontExtensions = ['woff', 'woff2', 'ttf', 'eot', 'otf']
    let updatedContent = content
    let updateCount = 0

    fontExtensions.forEach(ext => {
      const pattern = new RegExp(`href="\/?static\/([^"]*\\.${ext})"`, 'g')
      const matches = updatedContent.match(pattern)

      if (matches) {
        updateCount += matches.length
        updatedContent = updatedContent.replace(pattern, `href="${cdnUrl}static/$1"`)
      }
    })

    if (updateCount > 0) {
      log.success(`更新了 ${updateCount} 个字体资源引用`)
    }

    return updatedContent
  }

  /**
   * 验证更新后的 HTML
   * @param {string} cdnUrl - CDN URL
   */
  validateUpdatedHtml (cdnUrl) {
    log.config('验证 HTML 更新结果...')

    try {
      const content = fs.readFileSync(this.indexPath, 'utf8')

      // 检查是否包含 CDN URL
      const cdnReferences = (content.match(new RegExp(cdnUrl.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g')) || []).length

      if (cdnReferences > 0) {
        log.success(`验证通过: 找到 ${cdnReferences} 个 CDN 引用`)
      } else {
        log.warn('验证警告: 未找到 CDN 引用')
      }

      // 检查跨域属性
      const crossOriginCount = (content.match(/crossorigin="anonymous"/g) || []).length
      log.debug(`跨域属性数量: ${crossOriginCount}`)
    } catch (error) {
      log.error('HTML 验证失败:', error.message)
    }
  }

  /**
   * 清理备份文件
   */
  cleanupBackup () {
    const backupPath = this.indexPath + '.backup'

    try {
      if (fs.existsSync(backupPath)) {
        fs.unlinkSync(backupPath)
        log.debug('已清理备份文件')
      }
    } catch (error) {
      log.warn('清理备份文件失败:', error.message)
    }
  }
}

module.exports = { HtmlUpdater }
