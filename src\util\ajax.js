import Axios from 'axios'
// import qs from 'qs'
import Cookies from 'js-cookie'
import constants from './constants'
import { Loading, Message } from 'element-ui'
import util from '../util/util'

const invalidToken = '40006'
const axios = Axios.create({})

axios.interceptors.request.use(config => {
  config.headers['token'] = Cookies.get('token') || ''
  let flab = Cookies.get('flab') ? Cookies.get('flab') : '0'
  let id = util.getSessionInfo('loginId')
  config.headers['flabNo'] = flab
  config.headers['user-id'] = id ? util.decryptBase64(id) : ''
  // config.headers['user-name'] = store.getters.getValue('userInfo').name || 'user'
  config.headers['Content-Type'] = 'application/from-data'
  if (config.method === 'post') {
    config.headers['Content-Type'] = 'application/json'
  }
  return config
})

axios.interceptors.response.use(res => {
  let result = res.data
  if (result.code === invalidToken) {
    util.clearLoginData()
    // 清除x-lims-token
    Cookies.remove('x-lims-token')
    if (window.location.pathname !== '/login') {
      Message({
        message: '登陆失效，请重新登陆',
        type: 'error',
        duration: 3000
      })
      window.location.href = constants.ITSM + '/login?redirectUrl=' + encodeURIComponent(window.location.href)
    }
    return res
  } else {
    return res
  }
})

export function myAjax ({
  url,
  baseURL = constants.JS_CONTEXT,
  data = {},
  loadingDom, // 加载loading的元素， 可以是class、id等
  loadingObject, // 参考element-ui, https://element.eleme.cn/#/zh-CN/component/loading
  method = 'post',
  timeout = 0,
  isCustomFormData = false,
  // responseType 默认是json
  // axios允许值 'arraybuffer', 'blob', 'document', 'json', 'text', 'stream'
  responseType = 'json',
  headers = {},
  showErrorMessageBox = true, // 是否非SUCCESS_CODE状态时直接报错
  errorMessageBoxUseHtml = false, // 这个提示是否用html格式
  isFormData = false, // 是不是formData，该方法只适用于post
  withCredentials = false, // 表示跨域请求时是否需要使用凭证
  onUploadProgress = null, // 上传进度函数
  onDownloadProgress = null, // 下载进度函数
  cancelToken = null // 取消ajax请求，
// 取消请参考 http://www.axios-js.com/zh-cn/docs/#%E5%8F%96%E6%B6%88
}) {
  let defaultHeaders = {
    ...headers
  }
  let loading
  if (loadingDom) {
    let obj = {}
    if (loadingObject && typeof loadingObject === 'object') obj = loadingObject
    loading = Loading.service({
      target: loadingDom,
      lock: true,
      ...obj
    })
  }
  let option = {
    url: url,
    method: method,
    baseURL: baseURL,
    timeout: timeout,
    responseType: responseType,
    withCredentials: withCredentials,
    headers: defaultHeaders,
    cancelToken: cancelToken
  }
  if (isCustomFormData) {
    option.data = data
  } else if (method === 'get') {
    option.params = data
  } else if (method === 'post' && !isFormData) {
    let areaList = util.getSessionInfo('currentLab') || []
    let options = {
      '1': 'PA0001',
      '2': 'PA0002',
      '3': 'PA0003',
      '4': 'PA0004',
      '5': 'PA0005'
    }
    areaList = areaList.map(v => {
      return options[v]
    })
    data.areaList = areaList
    // 这里的数据默认是对象，对象是需要进行序列化的，请根据后台实际情况修改
    option.data = JSON.stringify(data)
    option.transformRequest = [function (d) {
      // d = qs.stringify(d)
      return d
    }]
  } else if (isFormData) {
    // 这里默认你传的是普通的 plain object
    // 此处对plain object进行formData化
    let formData = new FormData()
    for (let k in data) {
      if (Array.isArray(data[k])) {
        data[k].forEach(v => {
          formData.append(k, v)
        })
      } else {
        formData.append(k, data[k])
      }
    }
    option.data = formData
  }
  if (onDownloadProgress) option.onDownloadProgress = onDownloadProgress
  if (onUploadProgress) option.onUploadProgress = onUploadProgress
  return axios(option).then(function (res) {
    // blob是需要获取文件名的，一般的文件名放到了响应头中
    if (res) {
      // 全局统一接口错误提示
      if (responseType !== 'blob' && res.data && res.data.code !== constants.SUCCESS_CODE && showErrorMessageBox) {
        Message({
          message: res.data.message,
          dangerouslyUseHTMLString: errorMessageBoxUseHtml,
          type: 'error',
          duration: 5000
        })
      }
      return responseType === 'blob' || responseType === 'arraybuffer' ? {data: res.data, headers: res.headers} : res.data
    }
  }).catch(function (err) {
    if (err.message !== '手动取消') {
      Message({
        message: err.message || err.error || '错误',
        type: 'error',
        showClose: true,
        duration: 0
      })
    }
    return Promise.reject(err)
  }).finally(() => {
    if (loadingDom) {
      loading.close()
    }
  })
}

export default {myAjax}
