<template>
  <el-dialog
    title="批量修改"
    append-to-body
    :visible.sync="visible"
    :close-on-click-modal="false"
    :before-close="handleClose"
    width="800px"
    @opened="handleOpen">
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      inline
      label-width="100px"
      class="demo-ruleForm">
<!--      <el-form-item prop="type">-->
<!--        <el-radio-group v-model="form.type">-->
<!--          <el-radio :label="1">包lane数</el-radio>-->
<!--          <el-radio :label="2">实际周期</el-radio>-->
<!--          <el-radio :label="3">标准周期</el-radio>-->
<!--        </el-radio-group>-->
<!--      </el-form-item>-->
      <div class="tips-wrapper">
        <i class="el-icon-warning-outline color"></i>
        请选择对已选择的{{ ids.length }}条样本数据进行修改
      </div>
      <el-form-item prop="laneTotal" label="包lane数">
        <el-input-number v-model="form.laneTotal" class="input-wrapper" size="mini" :min="0" :max="10"
                         controls-position="right" clearable placeholder="请输入包lane数"/>
      </el-form-item>
      <el-form-item  prop="actualCycle" label="实际周期">
        <el-input-number
          v-model="form.actualCycle"
          size="mini"
          class="input-wrapper"
          :min="0"
          :max="1000"
          :step="0.1"
          step-strictly controls-position="right" clearable placeholder="请输入实际周期"
          @input.native="changeInputPt($event,1)"/>
        天
      </el-form-item>
      <el-form-item  prop="standardCycle" label="标准周期">
        <el-input-number
          v-model="form.standardCycle"
          size="mini"
          class="input-wrapper"
          :min="0"
          :max="1000"
          :step="0.1"
          step-strictly controls-position="right" clearable placeholder="请输入标准周期"
          @input.native="changeInputPt($event,1)"/>
        天
      </el-form-item>
      <el-form-item prop="totalOutput" label="上机产出总量">
        <el-input-number
          v-model="form.totalOutput"
          size="mini"
          class="input-wrapper"
          :min="0"
          :max="2000"
          :step="0.01"
          step-strictly controls-position="right" clearable placeholder="请输入上机产出总量"
          @input.native="changeInputPt($event,2)"/>
      </el-form-item>
      <el-form-item prop="deliveryTime" label="交付时间">
        <el-date-picker
          v-model="form.deliveryTime"
          size="mini"
          type="datetime"
          placeholder="选择日期"
          format="yyyy-MM-dd HH:mm:ss"
          value-format="yyyy-MM-dd HH:mm:ss"
          clearable/>
      </el-form-item>
      <el-form-item prop="orderDataAmount" label="下单数据量">
        <el-input-number
          v-model="form.orderDataAmount"
          size="mini"
          class="input-wrapper"
          :min="0"
          :step="0.01"
          step-strictly controls-position="right" clearable placeholder="请输入上机产出总量"
          @input.native="changeInputPt($event,2)"/>
      </el-form-item>
      <el-form-item prop="deliveryProblem" label="交付问题">
        <el-input
          v-model="form.deliveryProblem"
          size="mini"
          type="textarea"
          :rows="4"
          style="width: 600px"
          class="input-wrapper"
          maxlength="200"
          show-word-limit
          clearable
          placeholder="请输入交付问题"/>
      </el-form-item>
    </el-form>

    <span slot="footer" class="dialog-footer">
      <!-- 对话框操作按钮 -->
      <el-button size="mini" @click="handleClose">取 消</el-button>
      <el-button :loading="loading" type="primary" size="mini" @click="handleConfirm">提  交</el-button>
    </span>
  </el-dialog>
</template>
<script>
import mixins from '../../../../../util/mixins'
import {awaitWrap} from '../../../../../util/util'
import {updateSampleMonitoringInfo} from '../../../../../api/dataMonitoringManagement/smapleMonitoringManagementApi'

export default {
  name: 'batchModifyingDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    ids: {
      type: Array,
      default: () => []
    },
    laneTotal: {
      type: String,
      default: null
    },
    actualCycle: {
      type: String,
      default: null
    },
    productName: {
      type: String,
      default: null
    }
  },
  computed: {
    typeName () {
      const typeNames = {
        1: '包lane',
        2: '实际周期',
        3: '标准周期'
      }
      return typeNames[this.form.type]
    }
  },
  data () {
    return {
      form: {
        laneTotal: undefined,
        actualCycle: undefined,
        standardCycle: undefined,
        totalOutput: undefined,
        deliveryTime: null,
        orderDataAmount: null,
        deliveryProblem: null
      },
      loading: false,
      rules: {
        orderDataAmount: {
          // 只能输入大于等于0的数字
          validator: (rule, value, callback) => {
            if (value <= 0) {
              callback(new Error('请输入大于等于0的数字'))
            } else {
              callback()
            }
          },
          trigger: 'change'
        }
      }
    }
  },
  methods: {
    handleOpen () {
      this.$refs.form.resetFields()
      this.form = {
        laneTotal: undefined,
        actualCycle: undefined,
        standardCycle: undefined,
        totalOutput: undefined,
        deliveryTime: null,
        orderDataAmount: undefined,
        deliveryProblem: undefined
      }
    },
    changeInputPt (e, count) {
      // count为保留小数的位数，0为取整。
      if (count === 0) {
        e.target.value = parseInt(e.target.value) || ''
        return
      }
      if ((e.target.value.indexOf('.') >= 0)) {
        e.target.value = e.target.value.substring(0, e.target.value.indexOf('.') + (count + 1))
      }
    },
    setParams () {
      return {
        fidList: this.ids,
        flaneTotal: this.form.laneTotal,
        fstandardCycle: this.form.standardCycle,
        fdeliveryTime: this.form.deliveryTime,
        factualCycle: this.form.actualCycle,
        foutputTotal: this.form.totalOutput,
        fdataSize: this.form.orderDataAmount,
        fdeliveryProblem: this.form.deliveryProblem
        // 使用映射函数根据 this.form.type 的值来添加特定的属性
      }
    },

    handleConfirm () {
      this.$refs.form.validate(async valid => {
        if (valid) {
          const params = this.setParams()
          this.loading = true
          const {res = {}} = await awaitWrap(updateSampleMonitoringInfo(params))
          if (res.code === this.SUCCESS_CODE) {
            this.$message({
              message: '修改成功',
              type: 'success'
            })
            this.$emit('dialogConfirmEvent')
            this.visible = false
          }
          this.loading = false
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
.tips-wrapper {
  display: flex;
  align-items: center;
  margin: 5px;

  .color {
    color: #409eff;
    margin-right: 5px;
  }
}

.input-wrapper {
  width: 220px;
  margin: 0 5px;
}
</style>
