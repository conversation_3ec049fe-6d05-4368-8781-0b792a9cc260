<template>
  <div style="transition: all .5s">
    <div class="search-form">
      <el-form size="mini" :model="form" label-width="80px" inline>
        <el-form-item label="样例编号">
          <el-input v-model="form.sampleNums" type="textarea" size="mini" clearable></el-input>
        </el-form-item>
      </el-form>
      <el-form v-if="showAdvanced" v-model.trim="form" label-width="100px" inline @keyup.enter.native="handleSearch">
        <el-form-item label="样例编号">
          <el-input v-model.trim="form.sampleNum" size="mini" clearable></el-input>
        </el-form-item>
        <el-form-item label="项目名称">
          <el-input v-model.trim="form.proName" size="mini" clearable></el-input>
        </el-form-item>
        <el-form-item label="返样状态">
          <el-select v-model.trim="form.returnStatusArr" :collapse-tags="true" size="mini" multiple clearable  @clear="handleSearch" >
            <el-option :key="item.code" :label="item.label" :value="item.value" v-for="item in returnStatus"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="返样类型">
          <el-select v-model="form.returnTypeArr" :collapse-tags="true" size="mini" multiple clearable @clear="handleSearch">
            <el-option :key="item.code" :label="item.label" :value="item.value" v-for="item in returnTypes"></el-option>
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="客户名称">
          <el-input v-model.trim="form.customerName" size="mini" class="form-content" clearable max-length="100"></el-input>
        </el-form-item> -->
        <el-form-item label="报告时间">
          <el-date-picker
            v-model="form.reportTime"
            value-format="yyyy-MM-dd"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            size="mini"
            class="date"
            placeholder="选择日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="是否已发报告">
          <el-select v-model="form.haveReport" :collapse-tags="true" size="mini" clearable @clear="handleSearch">
            <el-option :value="0" label="否"></el-option>
            <el-option :value="1" label="是"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-select v-model="form.searchTimeType" size="mini" placeholder="请选择" style="width: 100px;">
            <el-option :value="0" label="返样时间"></el-option>
            <el-option :value="1" label="返样申请时间"></el-option>
          </el-select>
          <el-date-picker
            v-model="form.returnTime"
            value-format="yyyy-MM-dd"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            size="mini"
            class="date"
            placeholder="选择日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label-width="100px" label="订单联系人2 ">
          <el-input v-model.trim="form.orderEntryPerson" size="mini" class="form-content" clearable maxlength="50"></el-input>
        </el-form-item>
      </el-form>
    </div>
    <div class="operate-btns-group">
      <el-dropdown v-if="$setAuthority('003010003', 'buttons') || $setAuthority('003009004', 'buttons')" style="margin-left: 10px" @command="handleCommandPicture">
        <el-button :loading="downloadFileLoading" type="primary" plain size="mini">
          图片处理<i class="el-icon-arrow-down el-icon--right"></i>
        </el-button>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item :command="0" v-if="$setAuthority('003010003', 'buttons')">图片上传</el-dropdown-item>
          <el-dropdown-item :command="1" v-if="$setAuthority('003010004', 'buttons')">图片拍摄</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <el-button v-if="$setAuthority('003010001', 'buttons')" type="primary" plain size="mini" style="margin-left: 10px" @click="importLogisticsVisible = true">导入物流信息</el-button>
      <el-button v-if="$setAuthority('003010002', 'buttons')" type="primary" plain size="mini" @click="handleShowReturnSampleFixApplicationDialog">修改申请</el-button>
      <el-dropdown v-if="$setAuthority('003010003', 'buttons') || $setAuthority('003009004', 'buttons')" style="margin-left: 10px" @command="handleCommand">
        <el-button :loading="downloadFileLoading" type="primary" plain size="mini">
          导出<i class="el-icon-arrow-down el-icon--right"></i>
        </el-button>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item :command="0" v-if="$setAuthority('003010003', 'buttons')">全部导出</el-dropdown-item>
          <el-dropdown-item :command="1" v-if="$setAuthority('003010004', 'buttons')">勾选导出</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <el-button v-if="$setAuthority('003010002', 'buttons')" type="primary" plain size="mini" style="margin-left: 10px" @click="handleRevocation">撤回</el-button>
      <el-button type="primary" plain size="mini" @click="handelShowAdvanced">高级查询</el-button>
      <el-button type="primary" plain size="mini" @click="handleSearch">查询</el-button>
      <el-button size="mini" plain @click="handleResetForm">重置</el-button>
    </div>
    <el-table
      ref="backTable"
      :data="tableData"
      style="width: 100%;"
      :height="tbHeight"
      class="table"
      @select="handleSelectTable"
      @select-all="handleSelectAll"
      @row-click="handleRowClick">
      <el-table-column type="selection" width="55"></el-table-column>
      <el-table-column label="序号" type="index" fixed="left"></el-table-column>
      <el-table-column width="100" label="样例标签" prop="isBmsModify">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.isBmsModify === 1"  style="margin-right: 5px;"  size="mini">修</el-tag>
          <el-tag v-if="scope.row.probeTag === 1"  style="margin-right: 5px;"  size="mini">院内</el-tag>
        </template>
      </el-table-column>
      <el-table-column width="180" label="样例编号" prop="sampleNum" show-overflow-tooltip>
        <template slot-scope="scope">
          <div>
            <el-popover
              popper-class="sample-popover-wrapper"
              placement="right"
              trigger="hover"
            >
              <i style="font-size: 24px; cursor: pointer" class="el-icon-circle-plus-outline" @click="() => handleCopyText(scope.row.sampleNum)"></i>
              <span slot="reference" class="highlight" @click="handleShowImg(scope.row.sampleNum)">{{scope.row.sampleNum}}</span>
            </el-popover>
          </div>

<!--          <el-button type="text" @click="handleShowImg(scope.row.sampleNum)">{{scope.row.sampleNum}}</el-button>-->
        </template>
      </el-table-column>
      <el-table-column prop="hasReported" label="是否已发报告" width="150" show-overflow-tooltip></el-table-column>
      <el-table-column prop="experimentStatusName" label="样例状态" width="150" show-overflow-tooltip></el-table-column>
      <el-table-column prop="proName" label="项目名称" width="180" show-overflow-tooltip></el-table-column>
      <el-table-column prop="returnStatusName" label="返样状态" width="120" show-overflow-tooltip></el-table-column>
      <el-table-column prop="returnTypeName" label="返样类型" width="120" show-overflow-tooltip></el-table-column>
      <el-table-column prop="recipient" label="返样收件人" width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <desensitization :info="scope.row.recipient" type="name"></desensitization>
        </template>
      </el-table-column>
      <el-table-column prop="completeAddr" label="返样收件地址" min-width="180" show-overflow-tooltip></el-table-column>
      <el-table-column prop="recipientTel" label="返样收件电话" min-width="180" show-overflow-tooltip>
        <template slot-scope="scope">
          <desensitization :info="scope.row.recipientTel" type="phone"></desensitization>
        </template>
      </el-table-column>
      <el-table-column prop="returnSampleExplain" label="返样要求" min-width="180" show-overflow-tooltip></el-table-column>
      <el-table-column prop="returnCourierNum" label="返样物流单号" min-width="180" show-overflow-tooltip></el-table-column>
      <el-table-column prop="returnTime" label="返样时间" min-width="180" show-overflow-tooltip></el-table-column>
      <el-table-column prop="returnOperator" label="寄件人" min-width="180" show-overflow-tooltip></el-table-column>
      <el-table-column prop="applyTime" label="返样申请时间" min-width="180" show-overflow-tooltip></el-table-column>
      <el-table-column prop="sampleConfirmTime" label="到样时间" min-width="180" show-overflow-tooltip></el-table-column>
      <el-table-column prop="reportTime" label="报告时间" min-width="180" show-overflow-tooltip></el-table-column>
      <el-table-column prop="salesManName" label="订单联系人1" min-width="180" show-overflow-tooltip></el-table-column>
      <el-table-column prop="actotumName" label="订单联系人2 " min-width="180" show-overflow-tooltip></el-table-column>
      <!-- <el-table-column prop="hospital" label="送检医生" min-width="180" show-overflow-tooltip></el-table-column> -->
      <!-- <el-table-column prop="customerName" label="客户名称" width="160" show-overflow-tooltip></el-table-column> -->
      <!-- <el-table-column prop="doctor" label="送检单位" min-width="180" show-overflow-tooltip></el-table-column> -->
      <el-table-column prop="updator" label="修改人" min-width="180" show-overflow-tooltip></el-table-column>
      <el-table-column prop="updateTime" label="修改时间" min-width="180" show-overflow-tooltip></el-table-column>
    </el-table>
    <el-pagination
      :page-sizes="pageSizes"
      :page-size="pageSize"
      :current-page.sync="currentPage"
      :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper, slot"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange">
      <button @click="handleRefresh"><icon-svg icon-class="icon-refresh" /></button>
    </el-pagination>
    <return-sample-import-logistics-dialog
      :pvisible.sync="importLogisticsVisible"
      @dialogConfirmEvent="getData"/>
    <return-sample-fix-application-dialog
      :pvisible.sync="returnSampleFixApplicationVisible"
      :id="id"
      :experiment-status-name="experimentStatusName"
      @dialogConfirmEvent="getData"/>
    <return-sample-pic-dialog :pvisible.sync="returnSamplePicVisible" @dialogConfirmEvent="handleSearch"/>
    <return-sample-photo-dialog :pvisible.sync="returnSamplePhoteVisible" :fsample-code="fsampleCode"/>
    <sample-pic-detail-dialog :pvisible.sync="samplePicDetailVisible" :fsample-code="fsampleCode"/>
    <return-sample-cancel-dialog :pvisible.sync="cancelVisible" :return-id="returnId" @dialogConfirmEvent="handleSearch"/>
  </div>
</template>

<script>

// import xx form 'xxx'
import util from '../../../util/util'
import mixins from '../../../util/mixins'
import returnSampleImportLogisticsDialog from './returnSampleImportLogisticsDialog'
import returnSampleFixApplicationDialog from './returnSampleFixApplicationDialog'
import returnSamplePicDialog from './returnSamplePicDialog'
import returnSamplePhotoDialog from './returnSamplePhotoDialog'
import samplePicDetailDialog from './samplePicDetailDialog'
import returnSampleCancelDialog from './returnSampleCancelDialog'

export default {
  name: `backSampleManagement`,
  mixins: [mixins.tablePaginationCommonData],
  components: {
    returnSampleFixApplicationDialog, // 修改返样申请
    returnSampleImportLogisticsDialog, // 导入物流信息
    returnSamplePicDialog,
    returnSamplePhotoDialog,
    samplePicDetailDialog,
    returnSampleCancelDialog
  },
  mounted () {
    this.$_setTbHeight(74 + 40 + 42 + 32, '.search-form')
    this.getData()
    this.getReturnStatus()
    this.getReturnTypes()
  },
  data () {
    return {
      id: null,
      showAdvanced: false,
      experimentStatusName: null,
      form: {
        searchTimeType: '',
        sampleNum: '', // 样本号
        returnStatusArr: [], // 返样状态
        returnTypeArr: [], // 返样类型
        haveReport: '', // 是否已发报告
        reportTime: '', // 报告时间
        returnTime: '', // 返样时间
        sampleStatus: '', // 样本状态
        customerName: '',
        orderEntryPerson: '',
        proName: '' // 产品名称
      },
      submitForm: {
        sampleNum: '', // 样本号
        returnStatusArr: [], // 返样状态
        returnTypeArr: [], // 返样类型
        haveReport: '', // 是否已发报告
        reportTime: '', // 报告时间
        returnTime: '', // 返样时间
        sampleStatus: '', // 样本状态
        customerName: '',
        orderEntryPerson: '',
        proName: '' // 产品名称
      },
      returnStatus: [],
      returnTypes: [],
      downloadFileLoading: false,
      importLogisticsVisible: false,
      pageSizes: [100, 150, 200],
      pageSize: 100,
      returnSampleFixApplicationVisible: false,
      progressFilterOptions: [
        {text: '待领取', value: '待领取'},
        {text: '处理中', value: '处理中'},
        {text: '已完成', value: '已完成'},
        {text: '部分完成', value: '部分完成'},
        {text: '驳回', value: '驳回'},
        {text: '已返样', value: '已返样'}
      ],
      sampleStatusFilterValues: [],
      selectedRows: new Map(),
      backSampleApplicationDialogVisible: false,
      backSampleApplicationDialogTableData: [],
      returnSamplePicVisible: false,
      returnSamplePhoteVisible: false,
      fsampleCode: '',
      returnId: '',
      samplePicDetailVisible: false,
      cancelVisible: false
    }
  },
  methods: {
    handelShowAdvanced () {
      this.showAdvanced = !this.showAdvanced
      this.$_setTbHeight(74 + 40 + 42 + 32, '.search-form')
    },
    handleCopyText (newClipText) {
      util.copyText(newClipText).then(() => {
        this.$message.success('复制成功')
      }).catch(() => {
        this.$message.success('复制失败')
      })
    },
    getData () {
      let reportTime = this.submitForm.reportTime || []
      let returnTime = this.submitForm.returnTime || []
      this.$ajax({
        url: '/sample/return/page_sample_return',
        data: {
          params: {
            sampleNum: this.submitForm.sampleNum,
            returnStatusArr: this.submitForm.returnStatusArr,
            returnTypeArr: this.submitForm.returnTypeArr,
            haveReport: this.submitForm.haveReport,
            reportTimeStart: reportTime[0],
            reportTimeEnd: reportTime[1],
            returnTimeStart: this.submitForm.searchTimeType === 0 ? returnTime[0] : '',
            returnTimeEnd: this.submitForm.searchTimeType === 0 ? returnTime[1] : '',
            applyTimeStart: this.submitForm.searchTimeType === 1 ? returnTime[0] : '',
            applyTimeEnd: this.submitForm.searchTimeType === 1 ? returnTime[1] : '',
            sampleStatus: this.submitForm.sampleStatus,
            proName: this.submitForm.proName,
            customerName: this.submitForm.customerName,
            sampleNums: this.form.sampleNums && util.setGroupData(this.form.sampleNums, ',').split(','),
            orderEntryPerson: this.submitForm.orderEntryPerson
          },
          page: {
            current: this.currentPage,
            size: this.pageSize
          }
        },
        loadingDom: '.table'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.totalPage = res.data.total
          let rows = res.data.rows || []
          this.selectedRows.clear()
          this.tableData = []
          rows.forEach(v => {
            let item = {
              id: v.fid,
              isBmsModify: v.fisBmsModify,
              probeTag: v.fprobeTag,
              sampleNum: v.sampleNum,
              originNum: v.originNum,
              salesManName: v.salesManName,
              actotumName: v.actotumName,
              reportTime: v.reportTime,
              hasReported: v.reportTime ? '是' : '否',
              experimentStatusName: v.experimentStatusName, // 实验状态名
              proName: v.proName,
              returnStatusName: v.returnStatusName,
              returnTime: v.returnTime,
              returnTypeName: v.returnTypeName,
              recipient: v.recipient, // 收件人
              recipientTel: v.recipientTel,
              province: v.province,
              city: v.city,
              region: v.region,
              contactAddr: v.contactAddr,
              completeAddr: v.completeAddr,
              recipientInfoSummary: v.recipientInfoSummary, // * 收件信息汇总 （收件人&收件地址&联系电话）
              returnSampleExplain: v.returnSampleExplain, // 返样要求
              sampleConfirmTime: v.sampleConfirmTime, // 到样时间
              returnOperator: v.returnOperator,
              returnCourierNum: v.returnCourierNum, // 快递单号
              hospital: v.hospital,
              applyTime: v.applyTime, // 返样申请时间
              doctor: v.doctor,
              updator: v.updator,
              updateTime: v.updateTime,
              customerName: v.customerName,
              orderEntryPerson: v.factotumName
            }
            item.realData = v
            util.setDefaultEmptyValueForObject(item)
            this.tableData.push(item)
          })
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 点击查询
    handleSearch () {
      this.currentPage = 1
      this.submitForm = {...this.form}
      this.getData()
    },
    // 重置表单
    handleResetForm () {
      this.form = {
        sampleNum: '', // 样本号
        returnStatusArr: [], // 返样状态
        returnTypeArr: [], // 返样类型
        sampleNums: null,
        haveReport: '', // 是否已发报告
        reportTime: '', // 报告时间
        returnTime: '', // 返样时间
        sampleStatus: '', // 样本状态
        customerName: '',
        orderEntryPerson: '',
        proName: '' // 产品名称
      }
      this.handleSearch()
    },
    // 获取返样状态列表
    getReturnStatus () {
      this.$ajax({
        url: '/sample/return/list_sample_return_status',
        methods: 'get'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          let data = res.data || []
          this.returnStatus = []
          data.forEach(item => {
            this.returnStatus.push({
              label: item.name,
              value: item.code
            })
          })
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 获取返样类型列表
    getReturnTypes () {
      this.$ajax({
        url: '/sample/return/list_sample_return_type',
        methods: 'get'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          let data = res.data || []
          this.returnTypes = []
          data.forEach(item => {
            this.returnTypes.push({
              label: item.name,
              value: item.code
            })
          })
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 显示返样申请
    handleShowReturnSampleFixApplicationDialog () {
      if (this.selectedRows.size !== 1) {
        this.$message.error('请选择一条数据')
        return
      }
      let row = [...this.selectedRows.values()][0]
      // 1.只能修改返样状态为“待返样”且“是否已发报告”为“否”的数据，否则提示：“只能修改返样状态为“待返样”且“是否已发报告”为“否”的记录”
      if (row.returnStatusName !== '待返样' || row.hasReported !== '否') {
        this.$message.error('只能修改返样状态为“待返样”且“是否已发报告”为“否”的记录')
        return
      }
      this.experimentStatusName = row.experimentStatusName
      this.id = row.id
      this.returnSampleFixApplicationVisible = true
    },
    // 导入图片
    handleCommandPicture (command) {
      command === 0 ? this.handleUpload() : this.handlePhoto()
    },
    // 上传照片
    handleUpload () {
      this.returnSamplePicVisible = true
    },
    // 拍摄照片
    handlePhoto () {
      if (this.selectedRows.size > 1) {
        this.$message.error('最多选择一条数据')
        return
      }
      this.fsampleCode = ''
      if (this.selectedRows.size === 1) {
        let rows = [...this.selectedRows.values()][0] || {}
        this.fsampleCode = rows.sampleNum
      }
      console.log(this.fsampleCode)
      this.returnSamplePhoteVisible = true
    },
    // 查看图片详情
    handleShowImg (sampleCode) {
      this.fsampleCode = sampleCode
      this.samplePicDetailVisible = true
    },
    // 撤回
    handleRevocation () {
      if (this.selectedRows.size !== 1) {
        this.$message.error('请选择一条数据')
        return
      }
      let rows = [...this.selectedRows.values()][0] || {}

      if (rows.returnStatusName !== '已返样') {
        this.$message.error('请选择已返样的数据')
        return
      }
      this.returnId = rows.id
      this.cancelVisible = true
    },
    // 选择导出类型
    handleCommand (command) {
      command === 0 ? this.handleExportAll() : this.handleExport()
    },
    // 导出全部
    handleExportAll () {
      if (this.totalPage > 1000) {
        this.$message.error('当前数据过多，请选择报告时间范围或增加筛选条件')
        return
      }
      let reportTime = this.submitForm.reportTime || []
      let returnTime = this.submitForm.returnTime || []
      this.downloadFileLoading = true
      this.$ajax({
        url: '/sample/return/download_all_sample_return_info',
        data: {
          sampleNum: this.submitForm.sampleNum,
          returnStatusArr: this.submitForm.returnStatusArr,
          returnTypeArr: this.submitForm.returnTypeArr,
          haveReport: this.submitForm.haveReport,
          reportTimeStart: reportTime[0],
          reportTimeEnd: reportTime[1],
          returnTimeStart: returnTime[0],
          returnTimeEnd: returnTime[1],
          sampleStatus: this.submitForm.sampleStatus,
          proName: this.submitForm.proName
        },
        responseType: 'blob'
      }).then(res => {
        util.readBlob(res.data).then(() => {
          util.downloadFile(res)
        }).catch(msg => {
          this.$message.error(msg)
        })
      }).finally(() => {
        this.downloadFileLoading = false
      })
    },
    // 导出所选
    handleExport () {
      if (this.selectedRows.size === 0) {
        this.$message.error('请选择数据')
        return
      }
      let rows = [...this.selectedRows.values()].map(item => item.realData)
      this.downloadFileLoading = true
      this.$ajax({
        url: '/sample/return/download_sample_return_info',
        data: rows,
        responseType: 'blob'
      }).then(res => {
        util.readBlob(res.data).then(() => {
          util.downloadFile(res)
        }).catch(msg => {
          this.$message.error(msg)
        })
      }).finally(() => {
        this.downloadFileLoading = false
      })
    },
    // 点击行
    handleRowClick (row, c) {
      this.$refs.backTable.toggleRowSelection(row, !this.selectedRows.has(row.id))
      this.handleSelectTable(undefined, row)
    },
    // 选中行
    handleSelectTable (selection, row) {
      this.selectedRows.has(row.id) ? this.selectedRows.delete(row.id) : this.selectedRows.set(row.id, row)
    },
    // 全选
    handleSelectAll (selection) {
      this.selectedRows.clear()
      selection.forEach((row) => {
        this.selectedRows.set(row.id, row)
      })
    }
  }
}
</script>

<style scoped lang="scss">
.form-container{
  //display: flex;
  //justify-content: space-between;
  margin: 20px 0 0 0;
  border-bottom: 1px solid #ccc;
  .btn-group {
    display: inline-block;
    margin-top: 5px;
  }
}
.highlight {
  color: #409EFF;
  cursor: pointer;
}
>>> .el-popover {
  min-width: 32px !important;
}
</style>
