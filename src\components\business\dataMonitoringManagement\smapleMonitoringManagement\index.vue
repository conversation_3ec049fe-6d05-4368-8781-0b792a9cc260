<template>
  <div class="wrapper">
    <div class="search-form">
      <el-form
        ref="form"
        :model="form"
        :inline="true"
        label-width="100px"
        size="mini"
        label-suffix=":"
        @keyup.enter.native="handleSearch">
        <el-form-item label="质控流转状态">
          <el-select v-model="form.qcStatus" class="form-width" collapse-tags multiple clearable placeholder="请选择">
            <el-option
              v-for="(key, value) in dataStatus"
              :key="key"
              :label="key"
              :value="value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="审核状态">
          <el-select v-model="form.auditStatus" class="form-width" clearable placeholder="请选择">
            <el-option
              v-for="(key, value) in auditStatusConfig"
              :key="key"
              :label="key"
              :value="value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="订单编号">
          <el-input v-model.trim="form.orderCodes" class="form-width" clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="吉因加编号">
          <el-input v-model.trim="form.geneCodes" class="form-width" clearable placeholder="请输入"></el-input>
        </el-form-item>
      </el-form>
    </div>
    <search-params-dialog
      :pvisible.sync="searchDialogVisible"
      @reset="handleReset"
      @search="handleSearch">
      <el-form
        ref="form"
        class="params-search-form"
        :model="form"
        label-width="80px"
        label-suffix=":"
        size="small"
        label-position="top"
        inline>
        <el-form-item label="交付状态">
          <el-select v-model="form.deliverStatus" class="form-width" collapse-tags multiple clearable
                     placeholder="请选择">
            <el-option
              v-for="(key, value) in deliverStatusConfig"
              :key="key"
              :label="key"
              :value="value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="样本当前进度">
          <el-select v-model="form.currentStep" class="form-width" collapse-tags multiple clearable
                     placeholder="请选择">
            <el-option
              v-for="(key, value) in stepConfig"
              :key="key"
              :label="key"
              :value="value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="质控流转状态">
          <el-select v-model="form.qcStatus" class="form-width" collapse-tags multiple clearable placeholder="请选择">
            <el-option
              v-for="(key, value) in dataStatus"
              :key="key"
              :label="key"
              :value="value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="审核状态">
          <el-select v-model="form.auditStatus" class="form-width" clearable placeholder="请选择">
            <el-option
              v-for="(key, value) in auditStatusConfig"
              :key="key"
              :label="key"
              :value="value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="到样时间">
          <el-date-picker
            v-model.trim="form.confirmTime"
            class="form-long-width"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd HH:mm:ss"
            :default-time="['00:00:00', '23:59:59']"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="吉因加编号">
          <el-input v-model.trim="form.geneCode" class="form-width" clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="原始样本编号">
          <el-input v-model.trim="form.oldSampleName" class="form-width" clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="订单编号">
          <el-input v-model.trim="form.orderCode" class="form-width" clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="订单类型 ">
          <el-select v-model="form.orderType" class="form-width" collapse-tags multiple clearable placeholder="请选择">
            <el-option
              v-for="(key, value) in orderTypeConfig"
              :key="key"
              :label="key"
              :value="value">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="项目编码">
          <el-input v-model.trim="form.projectCode" class="form-width" clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="项目名称">
          <el-input v-model.trim="form.projectName" class="form-width" clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="样本类型">
          <el-input v-model.trim="form.sampleType" class="form-width" clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="产品名称">
          <el-input v-model.trim="form.productName" class="form-width" clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="产品类型">
          <el-input v-model.trim="form.productType" class="form-width" clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="文库类型">
          <el-input v-model.trim="form.libraryType" class="form-width" clearable placeholder="请输入"></el-input>
        </el-form-item>

        <el-form-item label="是否备份">
          <el-select v-model="form.isBackUp" class="form-width" collapse-tags clearable placeholder="请选择">
            <el-option
              v-for="(key, value) in IS_BACK_UP_CONFIG"
              :key="key"
              :label="key"
              :value="value">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="实验环节">
          <el-select v-model="form.experimentalLink" class="form-width" collapse-tags multiple clearable
                     placeholder="请选择">
            <el-option
              v-for="(key, value) in experimentalLinkConfig"
              :key="key"
              :label="key"
              :value="value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="质控报告时间">
          <el-date-picker
            v-model.trim="form.qcReportTime"
            class="form-long-width"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd HH:mm:ss"
            :default-time="['00:00:00', '23:59:59']"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="启动时间">
          <el-date-picker
            v-model.trim="form.enableTime"
            class="form-long-width"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd HH:mm:ss"
            :default-time="['00:00:00', '23:59:59']"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="建库完成时间">
          <el-date-picker
            v-model.trim="form.buildLibFinishTime"
            class="form-long-width"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd HH:mm:ss"
            :default-time="['00:00:00', '23:59:59']"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="上机时间">
          <el-date-picker
            v-model.trim="form.sequenceTime"
            class="form-long-width"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd HH:mm:ss"
            :default-time="['00:00:00', '23:59:59']"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="下机时间">
          <el-date-picker
            v-model.trim="form.deplaneTime"
            class="form-long-width"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd HH:mm:ss"
            :default-time="['00:00:00', '23:59:59']"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="交付时间">
          <el-date-picker
            v-model.trim="form.deliveryTime"
            class="form-long-width"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd HH:mm:ss"
            :default-time="['00:00:00', '23:59:59']"
          ></el-date-picker>
        </el-form-item>

        <el-form-item label="是否延期">
          <el-select v-model="form.isDelay" class="form-width" clearable placeholder="请选择">
            <el-option
              v-for="(key, value) in BOOLEAN_CONFIG"
              :key="key"
              :label="key"
              :value="value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否加测">
          <el-select v-model="form.isAddTesting" class="form-width" clearable placeholder="请选择">
            <el-option
              v-for="(value, key) in addTestingOrderOptions"
              :key="key"
              :label="value"
              :value="key">
            </el-option>
          </el-select>
        </el-form-item>
<!--        <el-form-item v-if="+form.isAddTesting === 1" label="是否加测合并">-->
<!--          <el-radio-group v-model="form.fisMergeAddTesting" size="mini" class="form-width">-->
<!--            <el-radio-button :label="1">加测合并</el-radio-button>-->
<!--            <el-radio-button :label="0">加测不合并</el-radio-button>-->
<!--          </el-radio-group>-->
<!--        </el-form-item>-->
        <el-form-item label="自动流转预警">
          <el-select v-model="form.flowWarning" class="form-width" clearable placeholder="请选择">
            <el-option
              v-for="(key, value) in warningConfig"
              :key="key"
              :label="key"
              :value="value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="非空值">
          <el-select v-model="form.notNullFieldList" class="form-width" clearable multiple colapse-tags placeholder="请选择">
            <el-option
              v-for="(key, value) in notNullFields"
              :key="key"
              :label="key"
              :value="value">
            </el-option>
          </el-select>
        </el-form-item>
        <!-- 空值 -->
        <el-form-item label="空值">
          <el-select v-model="form.nullFieldList" class="form-width" clearable multiple colapse-tags placeholder="请选择">
            <el-option
              v-for="(key, value) in nullFields"
              :key="key"
              :label="key"
              :value="value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="交付问题">
          <el-input v-model.trim="form.fdeliveryProblem" class="form-width" clearable placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="数据备注">
          <el-input v-model.trim="form.fdataNote" class="form-width" clearable placeholder="请输入"></el-input>
        </el-form-item>
      </el-form>
    </search-params-dialog>
    <div class="flex-wrapper">
      <div class="operate-btns-group">
        <template v-if="$setAuthority('023001001', 'buttons')">
          <el-button v-if="downloadLoading" type="primary" size="small" disabled><i class="el-icon-loading"></i>
            正在导出
          </el-button>
          <el-dropdown v-else @command="(command) => handleCommand(command, 1)" style="margin: 0 10px;">
            <el-button type="primary" size="mini">数据导出<i class="el-icon-arrow-down el-icon--right"></i></el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item :command="1">按条件导出</el-dropdown-item>
              <el-dropdown-item :command="2">按选中导出</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
        <el-button v-if="$setAuthority('023001002', 'buttons')" type="primary" plain size="mini"
                   @click="handleFixStatus">修改状态
        </el-button>
        <el-button v-if="$setAuthority('023001003', 'buttons')" type="primary" plain size="mini"
                   @click="handleEditData">编辑数据量
        </el-button>
        <el-button v-if="$setAuthority('023001004', 'buttons')" type="primary" plain size="mini"
                   @click="handleBatchModify">批量修改
        </el-button>
        <el-button type="primary" plain size="mini" @click="handleAudit">审核</el-button>
        <template v-if="$setAuthority('023001001', 'buttons')">
          <el-button v-if="downloadLoading" type="primary" size="small" disabled><i class="el-icon-loading"></i>
            正在导出
          </el-button>
          <el-dropdown v-else @command="(command) => handleCommand(command, 2)" style="margin: 0 10px;">
            <el-button type="primary" plain size="mini">执行表导出<i class="el-icon-arrow-down el-icon--right"></i></el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item :command="1">按条件导出</el-dropdown-item>
              <el-dropdown-item :command="2">按选中导出</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
        <el-button type="primary" plain size="mini" @click="handleSearch">查询</el-button>
        <el-button size="mini" @click="handleReset">重置</el-button>
        <el-badge :value="searchParamsKeyNum" :hidden="searchParamsKeyNum === 0" class="item" type="primary">
          <el-button size="mini" plain type="primary" @click="searchDialogVisible = true">更多查询</el-button>
        </el-badge>
      </div>

      <div class="setting-wrapper">
        <el-popover
          v-model="visible"
          placement="bottom-start"
          title="自定义列"
          width="360"
          trigger="manual"
        >
          <div>
            <el-table
              ref="table"
              :data="tableData"
              border
              size="mini"
              height="300px"
              style="width: 100%"
              row-key="id"
              @select="handleSelectTable"
              @row-click="handleRowClick"
              @select-all="handleSelectAll"
            >
              <el-table-column type="index" width="50" show-overflow-tooltip>
                <template slot-scope="scope">
                  <icon-svg icon-class="icon-tuozhuai" class="handle"></icon-svg>
                </template>
              </el-table-column>
              <el-table-column type="selection" min-width="55" show-overflow-tooltip :selectable="checkSelectable"></el-table-column>
              <el-table-column
                prop="title"
                label="列名"
                key="title"
                min-width="200">
              </el-table-column>
            </el-table>
            <div class="operate-wrapper">
              <div class="operate-item" @click="handleResetTableConfig">恢复默认</div>
              <div class="operate-item" @click="handleCancel">关闭</div>
              <div class="operate-item" @click="handleSave">保存</div>
            </div>
          </div>
          <div slot="reference" @click="handleShowSetting">
            <el-card :body-style="{ padding: '5px'}" shadow="hover">
              <icon-svg style="font-size: 20px" icon-class="icon-shezhi"></icon-svg>
            </el-card>
          </div>
        </el-popover>
      </div>
    </div>

    <div class="content">
      <vxe-table
        ref="tableRef"
        border
        resizable
        :height="tbHeight"
        keep-source
        class="table"
        :data="tableList"
        size="mini"
        :auto-resize="true"
        :edit-rules="validRules"
        :valid-config="{msgMode: 'full'}"
        :checkbox-config="{trigger: 'row'}"
        show-overflow="tooltip"
        :edit-config="{trigger: 'click', mode: 'cell',showStatus: true}"
        :scroll-y="{enabled: true}"
        @checkbox-all="handleSelectChange"
        @checkbox-change="handleSelectChange"
        @edit-closed="handleSaveNote"
      >
        <vxe-column fixed="left" type="checkbox" width="50"></vxe-column>
        <vxe-table-column fixed="left" type="seq" title="序号" width="60"></vxe-table-column>
        <vxe-table-column fixed="left" field="geneCode" title="吉因加编号" width="120">
          <template slot-scope="{ row }">
            <span :style="`color: ${row.fisMergeAddTestingColor}`" :key="row.fid">{{ row.geneCode }}</span>
          </template>
        </vxe-table-column>
        <vxe-table-column fixed="left" field="oldSampleName" title="原始样本编号" width="120"></vxe-table-column>
        <template v-for="(item, index) in tableConfig">
          <vxe-table-column v-if="item.isShow" :key="index" :field="item.field" :formatter="item.formater"
                            :title="item.title"
                            :min-width="item.width" :edit-render="item.render">
            <template slot-scope="{ row }">
              <div v-if="item.field === 'deliverStatusText'">
                <span v-if="row.deliverStatus !==0 && !row.deliverStatus">-</span>
                <span v-else :style="row.deliverStatus === 2 ? 'color: #51C86B' :  'color: #747474'">{{
                    row.deliverStatusText
                  }}</span>
              </div>
              <div v-else-if="item.field === 'qcStatusText'">
                <span v-if="row.qcStatus !==0 && !row.qcStatus">-</span>
                <span v-else
                      :style="row.qcStatus === 1 ? 'color: #51C86B' : row.qcStatus === 2 ? 'color: #D95A6A' : 'color: #747474'">{{
                    row.qcStatusText
                  }}</span>
              </div>
              <div v-else-if="item.field === 'orderCode'">
                <span class="link" @click="handleToOrderDetail(row)">{{ row.orderCode }}</span>
              </div>
              <div v-else-if="isTimeField(item.field, row)" style="width: 100%">
                <el-tooltip v-if="isTimeField(item.field, row).length > 1" placement="top">
                  <div slot="content">
                    <div v-for="item in isTimeField(item.field, row)" class="time-tips-item" :key="item">{{ item }}
                    </div>
                  </div>
                  <div class="link table-item" size="mini">{{ row[item.field] }}</div>
                </el-tooltip>
                <div v-else class="link table-item">
                  {{ row[item.field] }}
                </div>
              </div>
              <div v-else>
                <div style="width: 100%">
                  <tooltips :txt-info="row[item.field]"></tooltips>
                </div>
              </div>
            </template>
          </vxe-table-column>
        </template>
      </vxe-table>
      <div style="display: flex; align-items: center;font-size: 13px;">
        <el-pagination
          :page-sizes="pageSizes"
          :page-size="pageSize"
          :current-page.sync="currentPage"
          :total="totalPage"
          layout="total, sizes, prev, pager, next, jumper, slot"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange">
          <button @click="handleRefresh">
            <icon-svg icon-class="icon-refresh"/>
          </button>
        </el-pagination>
      </div>
    </div>
    <fix-status-dialog
      :pvisible.sync="fixStatusVisible"
      :ids="ids"
      :deliver-status="deliverStatus"
      @dialogConfirmEvent="getData"/>
    <edit-data-dialog
      :pvisible.sync="editDataVisible" :ids="ids" :order-code="orderCode" :order-type="orderType"
      @dialogConfirmEvent="getData"></edit-data-dialog>
    <batch-modifying-dialog
      :pvisible.sync="batchModifyVisible" :ids="ids" :lane-total="laneTotal"
      :actual-cycle="actualCycle"
      :product-name="productName"
      @dialogConfirmEvent="getData"></batch-modifying-dialog>
    <audit-dialog
      ref="auditDialog"
      :pvisible.sync="auditDialogVisible"
      :selected-data="selectedAuditData"
      @dialogConfirmEvent="getData"
     ></audit-dialog>
  </div>
</template>

<script>
import mixins from '../../../../util/mixins'
import {tableConfig} from './tabelConfig'
import util, {awaitWrap, deepCopy, downloadFile, readBlob, setGroupData} from '../../../../util/util'
import FixStatusDialog from './components/fixStatusDialog'
import EditDataDialog from './components/editDataDialog'
import Sortable from 'sortablejs'
import IconSvg from '../../../common/iconSvg.vue'
import BatchModifyingDialog from './components/batchModifyingDialog.vue'
import AuditDialog from './components/auditDialog.vue'
import {
  exportExecutionTable,
  exportSampleMonitoring,
  getSampleMonitoringList,
  updateSampleMonitoringInfo
} from '../../../../api/dataMonitoringManagement/smapleMonitoringManagementApi'
import {
  BOOLEAN_CONFIG,
  dataFormating,
  dataStatus,
  deliverStatusConfig,
  experimentalLinkConfig,
  IS_BACK_UP_CONFIG, notNullFields,
  nullFields,
  orderTypeConfig,
  stepConfig,
  warningConfig,
  auditStatusConfig
} from './dataFormate'

export default {
  name: 'index',
  mixins: [mixins.tablePaginationCommonData],
  components: {BatchModifyingDialog, IconSvg, EditDataDialog, FixStatusDialog, AuditDialog},
  mounted () {
    this.$_setTbHeight(74 + 40 + 42 + 32, '.search-form')
    this.handleSearch()
  },
  computed: {
    dataStatus () {
      return dataStatus
    },
    orderTypeConfig () {
      return orderTypeConfig
    },
    BOOLEAN_CONFIG () {
      return BOOLEAN_CONFIG
    },
    warningConfig () {
      return warningConfig
    },
    experimentalLinkConfig () {
      return experimentalLinkConfig
    },
    IS_BACK_UP_CONFIG () {
      return IS_BACK_UP_CONFIG
    },
    stepConfig () {
      return stepConfig
    },
    deliverStatusConfig () {
      return deliverStatusConfig
    },
    notNullFields () {
      return notNullFields
    },
    auditStatusConfig () {
      return auditStatusConfig
    },
    nullFields () {
      return nullFields
    }
  },
  data () {
    return {
      // 控制弹出框的可见性，默认为隐藏
      visible: false,
      // 控制搜索对话框的可见性，默认为隐藏
      searchDialogVisible: false,
      // 控制下载Excel时的加载状态，默认为隐藏
      downloadLoading: false,
      // 控制修复状态对话框的可见性，默认为隐藏
      fixStatusVisible: false,
      // 控制编辑数据对话框的可见性，默认为隐藏
      editDataVisible: false,
      // 控制批量修改对话框的可见性，默认为隐藏
      batchModifyVisible: false,
      // 控制审核对话框的可见性，默认为隐藏
      auditDialogVisible: false,
      // 选中的审核数据
      selectedAuditData: [],
      // 选中项的ID数组，默认为空数组
      ids: [],
      // 交付状态，默认为空
      deliverStatus: null,
      // 订单编号，默认为空
      orderCode: null,
      // 订单类型，默认为空
      orderType: null,
      productName: null,
      // 表单数据对象，默认为空对象
      form: {},
      formSubmit: {},
      // 包lane数
      laneTotal: null,
      // 实际周期
      actualCycle: null,
      // 分页大小数组，默认为100、200、500
      pageSizes: [100, 200, 500],
      pageSize: 100,
      addTestingOrderOptions: {
        0: '否',
        1: '是',
        2: '加测合并',
        3: '加测不合并'
      },
      // 表配置对象，由外部定义
      tableConfig: JSON.parse(localStorage.getItem('sampleModifyTableConfig')) || tableConfig,
      // 表数据数组，默认为空数组
      tableData: [],
      // 表列表数组，默认为空数组
      tableList: [],
      // 表单验证规则对象，默认为空对象
      validRules: {
      }
    }
  },
  methods: {
    /**
     * 设置查询参数。
     * 此函数用于根据表单提交的信息，生成并返回一个包含各种查询条件的对象。
     * 这些条件包括时间范围、项目信息、样本信息、订单信息等，用于精确查询项目进度和状态。
     *
     * @returns {Object} 返回一个对象，其中包含了所有的查询参数。
     */
    setParams () {
      const formSubmit = this.formSubmit
      // 初始化时间范围数组为空，如果存在则使用之
      const timeRanges = {
        fconfirmTime: formSubmit.confirmTime || [],
        fqcReportTime: formSubmit.qcReportTime || [],
        fenableTime: formSubmit.enableTime || [],
        fbuildLibFinishTime: formSubmit.buildLibFinishTime || [],
        fsequenceTime: formSubmit.sequenceTime || [],
        fdeplaneTime: formSubmit.deplaneTime || [],
        fdeliveryTime: formSubmit.deliveryTime || []
      }
      const resultMap = {
        0: [0, null],
        1: [1, null],
        2: [1, 1],
        3: [1, 0]
      }
      const result = resultMap[this.formSubmit.isAddTesting]
      if (result && result.length === 2) {
        this.formSubmit.isAddTesting = result[0]
        this.formSubmit.fisMergeAddTesting = result[1]
      }

      // 构建并返回查询参数对象
      return {
        // 处理列表数据和单个值的数据
        ...Object.entries({
          fqcStatusList: formSubmit.qcStatus,
          forderTypeList: formSubmit.orderType,
          fdeliverStatusList: formSubmit.deliverStatus,
          fcurrentStepList: formSubmit.currentStep,
          fexperimentalLinkList: formSubmit.experimentalLink,
          fgeneCodeList: setGroupData(formSubmit.geneCodes, ',', false),
          forderCodeList: setGroupData(formSubmit.orderCodes, ',', false),
          fgeneCode: formSubmit.geneCode,
          foldSampleName: formSubmit.oldSampleName,
          forderCode: formSubmit.orderCode,
          fprojectName: formSubmit.projectName,
          fsampleType: formSubmit.sampleType,
          fprojectCode: formSubmit.projectCode,
          fisMergeAddTesting: formSubmit.fisMergeAddTesting,
          fproductName: formSubmit.productName,
          flibraryType: formSubmit.libraryType,
          fisBackUp: formSubmit.isBackUp,
          fisDelay: formSubmit.isDelay,
          notNullFieldList: formSubmit.notNullFieldList,
          nullFieldList: formSubmit.nullFieldList,
          fdeliveryProblem: formSubmit.fdeliveryProblem,
          fdataNote: formSubmit.fdataNote,
          fisAddTesting: formSubmit.isAddTesting,
          fflowWarning: formSubmit.flowWarning,
          fauditStatus: formSubmit.auditStatus
        }).reduce((acc, [key, value]) => ({...acc, [key]: value}), {}),
        // 处理时间范围
        ...Object.entries(timeRanges).reduce((acc, [key, value]) => {
          const [start, end] = value
          return {...acc, [`${key}Start`]: start, [`${key}End`]: end}
        }, {})
      }
    },
    // 判断是否是时间字段
    isTimeField (field, row) {
      const timeFields = ['qcReportTime', 'enableTime', 'buildLibFinishTime', 'sequenceTime', 'deplaneTime', 'deliveryTime', 'taskCode', 'analysisProcessName', 'analysisType', 'analysisSubmitTime', 'analysisFinishTime', 'analysisDeliveryTime']
      const time = row[field] || ''
      if (time === '-') {
        return false
      }
      if (timeFields.includes(field)) {
        return time.split(',') || []
      }
      return false
    },
    checkSelectable (row) {
      return !['吉因加编号', '原始样本编号', '样本当前进度', '到样时间'].includes(row.title)
    },
    // 点击行
    handleRowClick (row, c) {
      this.handleSelectTable(undefined, row)
    },
    // 选中行
    handleSelectTable (selection, row) {
      if (!this.checkSelectable(row)) return
      if (this.pin) {
        this.shiftSelect(row)
      } else {
        this.startPoint = undefined// 清空多选起点
        this.endPoint = undefined// 清空多选终点
        this.selectedRows.has(row.id)
          ? this.selectedRows.delete(row.id)
          : this.selectedRows.set(row.id, row)
      }
      this.handleEchoSelect()
    },
    // 全选
    handleSelectAll (selection) {
      this.handleDelCurrentDataMap()
      selection.forEach((row) => {
        this.selectedRows.set(row.id, row)
      })
      this.selectedRowsSize = this.selectedRows.size
    },
    // 查看 type 1编辑 2 只读
    handleToOrderDetail (row) {
      console.log(row.orderId, row.orderCode, row.orderType)
      this.$store.commit({
        type: 'old/setValue',
        category: 'libraryOperatingData',
        libraryOperatingData: {
          type: 2, // type 1编辑 2 只读
          orderId: row.orderId,
          status: 2,
          code: row.orderCode,
          name: 'lims'
        }
      })
      let path = ''
      if (row.orderType === 1) path = '/business/subpage/technologyService/entryIlluminaLibraryOrder'
      if (row.orderType === 2) path = '/business/subpage/technologyService/entryMGILibraryOrder'
      if (row.orderType === 3) path = '/business/subpage/technologyService/entryTissueOrder'
      if (path) util.openNewPage(path)
    },
    /**
     * 处理搜索操作。
     * 重置当前页码并调用获取数据的方法，以便加载新搜索结果。
     */
    handleSearch () {
      // 深拷贝表单提交的数据，确保不直接修改原数据
      this.formSubmit = deepCopy(this.form)
      this.currentPage = 1
      this.getData()
    },
    /**
     * 处理重置操作。
     * 将表单数据重置为初始状态，并调用搜索方法以刷新列表。
     */
    handleReset () {
      this.form = this.$options.data().form
      this.handleSearch()
    },
    /**
     * 异步获取样本监控列表的数据。
     *
     * 使用设置的参数进行请求，并对请求结果进行处理，将处理后的数据赋值给列表。
     * 使用awaitWrap封装请求以处理加载状态。
     */
    async getData () {
      const params = {
        ...this.setParams(), // 分页信息
        pageVO: {
          currentPage: this.currentPage,
          pageSize: this.pageSize
        }
      }
      const {res} = await awaitWrap(getSampleMonitoringList(params, {
        loadingDom: '.table'
      }))
      if (res && res.code === this.SUCCESS_CODE) {
        this.totalPage = res.data.total
        this.tableList = dataFormating(res.data.records)
        const $table = this.$refs.tableRef
        if ($table) await $table.loadData(this.tableList)
      }
    },
    setUpdateParams (field, cellValue, row) {
      // 使用映射代替 if-else 逻辑，提高代码可读性
      const paramMapper = {
        'deliveryProblem': {fdeliveryProblem: cellValue},
        'dataNote': {fdataNote: cellValue}
      }
      const params = {
        fidList: [row.fid]
      }
      // 使用映射函数根据 this.form.type 的值来添加特定的属性
      return Object.assign(params, {...paramMapper[field]})
    },
    async handleSaveNote ({row, column}) {
      let xTable = this.$refs.tableRef
      let field = column.property
      let cellValue = row[field]
      // 判断单元格值是否被修改
      if (xTable.isUpdateByRow(row, field)) {
        // 判断字符长度
        if (cellValue && cellValue.length > 200) {
          this.$message.error('长度不能超过200, 请检查')
          await this.getData()
          return
        }
        const params = this.setUpdateParams(field, cellValue, row)
        const {res = {}} = await awaitWrap(updateSampleMonitoringInfo(params))
        if (res.code === this.SUCCESS_CODE) {
          this.$message({
            message: '修改成功',
            type: 'success'
          })
          // 局部更新单元格为已保存状态
          await this.$refs.tableRef.reloadRow(row, null, field)
        } else {
          await this.getData()
        }
      }
    },
    // 选择导出类型
    handleCommand (command, type) {
      command === 1 ? this.handleExportAll(type) : this.handleExport(type)
    },
    async downloadExport (res) {
      if (res) {
        const {err} = await awaitWrap(readBlob(res.data))
        err ? this.$message.error(err) : downloadFile(res)
      }
      this.downloadLoading = false
    },
    // 按条件导出
    async handleExportAll (type) {
      const params = this.setParams()
      await this.$confirm('是否确认导出查询数据？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      this.downloadLoading = true
      if (type === 1) {
        const {res} = await awaitWrap(exportSampleMonitoring(params))
        await this.downloadExport(res)
      } else {
        const {res} = await awaitWrap(exportExecutionTable(params))
        await this.downloadExport(res)
      }
    },
    // 导出所选
    async handleExport (type) {
      let selectRecords = this.$refs.tableRef.getCheckboxRecords()
      if (selectRecords.length === 0) {
        this.$message.error('请选择数据')
        return
      }
      let rowsId = selectRecords.map(item => item.fid)
      await this.$confirm('是否确认导出选中数据？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      this.downloadLoading = true
      if (type === 1) {
        const {res} = await awaitWrap(exportSampleMonitoring({
          fidList: rowsId
        }))
        await this.downloadExport(res)
      } else {
        const {res} = await awaitWrap(exportExecutionTable({
          fidList: rowsId
        }))
        await this.downloadExport(res)
      }
    },
    // 修改状态
    handleFixStatus () {
      console.time('sort')
      let selectRecords = this.$refs.tableRef.getCheckboxRecords()
      if (selectRecords.length === 0) {
        this.$message.error('请选择数据')
        return
      }
      let deliverStatus = selectRecords[0].deliverStatus
      // 点击生产进度监控，选择一条或多条交付状态相同数据进行修改
      if (selectRecords.length > 1) {
        let isSame = selectRecords.every(item => item.deliverStatus === deliverStatus)
        if (!isSame) {
          this.$message.error('请选择同一交付状态的数据')
          return
        }
      }
      this.deliverStatus = deliverStatus
      this.ids = selectRecords.map(item => item.fid)
      this.fixStatusVisible = true
      console.timeEnd('sort')
    },

    /**
     * 审核功能
     */
    handleAudit () {
      const selectRecords = this.$refs.tableRef.getCheckboxRecords()

      // 1. 验证是否选择了数据
      if (selectRecords.length === 0) {
        this.$message.error('请选择要审核的数据')
        return
      }

      // 2. 如果是多选，验证审核状态是否相同
      if (selectRecords.length > 1) {
        const firstAuditStatus = selectRecords[0].auditStatus
        const allSameStatus = selectRecords.every(item => item.auditStatus === firstAuditStatus)

        if (!allSameStatus) {
          this.$message.error('请选择审核状态相同的数据')
          return
        }
      }

      // 3. 设置选中的数据并显示审核对话框
      this.selectedAuditData = selectRecords
      this.auditDialogVisible = true
    },

    // 操作验证 => 选择同一订单的数据
    operateVerify (needSample = true) {
      let selectRecords = this.$refs.tableRef.getCheckboxRecords() || []
      // 获取当前选中的样本数据
      if (selectRecords.length === 0) {
        this.$message.error('请选择数据')
        return false
      }
      const orderCode = selectRecords[0].orderCode
      // 判断是否是同一订单编号
      if (selectRecords.length > 1) {
        let isSame = selectRecords.every(item => item.orderCode === orderCode)
        if (!isSame && needSample) {
          this.$message.error('请选择同一订单编号的数据')
          return false
        }
      }
      return true
    },
    // 编辑数据量
    handleEditData () {
      // 获取当前选中的样本数据
      let selectRecords = this.$refs.tableRef.getCheckboxRecords() || []
      if (!this.operateVerify()) return
      this.orderCode = selectRecords[0].orderCode
      this.ids = selectRecords.map(item => item.fid)
      this.orderType = selectRecords[0].orderType
      this.editDataVisible = true
    },
    // 批量修改
    handleBatchModify () {
      // 获取当前选中的样本数据
      let selectRecords = this.$refs.tableRef.getCheckboxRecords() || []
      if (!this.operateVerify(false)) return
      this.ids = selectRecords.map(item => item.fid)
      // 回显数据
      this.laneTotal = selectRecords[0].realData.laneTotal
      this.actualCycle = selectRecords[0].realData.actualCycle
      this.productName = selectRecords[0].realData.productName
      this.batchModifyVisible = true
    },
    handleSelectChange () {
      let selectRecords = this.$refs.tableRef.getCheckboxRecords()
      this.selectedRowsSize = selectRecords.length
    },
    // 恢复默认表格配置
    handleResetTableConfig () {
      this.tableConfig = tableConfig
      localStorage.setItem('sampleModifyTableConfig', JSON.stringify(this.tableConfig))
      this.visible = false
    },
    // 拖拽排序
    async initSort () {
      await this.$nextTick()
      const el = document.querySelectorAll('.el-table__body-wrapper > table > tbody')[0]
      // 根据具体需求配置options配置项
      Sortable.create(el, {
        handle: '.handle', // handle's class
        onEnd: (evt) => { // 监听拖动结束事件
          try {
            // 交换元素的逻辑，避免直接使用splice，以提高性能
            const temp = this.tableData[evt.oldIndex]
            this.tableData[evt.oldIndex] = this.tableData[evt.newIndex]
            this.tableData[evt.newIndex] = temp
          } catch (error) {
            console.error('Error during sorting:', error)
            // 可以进一步处理异常，例如回滚操作、显示错误信息等
          }
        }
      })
    },
    // 保存表格配置
    handleSave () {
      this.tableConfig = this.tableData.map(item => {
        if (item.isCustomerField) {
          item.isShow = !!this.selectedRows.has(item.id)
        }
        return item
      })
      localStorage.setItem('sampleModifyTableConfig', JSON.stringify(this.tableConfig))
      this.visible = false
    },
    // 取消表格配置
    handleCancel () {
      this.visible = false
    },
    // 显示表格配置
    handleShowSetting () {
      this.initSort()
      this.visible = !this.visible
      // 回显选中的列
      this.tableData = this.tableConfig.filter(item => item.isCustomerField)
      this.tableData.forEach(item => {
        if (item.isShow) {
          this.selectedRows.set(item.id, item)
        }
      })
      this.handleEchoSelect()
    }
  }
}
</script>

<style scoped lang="scss">
.wrapper {
  width: 100%;

  .btn-group {
    margin-bottom: 10px;
  }
}

.flex-wrapper {
  display: flex;
  justify-content: space-between;
}

.setting-wrapper {
  height: 32px;
}

.operate-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  text-align: center;
  margin-top: 12px;

  .operate-item {
    flex: 1;
    cursor: pointer;
  }

  .operate-item:hover {
    color: #409EFF;
  }
}

.time-tips-item {
  border-bottom: 1px solid #ebeef5;
  padding: 5px 0;
}

.time-tips-item:last-child {
  border-bottom: none;
}

.table-item {
  width: 100%;
  // 单行省略
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
/deep/ .vxe-body--column {
  padding: 0 !important;
}
</style>
