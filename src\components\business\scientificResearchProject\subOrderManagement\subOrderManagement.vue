<template>
  <div>
    <!--操作按钮区-->
    <div class="flex search-form">
      <!--操作按钮区-->
      <div class="operate-btns-group">
        <el-button v-if="$setAuthority('017003001', 'buttons')" size="mini" type="primary" @click="handleReport">生成报告</el-button>
      </div>
      <!--查询区-->
      <div>
        <div class="flex">
          <el-select v-model.trim="searchType" style="width: 144px!important" size="mini" clearable placeholder="请选择">
            <el-option
              :key="item.value"
              :label="item.label"
              :value="item.value"
              v-for="item in optionsList">
            </el-option>
          </el-select>
          <el-select
            v-model.trim="searchValue"
            v-if="showSelect"
            style="width: 256px"
            collapse-tags
            multiple filterable clearable size="mini">
            <el-option
              :key="index"
              :label="item.label"
              :value="item.value"
              v-for="(item, index) in orderTypes"></el-option>
          </el-select>
          <el-input
            v-model.trim="searchValue"
            :disabled="!searchType"
            v-if="showInput"
            size="mini"
            style="width: 256px"
            placeholder="请输入"
            clearable
            @keyup.enter.native="handleSearch()"></el-input>
          <div class="btn">
            <el-button size="mini" type="primary" @click="handleSearch()">查询</el-button>
            <el-button size="mini" type="primary" @click="handleReset">重置</el-button>
            <el-button size="mini" type="primary" @click="handleAdvancedReport">高级查询</el-button>
          </div>
        </div>
      </div>
    </div>

    <!--表格-->
    <div>
      <el-table
        ref="table"
        :data="tableData"
        :cell-style="handleRowStyle"
        class="table"
        size="mini"
        border
        style="width: 100%"
        height="calc(100vh - 74px - 40px - 42px - 32px)"
        @select="handleSelectTable"
        @row-click="handleRowClick"
        @select-all="handleSelectAll">
        <el-table-column type="selection" width="50"></el-table-column>
        <el-table-column type="index" label="序号" width="50"></el-table-column>
        <el-table-column prop="subOrderReportStatusText" label="报告状态" min-width="80" show-overflow-tooltip>
          <template slot-scope="scope">
            <span :class="scope.row.sunOrderReportStatusClass">{{scope.row.subOrderReportStatusText}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="reportNum" label="已有报告" min-width="80" show-overflow-tooltip></el-table-column>
        <el-table-column prop="sampleNumText" label="样本数量" min-width="80" show-overflow-tooltip></el-table-column>
        <el-table-column prop="projectCode" label="项目编号" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="projectName" label="项目名称" min-width="180" show-overflow-tooltip></el-table-column>
        <el-table-column prop="orderType" label="订单类型" min-width="140" show-overflow-tooltip>
        </el-table-column>
        <el-table-column prop="subOrderCode" label="子订单编号" min-width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="parentOrderCode" label="订单编号" min-width="140" show-overflow-tooltip></el-table-column>
        <el-table-column fixed="right" label="操作" min-width="60" show-overflow-tooltip>
          <template slot-scope="scope">
            <span v-if="$setAuthority('017003002', 'buttons')" class="link"
                  @click="handlePreview(scope.row.id)">查看</span>
          </template>
        </el-table-column>
      </el-table>
      <div style="display: flex; align-items: center;font-size: 13px;">
          <span style="color: deepskyblue;height: 28px;line-height: 28px;vertical-align: top;">
            当前选中 {{ selectedRowsSize }} 条记录
          </span>
        <el-pagination
          :page-sizes="pageSizes"
          :page-size="pageSize"
          :current-page.sync="currentPage"
          :total="totalPage"
          layout="total, sizes, prev, pager, next, jumper, slot"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange">
          <button @click="handleRefresh">
            <icon-svg icon-class="icon-refresh"/>
          </button>
        </el-pagination>
      </div>
    </div>

    <!--查询抽屉-->
    <search-dialog
      ref="searchDialog"
      :pvisible.sync="searchVisible"
      @dialogConfirmEvent="handleSearch"/>
    <!--报告生成-->
    <generate-report-dialog
      :pvisible.sync="generateReportVisible"
      :sub-order-id="subOrderId"
      :order-code="orderCode"
      :report-type="2"
      @dialogConfirmEvent="getData"/>
    <!--已有报告列表-->
    <reports-dialog
      :pvisible.sync="reportsVisible"
      :sub-order-id="subOrderId"
      @dialogConfirmEvent="getData"
    ></reports-dialog>
  </div>
</template>

<script>

import mixins from '../../../../util/mixins'
import searchDialog from './components/searchDialog'
import util from '../../../../util/util'
import GenerateReportDialog from '../components/generateReportDialog'
import ReportsDialog from './components/reportsDialog' // 查询抽屉

export default {
  mixins: [mixins.tablePaginationCommonData],
  components: {
    ReportsDialog,
    GenerateReportDialog,
    searchDialog
  },
  watch: {
    searchType (newValue) {
      this.searchValue = ''
      if (!newValue) {
        this.handleSearch()
      }
    }
  },
  computed: {
    showInput () {
      return ['', 'fprojectCode', 'forderCode'].includes(this.searchType)
    },
    showSelect () {
      return ['forderType'].includes(this.searchType)
    }
  },
  data () {
    return {
      selectedRows: new Map(),
      searchVisible: false,
      generateReportVisible: false,
      reportsVisible: false,
      subOrderId: null,
      orderCode: '',
      searchType: '',
      searchValue: '',
      advanceForm: {},
      submitForm: {},
      typeOptions: {
        1: 'illumina文库订单',
        2: 'MGI文库订单',
        3: '组织核酸样本订单',
        5: '单细胞订单'
      },
      orderTypes: [
        {
          label: 'illumina文库订单',
          value: 1
        },
        {
          label: 'MGI文库订单',
          value: 2
        },
        {
          label: '组织或核酸订单',
          value: 3
        }
      ],
      optionsList: [
        {
          label: '项目编号',
          value: 'fprojectCode'
        }, {
          label: '订单编号',
          value: 'forderCode'
        }, {
          label: '订单类型',
          value: 'forderType'
        }
      ],
      statusOptions: {
        0: {
          text: '未生成',
          class: 'not-generate'
        },
        1: {
          text: '审核通过',
          class: ''
        },
        10: {
          text: '已发送',
          class: ''
        },
        2: {
          text: '生成失败',
          class: 'fail-generate'
        },
        20: {
          text: '被驳回',
          class: 'fail-generate'
        },
        3: {
          text: '未生成',
          class: 'not-generate'
        },
        30: {
          text: '生成中',
          class: 'generate'
        },
        31: {
          text: '待审核',
          class: ''
        }
      },
      tableData: []
    }
  },
  methods: {
    // 生成报告
    // 1.仅允许在当前页面选中一条记录进行操作。
    // 2.所选记录的“报告状态”为“生成失败”或“被驳回”或“未生成”。
    handleReport () {
      if (this.selectedRows.size === 0) {
        this.$message.warning('未选择任何记录')
        return
      }
      if (this.selectedRows.size > 1) {
        this.$message.warning('请选择一个样本生产报告')
        return
      }
      let rows = [...this.selectedRows.values()] || []
      if (rows.some(v => [2, 20, 3].includes(v.sampleReportStatus))) {
        this.$message.error('仅允许选择样本报告状态为“生成失败”、“被驳回”、“未生成”的记录进行操作')
        return
      }
      let row = rows[0] || {}
      this.subOrderId = row.id
      this.orderCode = row.parentOrderCode
      this.generateReportVisible = true
    },
    handlePreview (id) {
      this.subOrderId = id
      this.reportsVisible = true
    },
    // 通过给每个单元格覆盖样式来取消鼠标经过样式
    handleRowStyle ({row, rowIndex}) {
      if (this.selectedRows.has(row.id)) {
        return {backgroundColor: '#c7e1ff !important', padding: 0, height: '24px'}
      }
      return {padding: 0, height: '24px'}
    },
    // 查询
    handleSearch (data) {
      if (data) {
        this.advanceForm = data
      } else {
        this.advanceForm = this.$refs.searchDialog.setParams()
      }
      this.submitForm = util.deepCopy(this.advanceForm)
      this.submitForm.searchKey = this.searchType
      this.submitForm.searchValue = this.searchValue
      if (this.submitForm.searchKey === 'forderType') {
        this.submitForm.searchValue = this.searchValue.join(',')
      }
      this.currentPage = 1
      this.clearMap()

      this.getData()
    },
    // 重置分别重置高级和简易查询
    handleReset () {
      this.searchType = ''
      this.searchValue = ''
      this.handleSearch()
    },
    // 高级查询
    handleAdvancedReport () {
      this.searchVisible = true
    },
    // 获取表格数据
    getData () {
      let data = util.getSessionInfo('currentLab') || []
      let options = {
        '1': 'PA0001',
        '2': 'PA0002',
        '3': 'PA0003',
        '4': 'PA0004'
      }
      data = data.map(v => {
        return options[v]
      })
      this.$ajax({
        url: '/order/report/get_sub_order_list',
        data: {
          areaList: data,
          pageVO: {
            currentPage: this.currentPage,
            pageSize: this.pageSize
          },
          ...util.deepCopy(this.submitForm)
        },
        loadingDom: '.table'
      }).then((res) => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.totalPage = res.data.total
          this.tableData = []
          let data = res.data.rows || []
          data.forEach((v) => {
            let subOrderReportStatus = this.statusOptions[v.fsubOrderReportStatus] || {
              text: '',
              class: ''
            }
            let item = {
              subOrderReportStatus: v.fsubOrderReportStatus,
              sunOrderReportStatusClass: subOrderReportStatus.class,
              subOrderReportStatusText: subOrderReportStatus.text,
              id: v.fsubOrderId,
              reportNum: v.freportNum,
              qcSampleNum: v.fqcSampleNum,
              sampleNum: v.fsampleNum,
              sampleNumText: v.fqcSampleNum + '/' + v.fsampleNum,
              projectCode: v.fprojectCode,
              projectName: v.fprojectName,
              orderType: this.typeOptions[v.forderType],
              subOrderCode: v.fsubOrderCode,
              parentOrderCode: v.fparentOrderCode
            }
            util.setDefaultEmptyValueForObject(item)
            this.tableData.push(item)
          })
          this.$nextTick(() => {
            this.handleEchoSelect()
          })
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.flex {
  display: flex;
  justify-content: space-between;
}
.buttonGroup{
  margin: 10px 0;
  height: 40px;
  line-height: 40px;
}
.btn {
  margin-left: 10px;
}
.not-generate {
  color: $color
}
.generate {
  color: $success-color
}
.fail-generate {
  color: $fail-color
}
</style>
