<template>
  <div class="page">
    <div class="search-form">
      <div>
        <el-form :model="form" inline size="mini">
          <el-form-item label="样例编号">
            <el-input v-model.trim="form.sampleCode" class="input-width" @keyup.enter.native="handleSearch"></el-input>
          </el-form-item>
          <el-form-item label="信息补录">
            <el-cascader v-model="form.infoSupplement" :show-all-levels="false" :options="infoSupplementOptions"
              :props="{ multiple: true }" collapse-tags clearable></el-cascader>
          </el-form-item>
          <el-form-item label="我的任务">
            <el-checkbox v-model="form.myTask"></el-checkbox>
          </el-form-item>
          <el-form-item>
            <el-select v-model="form.searchTimeType" placeholder="请选择" style="width: 100px;"
              @change="handleSearchTimeChange">
              <el-option label="到样时间" value="0"></el-option>
              <el-option label="补录时间" value="1"></el-option>
              <el-option label="审核时间" value="2"></el-option>
            </el-select>
            <el-date-picker v-model="form.times[0]" value-format="yyyy-MM-dd" type="date" size="mini" class="date"
              style="width: 150px" placeholder="选择日期">
            </el-date-picker>
            <span>--</span>
            <el-date-picker v-model="form.times[1]" type="date" value-format="yyyy-MM-dd" size="mini" class="date"
              style="width: 150px" placeholder="选择日期">
            </el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleChange">{{showAdvancedSearch ? '隐藏' : '高级'}}查询</el-button>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" size="mini" plain @click="handleSearch">查询</el-button>
            <el-button size="mini" plain @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
        <!--<div class="search-row">-->
        <!--<div>-->
        <!--<label>我的任务</label>-->
        <!--<el-checkbox v-model="form.myTask"></el-checkbox>-->
        <!--</div>-->
        <!--<div>-->
        <!--<label>信息补录</label>-->
        <!--<el-cascader-->
        <!--v-model="form.infoSupplement"-->
        <!--size="mini"-->
        <!--collapse-tags-->
        <!--:options="infoSupplementOptions"-->
        <!--:props="{ multiple: true }"-->
        <!--clearable></el-cascader>-->
        <!--</div>-->
        <!--<div>-->
        <!--<label>我的任务</label>-->
        <!--<el-checkbox v-model="form.myTask"></el-checkbox>-->
        <!--</div>-->
        <!--<div>-->
        <!--<el-button size="mini" @click="showAdvancedSearch = !showAdvancedSearch" type="primary">高级查询</el-button>-->
        <!--</div>-->
        <!--</div>-->
      </div>
    </div>
    <!--高级查询-->
    <el-collapse-transition>
      <div v-show="showAdvancedSearch" class="search" style="height: auto;">
        <div>
          <el-form :model="form" label-width="110px" inline size="mini">
            <el-row :gutter="10">
              <el-col :span="6">
                <el-form-item label="任务状态">
                  <el-select v-model="form.taskStatus" size="mini" class="input-width">
                    <el-option label="全部" value=""></el-option>
                    <el-option :value="0" label="未领取"></el-option>
                    <el-option :value="1" label="已领取"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="姓名">
                  <el-input v-model.trim="form.name" class="input-width"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="身份证/护照">
                  <el-input v-model.trim="form.idCard" class="input-width"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="流转方式">
                  <el-select v-model="form.researchClinical" class="input-width">
                    <!-- <el-option :key="k" :label="v" :value="k" v-for="(v, k) in researchClinicalType"></el-option> -->
                    <el-option label="全部" value="2"></el-option>
                    <el-option label="非自动" value="0"></el-option>
                    <el-option label="自动" value="1"></el-option>
                    <el-option label="科研A" value="3"></el-option>
                    <!-- <el-option label="科研B" value="4"></el-option> -->
                    <!--<el-option label="临床" :value="1"></el-option>-->
                    <!--<el-option label="科研" :value="2"></el-option>-->
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="6">
                <el-form-item label="样例编号(精确)">
                  <el-input v-model="form.sampleCodeAccurate" class="input-width"></el-input>
                </el-form-item>
              </el-col>
              <!-- <el-col :span="6">
                <el-form-item label="送检单位">
                  <el-input v-model.trim="form.unit" class="input-width"></el-input>
                </el-form-item>
              </el-col> -->
              <el-col :span="6">
                <el-form-item label="项目名称">
                  <el-input v-model.trim="form.projectName" class="input-width"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="审批号">
                  <el-input v-model.trim="form.approvalNum" class="input-width"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <!-- <el-col :span="6">
                <el-form-item label="送检医生">
                  <el-input v-model.trim="form.doctor" class="input-width"></el-input>
                </el-form-item>
              </el-col> -->
              <el-col :span="6">
                <el-form-item label="订单联系人1">
                  <el-input v-model.trim="form.seller" class="input-width"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="患者编号">
                  <el-input class="input-width" v-model.trim="form.patientCode"></el-input>
                </el-form-item>
              </el-col>
              <!-- <el-col :span="6">
                <el-form-item label="客户名称">
                  <el-input class="input-width" v-model.trim="form.customerName" maxlength="100"></el-input>
                </el-form-item>
              </el-col> -->
            </el-row>
            <el-row :gutter="10">
              <el-col :span="6">
                <el-form-item label="订单联系人2">
                  <el-input class="input-width" v-model.trim="form.orderEntryPerson" maxlength="50"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </div>
    </el-collapse-transition>
    <div class="content">
      <div class="operate-btns-group">
        <el-button v-if="$setAuthority('003004008', 'buttons')" type="primary" size="mini" plain
          @click="handleFastEntry">
          <icon-svg class="iconClass" icon-class="icon-luru"></icon-svg>快速录入</el-button>
        <el-button v-if="$setAuthority('003004001', 'buttons')" type="primary" size="mini" plain
          @click="handleRecieveTask">
          <icon-svg class="iconClass" icon-class="icon-start-copy"></icon-svg>任务领取</el-button>
        <el-button v-if="$setAuthority('003004002', 'buttons')" type="primary" size="mini" plain
          @click="handleInformationSupplement">
          <icon-svg class="iconClass" icon-class="icon-luru1"></icon-svg>信息补录</el-button>
        <el-button v-if="$setAuthority('003004003', 'buttons')" type="primary" size="mini" plain
          @click="handleInformationReview">
          <icon-svg class="iconClass" icon-class="icon-shenhe"></icon-svg>信息审核</el-button>
        <el-button v-if="$setAuthority('003004004', 'buttons')" type="primary" size="mini" plain
          @click="handleSuspiciousModification">
          <icon-svg class="iconClass" icon-class="icon-luru1"></icon-svg>存疑修改</el-button>
        <el-button v-if="$setAuthority('003004005', 'buttons')" type="primary" size="mini" plain
          @click="handleViewDetail">
          <icon-svg class="iconClass" icon-class="icon-mingxi"></icon-svg>查看明细</el-button>
        <el-button :loading="exportLoading" v-if="$setAuthority('003004010', 'buttons')" type="primary" size="mini"
          plain @click="handleExport">
          <icon-svg class="iconClass" icon-class="icon-Dzu"></icon-svg>导出{{exportLoading ? '中' : ''}}</el-button>
        <el-button v-if="$setAuthority('003004009', 'buttons')" type="primary" size="mini" plain @click="handleMerge">
          <icon-svg class="iconClass" icon-class="icon-hebing"></icon-svg>合并</el-button>
        <el-button v-if="$setAuthority('003004007', 'buttons')" type="primary" size="mini" plain
          @click="handleViewMicroscopyResult">
          <icon-svg class="iconClass" icon-class="icon-jieguo"></icon-svg>查看镜检结果</el-button>
        <el-button v-if="$setAuthority('003004006', 'buttons')" type="primary" size="mini" plain
          @click="handleShowCopyInfoDialog">
          <icon-svg class="iconClass" icon-class="icon-fuzhi"></icon-svg>信息复制</el-button>
        <el-button v-if="$setAuthority('003004011', 'buttons')" type="primary" size="mini" plain
          @click="handleShowCountDialog">
          <icon-svg class="iconClass" icon-class="icon-fuzhi"></icon-svg>统计</el-button>
        <el-button v-if="$setAuthority('003004014', 'buttons')" type="primary" size="mini" plain
          @click="handleAuditDialog">
          <icon-svg class="iconClass" icon-class="icon-fuzhi"></icon-svg>操作记录</el-button>
        <el-button v-if="$setAuthority('003004015', 'buttons')" type="primary" size="mini" plain
          @click="handleShowUnusualCountDialog">
          <icon-svg class="iconClass" icon-class="icon-fuzhi"></icon-svg>异常样本</el-button>
      </div>
      <div>
        <el-table ref="table" :data="tableData"
          :height="`calc(100vh - 74px - 40px - 41px - 42px - 32px - ${advancedSearchHeight}px)`"
          :cell-style="handleRowStyle" class="clinicalInfoTable" size="mini" border style="width: 100%"
          @select="handleSelectTable" @row-click="handleRowClick" @select-all="handleSelectAll"
          @filter-change="filterMethod">
          <el-table-column :selectable="checkedTableCanSelect" type="selection" width="45"
            fixed="left"></el-table-column>
          <el-table-column type="index" label="序号" width="50" fixed="left"></el-table-column>
          <el-table-column :filters="filterList" prop="sampleTag" label="样例标签" width="120" show-overflow-tooltip
            column-key="tag">
            <template slot-scope="scope">
              <template v-if="scope.row.sampleTag">
                <el-tag :key="item" v-for="item in scope.row.sampleTag" style="margin-right: 5px;"
                  size="small">{{item}}</el-tag>
                <el-tag v-if="scope.row.fisAddTag === 1" style="margin-right: 5px;" size="small">补</el-tag>
                <el-tag v-if="scope.row.fisTongTag === 1" style="margin-right: 5px;" size="small">同</el-tag>
                <el-tag v-if="scope.row.fisNote === 1" type="danger" style="margin-right: 5px;" size="small">注</el-tag>
                <el-tag v-if="scope.row.fisUpdateTag === 1" style="margin-right: 5px;" size="small">改</el-tag>
                <el-tag v-if="scope.row.fupdate === 1" style="margin-right: 5px;" size="small">更</el-tag>
                <el-tag v-if="scope.row.fisFollowUp === 1" style="margin-right: 5px;" size="small">随</el-tag>
                <el-tag v-if="scope.row.probeTag === 1" style="margin-right: 5px;" size="small">院内</el-tag>
                <el-tag v-if="scope.row.fisFollowUp === 2" style="margin-right: 5px;" type="danger"
                  size="small">访</el-tag>
                <el-tag v-if="scope.row.isName === 1" style="margin-right: 5px;" type="danger" size="small">名</el-tag>
                <el-tag v-if="scope.row.humanHeritageLabel === 1" style="margin-right: 5px;" size="small">遗</el-tag>
                <el-tag v-if="scope.row.ftatSpeedTip  === 1" type="danger" style="margin-right: 5px;"
                  size="small">速</el-tag>
                <el-tag v-if="scope.row.ftatSpeedTip === 0" type="success" style="margin-right: 5px;"
                  size="small">缓</el-tag>
                <el-tag v-if="scope.row.fisExpress === 1" type="warning" style="margin-right: 5px;"
                  size="small">急</el-tag>
              </template>
            </template>
          </el-table-column>
          <el-table-column prop="sampleStatusText" label="样例状态" width="100" show-overflow-tooltip></el-table-column>
          <el-table-column prop="testingLink" label="检测环节" width="80" show-overflow-tooltip></el-table-column>
          <el-table-column prop="taskStatusText" label="任务状态" width="70" show-overflow-tooltip></el-table-column>
          <el-table-column prop="fpatientId" label="Patient ID" width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="sampleCode" label="样例编号" width="90" show-overflow-tooltip></el-table-column>
          <el-table-column prop="name" label="姓名" width="80" show-overflow-tooltip>
            <template slot-scope="scope">
              <desensitization :info="scope.row.name" type="name"></desensitization>
            </template>
          </el-table-column>
          <el-table-column prop="infoText" label="信息补录" width="90" show-overflow-tooltip></el-table-column>
          <el-table-column prop="rejectNotes" label="驳回备注" width="100" show-overflow-tooltip></el-table-column>
          <!-- <el-table-column prop="researchClinicalText" label="流转方式" width="70" show-overflow-tooltip></el-table-column> -->
          <!-- <el-table-column prop="productCode" label="产品/项目编号" width="100" show-overflow-tooltip></el-table-column> -->
          <el-table-column prop="productName" label="项目名称" width="160" show-overflow-tooltip></el-table-column>
          <el-table-column prop="geneplusProjectCode" label="吉因加项目编号" width="160"
            show-overflow-tooltip></el-table-column>
          <!-- <el-table-column prop="customerName" label="客户名称" width="160" show-overflow-tooltip></el-table-column> -->
          <!-- <el-table-column prop="unit" label="送检单位" width="140" show-overflow-tooltip></el-table-column> -->
          <el-table-column prop="sampleTime" label="到样时间" min-width="140" show-overflow-tooltip></el-table-column>
          <el-table-column prop="cardType" label="证件类型" min-width="140" show-overflow-tooltip></el-table-column>
          <el-table-column prop="idCard" label="证件号" min-width="140" show-overflow-tooltip>
            <template slot-scope="scope">
              <desensitization :info="scope.row.idCard" type="idCard"></desensitization>
            </template>
          </el-table-column>
          <el-table-column label="订单联系人1" min-width="120" show-overflow-tooltip>
            <template slot-scope="scope">
              <template v-if="scope.row.salesType !== '-'">
                {{scope.row.sales}} / {{scope.row.salesType}}
              </template>
              <template v-else>
                {{scope.row.sales}}
              </template>
            </template>
          </el-table-column>
          <el-table-column prop="factotumName" label="订单联系人2" min-width="100" show-overflow-tooltip>
          </el-table-column>
          <el-table-column prop="phone" label="联系电话" min-width="100" show-overflow-tooltip>
            <template slot-scope="scope">
              <desensitization :info="scope.row.phone" type="phone"></desensitization>
            </template>
          </el-table-column>
          <!-- <el-table-column prop="doctor" label="送检医生" width="80" show-overflow-tooltip></el-table-column> -->
          <el-table-column prop="createTime" label="录入时间" min-width="140" show-overflow-tooltip></el-table-column>
          <el-table-column prop="preDeliveryTime" label="预交付时间" min-width="140" show-overflow-tooltip></el-table-column>
          <el-table-column :filters="reviewers" prop="makeUpPerson" label="补录人" min-width="80" show-overflow-tooltip
            column-key="makeUpPerson">
          </el-table-column>
          <el-table-column prop="makeUpTime" label="补录时间" min-width="140" show-overflow-tooltip></el-table-column>
          <el-table-column :filters="reviewers" prop="reviewer" label="审核人" min-width="80" show-overflow-tooltip
            column-key="reviewers"></el-table-column>
          <el-table-column prop="reviewTime" label="审核时间" min-width="180" show-overflow-tooltip></el-table-column>
          <el-table-column prop="doubtMan" label="存疑人" min-width="80" show-overflow-tooltip></el-table-column>
          <el-table-column prop="doubtTime" label="存疑时间" min-width="140" show-overflow-tooltip></el-table-column>
          <el-table-column prop="doubtRemark" label="存疑备注" min-width="140" show-overflow-tooltip></el-table-column>
          <el-table-column prop="exceptionNote" label="异常备注" min-width="140" show-overflow-tooltip></el-table-column>
          <el-table-column prop="patientId" label="患者编号" min-width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="productAreaText" label="生产片区" min-width="80" show-overflow-tooltip></el-table-column>
        </el-table>
        <el-pagination :page-sizes="pageSizes" :page-size="pageSize" :current-page.sync="currentPage" :total="totalPage"
          layout="total, sizes, prev, pager, next, jumper, slot" @size-change="handleSizeChange"
          @current-change="handleCurrentChange">
          <button @click="handleRefresh"><icon-svg icon-class="icon-refresh" /></button>
        </el-pagination>
      </div>
    </div>
    <view-microscopy-result-dialog :pvisible.sync="viewMicroscopyResultDialogData.visible"
      :sample-num="viewMicroscopyResultDialogData.sampleNum" />
    <copy-info-dialog :pvisible.sync="copyInfoDialogData.visible" :sample-num="copyInfoDialogData.sampleNum" />
    <count-dialog :pvisible.sync="countDialogData.visible" />
    <save-info-dialog :key="saveInfoDialogKey" :pvisible.sync="saveInfoDialogVisible" :ptype="saveInfoDialogData.type"
      :psample-basic-id="saveInfoDialogData.sampleBasicId" :psample-num="saveInfoDialogData.sampleNum"
      :is-follow-ups="saveInfoDialogData.fisFollowUp" :testing-link="saveInfoDialogData.testingLink"
      :sample-product-id="saveInfoDialogData.sampleProductId"
      @saveInfoDialogConfirmEvent="handleSaveInfoDialogConfirm"></save-info-dialog>
    <merge-dialog :pvisible.sync="mergeDialogData.visible" :table-data="mergeDialogData.tableData"
      @dialogConfirm="getData" />
    <fast-entry-dialog :pvisible.sync="fastEntryData.visible" @dialogConfirm="getData" />
    <audit-drawer :pvisible.sync="auditDrawerData.visible" :sample-basic-id="auditDrawerData.sampleBasicId" />
    <unusual-count-dialog :pvisible.sync="unusualCountData.visible"></unusual-count-dialog>
  </div>
</template>

<script>
import mixins from '../../../util/mixins'
import util from '../../../util/util'
import saveInfoDialog from './clinicalInfoManagementSaveInfoDialog'
import viewMicroscopyResultDialog from './clinicalInfoManagementViewMicroscopyResultDialog'
import copyInfoDialog from './clinicalInfoManagementCopyInfoDialog'
import mergeDialog from './clinicalInfoManagementMergeDialog'
import fastEntryDialog from './clinicalInfoManagementFastEntryDialog'
import countDialog from './countDialog' // 统计
import auditDrawer from './clinicalInfoAuditDrawer'
import UnusualCountDialog from './unusualCountDialog' // 审核记录
export default {
  name: 'patientInfoDetail',
  mixins: [mixins.tablePaginationCommonData],
  components: {
    UnusualCountDialog,
    saveInfoDialog,
    viewMicroscopyResultDialog,
    copyInfoDialog,
    mergeDialog,
    fastEntryDialog,
    countDialog,
    auditDrawer
  },
  mounted () {
    const startDate = new Date(new Date().setMonth(new Date().getMonth() - 1))
    this.form.times = [util.dateFormatter(startDate, false), util.dateFormatter(new Date(), false)]
    this.handleSearch()
    this.getReviewerList()
  },
  computed: {
    username () {
      return this.$store.getters.getValue('userInfo').name
    },
    emailPrefix () {
      return this.$store.getters.getValue('userInfo').emailPrefix
    }
  },
  data () {
    return {
      reviewers: [],
      filterList: [
        { text: '名', value: '名' },
        { text: '多', value: '多' },
        { text: '同', value: '同' },
        { text: '既', value: '既' },
        { text: '补', value: '补' },
        { text: '改', value: '改' },
        { text: '随', value: '随' },
        { text: '访', value: '访' },
        { text: '注', value: '注' },
        { text: '更', value: '更' },
        { text: '速', value: '速' },
        { text: '缓', value: '缓' },
        { text: '急', value: '急' }
        // { text: '院内', value: '院内' },
      ],
      selectedRows: new Map(),
      showAdvancedSearch: false, // 显示高级查询
      advancedSearchHeight: 0, // 高级查询高度
      viewMicroscopyResultDialogData: { // 查看镜检结果弹窗数据
        visible: false,
        sampleNum: ''
      },
      countDialogData: { // 信息补录统计弹窗数据
        visible: false
      },
      unusualCountData: { // 异常信息统计弹窗数据
        visible: false
      },
      copyInfoDialogData: { // 信息复制弹窗数据
        visible: false,
        sampleNum: ''
      },
      mergeDialogData: { // 合并弹窗数据
        visible: false,
        tableData: []
      },
      fastEntryData: { // 快速录入弹窗
        visible: false
      },
      auditDrawerData: { // 审核记录
        visible: false,
        sampleBasicId: 0
      },
      exportLoading: false,
      tableData: [],
      form: {
        sampleCode: '',
        infoSupplement: [[5, 0], [5, 1], [5, 2], [5, 3]],
        myTask: false,
        taskStatus: '',
        name: '',
        idCard: '',
        researchClinical: '1',
        sampleCodeAccurate: '',
        unit: '',
        projectName: '',
        approvalNum: '', // 审批号
        searchTimeType: '0', // 时间类型
        times: ['', ''],
        doctor: '',
        seller: '',
        patientCode: '',
        fisName: '',
        fisMultiTag: '',
        fisPastTag: '',
        fisAddTag: '',
        fisTongTag: '',
        fisUpdateTag: '',
        fisFollowUps: '',
        isTatFast: '',
        isTatSlow: '',
        isExpress: '',
        customerName: '',
        orderEntryPerson: ''
      },
      tableHeight: 0,
      formSubmit: {}, // 提交的form
      infoSupplementOptions: [
        {
          label: '全选',
          value: 5,
          children: [
            // {value: 1, label: '前端未补录'},
            {value: 0, label: '未补录'},
            {value: 1, label: '补录中'},
            {value: 2, label: '驳回'},
            {value: 3, label: '待审'},
            {value: 4, label: '审核通过'},
            {value: 6, label: '前端未补录'},
            {value: 7, label: '样本暂存'}
          ]
        }
      ],
      infoSupplement: {
        0: '未补录',
        1: '补录中',
        4: '审核通过',
        2: '驳回',
        3: '待审',
        7: '样本暂存'
      },
      taskStatusType: {
        0: '未领取',
        1: '已领取'
      },
      researchClinicalType: {
        2: '全部',
        0: '非自动',
        1: '自动',
        3: '科研A',
        4: '科研B'
      },
      productAreaType: {
        1: '北京',
        2: '深圳'
      },
      searchTimeType: 'sampleTime', // 高级查询的事件类型 到样时间：sampleTime 补录时间：makeUpTime 审核时间：reviewTime
      saveInfoDialogVisible: false,
      saveInfoDialogKey: 1, // 保存弹窗的key
      saveInfoDialogData: {
        type: 0,
        sampleBasicId: '',
        sampleNum: '',
        fisFollowUp: 3
      }
    }
  },
  methods: {
    checkedTableCanSelect (row) {
      return row.infoText !== '样本暂存'
    },
    handleChange () {
      this.showAdvancedSearch = !this.showAdvancedSearch
      this.showAdvancedSearch ? this.advancedSearchHeight = 191 : this.advancedSearchHeight = 0
    },
    // 打开审核记录抽屉组件
    handleAuditDialog () {
      if (this.selectedRows.size !== 1) {
        this.$message.error('请选择一行数据')
        return
      }
      let row = [...this.selectedRows.values()][0]
      this.auditDrawerData.visible = true
      this.auditDrawerData.sampleBasicId = row.id
    },
    // 表格过滤
    filterMethod (value) {
      if (value.hasOwnProperty('tag')) this.filterTags(value)
      if (value.hasOwnProperty('reviewers')) this.filterReviewer(value)
      if (value.hasOwnProperty('makeUpPerson')) this.filterMakeUpPerson(value)
    },
    // 标签过滤
    filterTags (value) {
      let followTags = []
      value['tag'].some(item => item === '名') ? this.form.fisName = 1 : this.form.fisName = 0
      value['tag'].some(item => item === '多') ? this.form.fisMultiTag = 1 : this.form.fisMultiTag = 0
      value['tag'].some(item => item === '既') ? this.form.fisPastTag = 1 : this.form.fisPastTag = 0
      value['tag'].some(item => item === '补') ? this.form.fisAddTag = 1 : this.form.fisAddTag = 0
      value['tag'].some(item => item === '同') ? this.form.fisTongTag = 1 : this.form.fisTongTag = 0
      value['tag'].some(item => item === '改') ? this.form.fisUpdateTag = 1 : this.form.fisUpdateTag = 0
      value['tag'].some(item => item === '注') ? this.form.fisNote = 1 : this.form.fisNote = 0
      value['tag'].some(item => item === '更') ? this.form.fupdate = 1 : this.form.fupdate = 0
      value['tag'].some(item => item === '速') ? this.form.isTatFast = 1 : this.form.isTatFast = 0
      value['tag'].some(item => item === '缓') ? this.form.isTatSlow = 1 : this.form.isTatSlow = 0
      value['tag'].some(item => item === '急') ? this.form.isExpress = 1 : this.form.isExpress = 0
      // value['tag'].some(item => item === '院内') ? this.form.fprobeTag = 1 : this.form.fprobeTag = 0
      if (value['tag'].some(item => item === '随')) {
        followTags.push(1)
      }
      if (value['tag'].some(item => item === '访')) {
        followTags.push(2)
      }
      this.form.fisFollowUps = followTags.join(',')
      this.handleSearch()
    },
    // 过滤审核人
    filterReviewer (value) {
      this.form.auditorIds = value['reviewers'].join(',')
      this.handleSearch()
    },
    // 过滤补录人
    filterMakeUpPerson (value) {
      this.form.makeuperIds = value['makeUpPerson'].join(',')
      this.handleSearch()
    },
    // 获取审核人列表（信息录入组）
    getReviewerList () {
      this.$ajax({
        url: '/sample/list_auditors',
        methods: 'post'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.reviewers = []
          let data = res.data || []
          data.forEach(v => {
            let item = {
              value: v.userId,
              text: v.realName
            }
            this.reviewers.push(item)
          })
        }
      })
    },
    // 通过给每个单元格覆盖样式来取消鼠标经过样式
    handleRowStyle ({row, rowIndex}) {
      if (this.selectedRows.has(row.id)) {
        return {backgroundColor: '#c7e1ff !important'}
      }
      return ''
    },
    handleSearch () {
      this.formSubmit = {...this.form}
      // this.form.infoSupplement = [[5, 1], [5, 2]]
      if (this.form.infoSupplement.length === 0) {
        this.formSubmit.infoSupplement = ''
      } else if (this.form.infoSupplement.length === this.infoSupplementOptions[0].children.length) { // 全选
        this.formSubmit.infoSupplement = 5
      } else {
        this.formSubmit.infoSupplement = this.form.infoSupplement.map(v => {
          return v[1]
        })
      }
      this.currentPage = 1
      this.getData()
    },
    getData () {
      const types = {
        0: '其他',
        1: '居民身份证',
        2: '护照',
        3: '军官证',
        4: '港澳通行证',
        5: '社保卡'
      }
      this.$ajax({
        url: '/sample/basic/get_sample_list',
        data: {
          page: {
            current: this.currentPage,
            size: this.pageSize
          },
          params: {
            sampleNum: this.formSubmit.sampleCode,
            sampleStatus: this.formSubmit.infoSupplement.toString(),
            isExpress: this.formSubmit.myTask ? this.emailPrefix : '',
            clinicalStatus: this.formSubmit.taskStatus,
            name: this.formSubmit.name,
            idcard: this.formSubmit.idCard,
            sampleUseType: this.formSubmit.researchClinical,
            geneSampleNums: this.formSubmit.sampleCodeAccurate.replace(/\s+|，/g, ',').split(',').filter(v => v).join(','),
            inspectionUnit: this.formSubmit.unit,
            productName: this.formSubmit.projectName,
            projectNum: this.formSubmit.approvalNum,
            doctor: this.formSubmit.doctor,
            sampleCollectMan: this.formSubmit.seller,
            patientId: this.formSubmit.patientCode,
            isAuditOrNot: this.formSubmit.searchTimeType,
            timeStart: this.formSubmit.times[0],
            timeEnd: this.formSubmit.times[1],
            fisName: this.formSubmit.fisName,
            fisMultiTag: this.formSubmit.fisMultiTag,
            fisPastTag: this.formSubmit.fisPastTag,
            fisAddTag: this.formSubmit.fisAddTag,
            fisTongTag: this.formSubmit.fisTongTag,
            fisNote: this.formSubmit.fisNote,
            fupdate: this.formSubmit.fupdate,
            fisUpdateTag: this.formSubmit.fisUpdateTag,
            fisFollowUps: this.formSubmit.fisFollowUps,
            auditorIds: this.formSubmit.auditorIds,
            makeuperIds: this.formSubmit.makeuperIds,
            customerName: this.formSubmit.customerName,
            orderEntryPerson: this.formSubmit.orderEntryPerson,
            isTatFast: this.formSubmit.isTatFast,
            isTatSlow: this.formSubmit.isTatSlow,
            fisExpress: this.formSubmit.isExpress
          }
        },
        loadingDom: '.clinicalInfoTable'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.selectedRows.clear()
          this.totalPage = res.data.total
          let rows = res.data.rows || []
          this.tableData = []
          rows.forEach(v => {
            let item = {
              id: v.sampleBasicId,
              sampleTag: v.isMuliCheck ? v.isMuliCheck.split(',') : [],
              testingLink: v.currentStep,
              taskStatus: v.clinicalStatus,
              taskStatusText: this.taskStatusType[v.clinicalStatus] || '',
              sampleCode: v.sampleNum,
              name: v.name,
              info: v.clinicalMkSta,
              infoText: this.setInfo(v),
              rejectNotes: v.rejectRmk,
              createTime: v.createTime,
              researchClinical: v.samleUseType,
              researchClinicalText: this.researchClinicalType[v.samleUseType] || '',
              productCode: v.proCode,
              productName: v.proName,
              geneplusProjectCode: v.fgeneplusProjectCode,
              customerName: v.customerName,
              unit: v.hospital,
              doctor: v.doctor,
              sales: v.salesManName,
              salesType: v.salesManType,
              factotumName: v.factotumName,
              sampleStatus: v.experimentStatus,
              phone: v.salesManPhone,
              sampleTime: v.sampleConfirmTime,
              preDeliveryTime: v.preDeliverDate,
              fpatientId: v.fpatientId,
              makeUpPerson: v.clinicalMkMan,
              makeUpTime: v.clinicalMkTime,
              reviewer: v.clinicalAuditMan,
              reviewTime: v.clinicalAuditTime,
              sampleProductId: v.sampleProductId,
              doubtMan: v.doubtMan,
              doubtTime: v.doubtTime,
              ftatSpeedTip: v.ftatSpeedTip,
              fisExpress: v.fisExpress,
              idCard: v.idCard,
              cardType: types[v.cardType],
              probeTag: v.fprobeTag,
              doubtRemark: v.doubtRemark,
              exceptionNote: v.fexceptionNote,
              patientId: v.patientId,
              productArea: v.productArea,
              productAreaText: this.productAreaType[v.productArea] || '',
              isBmsStatus: v.isBmsStatus,
              isName: v.fisName,
              humanHeritageLabel: v.humanHeritageLabel,
              fisAddTag: v.fisAddTag,
              fisTongTag: v.fisTongTag,
              fisUpdateTag: v.fisUpdateTag,
              fisPastTag: v.fisPastTag,
              fisFollowUp: v.fisFollowUp,
              fisNote: v.fisNote,
              fupdate: v.fisUpdate,
              temporaryStorageStatus: v.temporaryStorageStatus // 为Y时表示暂存样本
            }
            let sampleStatusText = {
              0: '待送样',
              1: '已寄送，送样中',
              2: '已到样',
              3: '暂停检测',
              4: '停止检测',
              5: '检测中',
              6: '停止检测-重送样',
              7: '停止检测-已重送样',
              8: '已发报告'
            }
            item.sampleStatusText = sampleStatusText[item.sampleStatus] || '-'
            item.realData = util.deepCopy(item)
            util.setDefaultEmptyValueForObject(item)
            this.tableData.push(item)
          })
        } else {
          this.$message.error(res.message)
        }
      })
    },
    setInfo (v) {
      let r = ''
      if (v.clinicalMkSta === 7) {
        r = this.infoSupplement[v.clinicalMkSta]
      } else if (v.isBmsStatus === 0) {
        r = '前端未补录'
      } else {
        r = this.infoSupplement[v.clinicalMkSta] || ''
      }
      return r
    },
    // 选择时间的改变
    handleSearchTimeChange () {
      this.form.times = ['', '']
    },
    handleReset () {
      this.$refs.table.clearFilter()
      this.form = {
        sampleCode: '',
        infoSupplement: [[5, 0], [5, 1], [5, 2], [5, 3]],
        myTask: false,
        taskStatus: '',
        name: '',
        idCard: '',
        researchClinical: '1',
        sampleCodeAccurate: '',
        unit: '',
        projectName: '',
        approvalNum: '', // 审批号
        searchTimeType: '0', // 时间类型
        times: ['', ''],
        doctor: '',
        seller: '',
        patientCode: '',
        fisName: '',
        fisMultiTag: '',
        fisPastTag: '',
        customerName: '',
        orderEntryPerson: ''
      }
      const startDate = new Date(new Date().setMonth(new Date().getMonth() - 1))
      this.form.times = [util.dateFormatter(startDate, false), util.dateFormatter(new Date(), false)]
      this.handleSearch()
    },
    // 快速录入
    handleFastEntry () {
      this.fastEntryData.visible = true
    },
    // 任务领取
    handleRecieveTask () {
      if (this.selectedRows.size === 0) {
        this.$message.error('请选择需要领取的任务')
        return
      }
      let values = [...this.selectedRows.values()]
      let keys = [...this.selectedRows.keys()]
      // 只有未领取且未补录才能领取
      let coincidence = values.every(item => {
        return item.taskStatus === 0
      })
      if (!coincidence) {
        this.$alert('只能选择未领取的样本', '提示', {type: 'error'})
        return
      }
      this.$ajax({
        url: '/sample/basic/receive_clinical_task',
        data: {
          sampleBasicIds: keys.toString()
        },
        loadingDom: '.page'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.$message.success('领取成功')
          this.getData()
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 查看镜检结果
    handleViewMicroscopyResult () {
      if (this.selectedRows.size === 0) {
        this.$message.error('请选择需要查看的样例')
        return
      }
      if (this.selectedRows.size > 1) {
        this.$message.error('只能选择一条记录进行查看')
        return
      }
      let rows = [...this.selectedRows.values()][0]
      this.viewMicroscopyResultDialogData.visible = true
      this.viewMicroscopyResultDialogData.sampleNum = rows.sampleCode
    },
    // 显示信息复制弹窗
    handleShowCopyInfoDialog () {
      if (this.selectedRows.size === 0) {
        this.$message.error('请选择需要复制的样例')
        return
      }
      if (this.selectedRows.size > 1) {
        this.$message.error('只能选择一条记录进行查看')
        return
      }
      let row = [...this.selectedRows.values()][0]
      if (row.info === 3) {
        this.$alert('必请选择前端未补录/未补录/补录中/驳回的样例进行信息复制！', '提示', {type: 'error'})
        return
      }
      if (row.info === 4) {
        this.$alert('必请选择未审核通过的样例进行信息复制！', '提示', {type: 'error'})
        return
      }
      let id = [...this.selectedRows.keys()][0]
      this.copyInfoDialogData.visible = true
      this.copyInfoDialogData.sampleNum = id
    },
    // 显示统计弹窗
    handleShowCountDialog () {
      this.countDialogData.visible = true
    },
    handleShowUnusualCountDialog () {
      this.unusualCountData.visible = true
    },
    // 信息补录弹窗的重新加载展示
    reloadSaveInfoDialog () {
      this.saveInfoDialogKey += 1
      setTimeout(async () => {
        // if (await this.handleChecked()) {
        this.saveInfoDialogVisible = true
        this.handleRecord()
        // }
      })
    },
    // 检查是否存在他人打开弹窗
    async handleChecked () {
      if (this.saveInfoDialogData.type !== 2) {
        return true
      }
      let res = await this.$ajax({
        url: '/sample/basic/lock_or_unlock_current_window',
        method: 'post',
        data: {
          sampleNum: this.saveInfoDialogData.sampleNum,
          operation: 'lock'
        }
      })
      if (res && res.code === this.SUCCESS_CODE) {
        let status = res.data
        if (status) {
          this.$message.error('样本已在审核中，请稍后操作')
          return false
        }
        return true
      }
    },
    // 审核记录
    handleRecord () {
      let operationName = ''
      switch (this.saveInfoDialogData.type) {
        case 1: operationName = '样本补录'
          break
        case 2: operationName = '信息审核'
          break
        case 3: operationName = '存疑修改'
          break
      }
      this.$ajax({
        url: '/sample/basic/save_operation_record',
        method: 'post',
        data: {
          sampleBasicId: this.saveInfoDialogData.sampleBasicId,
          operationName: operationName
        }
      })
    },
    // 信息补录
    handleInformationSupplement () {
      if (this.selectedRows.size !== 1) {
        this.$message.error('请选择一条数据')
        return
      }
      let row = [...this.selectedRows.values()][0]
      if (row.taskStatus === 0) {
        this.$alert('请先领取任务!', '提示', {type: 'error'})
        return
      }
      // 驳回状态 => 存疑审核驳回不能走补录
      if (row.info === 2 && row.realData.doubtMan) {
        this.$alert('当前状态，只能由存疑人操作“存疑修改”进行修改信息！', '提示', {type: 'error'})
        return
      }
      if (this.emailPrefix !== row.makeUpPerson) {
        this.$alert('您不是任务领取人，不能补录！', '提示', {type: 'error'})
        return
      }
      if (row.taskStatus !== 1) {
        this.$alert('请先领取任务！', '提示', {type: 'error'})
        return
      }
      if (row.info !== 0 && row.info !== 1 && row.info !== 2) {
        this.$alert('只能选择未补录，补录中或驳回的记录进行补录！', '提示', {type: 'error'})
        return
      }
      if (row.infoText === '前端未补录') {
        this.$alert('前端未补录的记录进行补录！', '提示', {type: 'error'})
        return
      }
      this.saveInfoDialogData = {
        type: 1,
        sampleBasicId: row.id,
        sampleNum: row.sampleCode,
        fisFollowUp: row.fisFollowUp,
        testingLink: row.testingLink,
        sampleProductId: row.sampleProductId
      }
      this.reloadSaveInfoDialog()
      // this.saveInfoDialogVisible = true
    },
    // 信息审核
    handleInformationReview () {
      if (this.selectedRows.size !== 1) {
        this.$message.error('请选择一条数据')
        return
      }
      let row = [...this.selectedRows.values()][0]
      if (row.taskStatus === 0) {
        this.$alert('请先领取任务!', '提示', {type: 'error'})
        return
      }
      if (row.info !== 3) {
        this.$alert('必须是待审样例才能审核！', '提示', {type: 'error'})
        return
      }
      if (this.emailPrefix === row.makeUpPerson) {
        this.$alert('补录人和审核人不能是同一个人！', '提示', {type: 'error'})
        return
      }
      this.saveInfoDialogData = {
        type: 2,
        sampleBasicId: row.id,
        sampleNum: row.sampleCode,
        testingLink: row.testingLink,
        sampleProductId: row.sampleProductId
      }
      this.reloadSaveInfoDialog()
      // this.saveInfoDialogVisible = true
    },
    // 存疑修改
    handleSuspiciousModification () {
      if (this.selectedRows.size !== 1) {
        this.$message.error('请选择一条数据')
        return
      }
      let row = [...this.selectedRows.values()][0]
      if (row.taskStatus === 0) {
        this.$alert('请先领取任务!', '提示', {type: 'error'})
        return
      }
      if (row.info !== 4 && row.info !== 2) {
        this.$alert('必须是已审样例或驳回样例才能存疑修改！', '提示', {type: 'error'})
        return
      }
      // 驳回 => 只能由存疑人进行修改信息
      if (row.info === 2 && row.doubtMan !== this.emailPrefix) {
        this.$alert('当前状态下只能由存疑人进行修改信息！', '提示', {type: 'error'})
        return
      }
      this.saveInfoDialogData = {
        type: 3,
        sampleBasicId: row.id,
        sampleNum: row.sampleCode,
        fisFollowUp: row.fisFollowUp,
        testingLink: row.testingLink,
        sampleProductId: row.sampleProductId
      }
      this.reloadSaveInfoDialog()
      // this.saveInfoDialogVisible = true
    },
    // 查看明细
    handleViewDetail () {
      if (this.selectedRows.size !== 1) {
        this.$message.error('请选择一条数据')
        return
      }
      let row = [...this.selectedRows.values()][0]
      this.$store.commit({
        type: 'old/setValue',
        category: 'clinicalInfo',
        clinicalInfo: {
          sampleBasicId: row.id,
          sampleNum: row.sampleCode,
          sampleProductId: row.sampleProductId,
          fisFollowUp: row.fisFollowUp
        }
      })
      util.openNewPage('/business/sub/clinicalInfoManagementInfoDetail')
    },
    // 导出
    handleExport () {
      if (this.selectedRows.size === 0) {
        this.$message.error('请至少选择一条数据')
        return
      }
      let isSanger = [...this.selectedRows.values()].every(v => {
        return v.productName === 'OncoH-Sanger验证'
      })
      if (!isSanger) {
        this.$message.error('只允许导出Sanger验证的样例的检测位点。')
        return
      }
      let sampleNums = [...this.selectedRows.values()].map(v => {
        return v.sampleCode
      })
      this.exportLoading = true
      this.$ajax({
        url: '/sample/sanger/export_sanger_sheet',
        data: {
          sampleNum: sampleNums.toString()
        },
        responseType: 'blob'
      }).then(res => {
        util.readBlob(res.data).then(() => {
          util.downloadFile(res, true)
        }).catch(msg => {
          this.$message.error(msg)
        })
      }).finally(() => {
        this.exportLoading = false
      })
    },
    handleMerge () {
      if (this.selectedRows.size < 2) {
        this.$message.error('请至少选择两条身份证一样的数据')
        return
      }
      let rows = [...this.selectedRows.values()]
      console.log(rows)
      // 1.样例编号不能相同
      let sampleCodeMap = new Map()
      rows.forEach(v => {
        sampleCodeMap.set(v.realData.sampleCode, '')
      })
      if (sampleCodeMap.size !== rows.length) {
        this.$message.error('存在两个样例编号相同的数据，无法合并')
        return
      }
      // 2、身份证号必须相同且不为空
      let firstRow = rows[0]
      let hasSameIdCard = rows.every(v => {
        return v.realData.idCard && v.realData.idCard === firstRow.idCard
      })
      if (!hasSameIdCard) {
        this.$message.error('存在身份证为空或身份证不一致的情况，无法合并')
        return
      }
      // 3、患者名称必须相同
      let hasSameName = rows.every(v => {
        return v.realData.name && v.realData.name === firstRow.name
      })
      if (!hasSameName) {
        this.$message.error('存在姓名为空或姓名不一致的情况，无法合并')
        return
      }
      this.mergeDialogData = {
        tableData: rows,
        visible: true
      }
    },
    // 保存信息弹窗确认
    handleSaveInfoDialogConfirm () {
      this.saveInfoDialogVisible = false
      this.getData()
    },
    // 保存信息关闭
    handleSaveInfoDialogClose () {
      this.saveInfoDialogVisible = false
      this.getData()
    },
    // 点击行
    handleRowClick (row, c) {
      this.$refs.table.toggleRowSelection(row, !this.selectedRows.has(row.id))
      this.handleSelectTable(undefined, row)
    },
    // 选中行
    handleSelectTable (selection, row) {
      this.selectedRows.has(row.id) ? this.selectedRows.delete(row.id) : this.selectedRows.set(row.id, row)
    },
    // 全选
    handleSelectAll (selection) {
      this.selectedRows.clear()
      selection.forEach((row) => {
        this.selectedRows.set(row.id, row)
      })
    }
  }
}
</script>

<style scoped lang="scss">
  .input-width{
    width: 150px;
  }
  .search{
    background-color: #ffffff;
    display: flex;
    align-items: center;
    .search-row{
      display: flex;
      width: auto;
      margin-bottom: 20px;
      & > div{
        margin-right: 30px;
        label{
          font-size: 14px;
          color: #606266;
          display: inline-block;
          width: 100px;
          text-align: right;
          padding: 0 12px 0 0;
          box-sizing: border-box;
        }
        .input{
          width: 200px;
        }
      }
    }
  }
  .content{
    background-color: #ffffff;
    .buttonGroup{
      height: 45px;
      display: flex;
      align-items: center;
      //margin: 0 20px;
      >>>.el-button--mini{
        padding: 5px 10px;
      }
      .iconClass{
        padding-right: 5px;
        font-size: 16px;
      }
    }
  }
  >>>.el-pagination{
    padding: 7px 2em;
  }
  /*/deep/ .el-table__header .el-checkbox {*/
    /*display: none;*/
  /*}*/
</style>
