<template>
  <el-dialog
    title="审核确认"
    :visible.sync="visible"
    width="500px"
    :close-on-click-modal="false"
    @close="handleClose">

    <div class="audit-content">
      <div class="audit-info">
        <p><strong>当前选中：</strong>{{ selectedCount }} 条数据</p>
        <p><strong>当前状态：</strong>{{ currentStatusText }}</p>
      </div>

      <div class="audit-form">
        <el-form :model="form" label-width="100px">
          <el-form-item label="审核状态：">
            <el-radio-group v-model="form.auditStatus">
              <el-radio :label="0">未审核</el-radio>
              <el-radio :label="1">已审核</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button size="mini" @click="handleClose">取消</el-button>
      <el-button size="mini" type="primary" :loading="loading" @click="handleConfirm">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { auditStatusConfig } from '../dataFormate'
import {awaitWrap} from '@/util/util'
import {
  updateSampleMonitoringAuditStatus
} from '@/api/dataMonitoringManagement/smapleMonitoringManagementApi'
export default {
  name: 'AuditDialog',
  props: {
    pvisible: {
      type: Boolean,
      default: false
    },
    selectedData: {
      type: Array,
      default: () => []
    }
  },
  watch: {
    pvisible (val) {
      if (val && this.selectedData.length > 0) {
        // 设置默认的审核状态为第一个数据的状态
        this.form.auditStatus = this.selectedData[0].auditStatus || 0
      }
    }
  },
  computed: {
    visible: {
      get () {
        return this.pvisible
      },
      set (val) {
        this.$emit('update:pvisible', val)
      }
    },
    selectedCount () {
      return this.selectedData.length
    },
    currentStatusText () {
      if (this.selectedData.length === 0) return '-'

      // 获取第一个数据的审核状态
      const firstStatus = this.selectedData[0].auditStatus

      // 检查是否所有数据的审核状态都相同
      const allSameStatus = this.selectedData.every(item => item.auditStatus === firstStatus)

      if (allSameStatus) {
        return auditStatusConfig[firstStatus] || '-'
      } else {
        return '状态不一致'
      }
    }
  },
  data () {
    return {
      loading: false,
      form: {
        auditStatus: 0
      }
    }
  },
  methods: {
    handleClose () {
      this.visible = false
      this.form.auditStatus = 0
      this.loading = false
    },

    async handleConfirm () {
      this.loading = true

      // 构造提交数据
      const submitData = {
        fidList: this.selectedData.map(item => item.fid),
        fauditStatus: this.form.auditStatus
      }
      try {
        // 调用审核API
        const { res } = await awaitWrap(updateSampleMonitoringAuditStatus(submitData))
        if (res && res.code === this.SUCCESS_CODE) {
          this.$message.success('审核状态更新成功')
          this.$emit('dialogConfirmEvent', submitData)
          this.handleClose()
        }
      } catch (error) {
        this.$message.error('审核操作失败，请重试')
        console.error('审核失败:', error)
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style scoped lang="scss">
.audit-content {
  .audit-info {
    background-color: #f5f7fa;
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 20px;

    p {
      margin: 5px 0;
      color: #606266;

      strong {
        color: #303133;
      }
    }
  }

  .audit-form {
    padding: 10px 0;
  }
}

.dialog-footer {
  text-align: right;
}
</style>
