<template>
  <div>
    <el-dialog
      title="忘记密码"
      :visible.sync="visible"
      :close-on-click-modal="false"
      :before-close="handleClose"
      width="500px">
      <el-form :model="form" ref="form" :rules="rules" size="mini" align="left" label-width="100px">
        <el-form-item label="手机号码:" prop="phone">
          <el-input type="text" v-model="form.phone" clearable autocomplete="off" ></el-input>
        </el-form-item>
        <el-form-item label="验证码:" prop="verifyingCode">
          <div style="display: flex; align-items: center;">
            <el-input type="text" v-model="form.verifyingCode" clearable autocomplete="off"></el-input>
            <el-button type="primary" size="small" style="width: 120px;flex-shrink: 0;margin-left: 10px;" :disabled="times > 0" @click="handleSendPhoneCode">
              <span v-if="times === 0">获取验证码</span>
              <span v-else>{{times}}秒后重新获取</span>
            </el-button>
          </div>
        </el-form-item>
        <el-form-item label="新密码:" prop="newPassword">
          <el-input v-model.trim="form.newPassword" :type="showPasswordInputType('np1')" clearable autocomplete="off" >
            <template #suffix >
              <icon-svg class="input-inner-icon" :icon-class="showPasswordIcon('np1')" @click.native.stop="handlePasswordShow('np1')"></icon-svg>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="确认密码:" prop="checkPassword">
          <el-input v-model.trim="form.checkPassword" :type="showPasswordInputType('np2')"  clearable autocomplete="off" >
            <template #suffix >
              <icon-svg class="input-inner-icon" :icon-class="showPasswordIcon('np2')" @click.native.stop="handlePasswordShow('np2')"></icon-svg>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item align="center" label-width="0">
          <el-button type="primary" :loading="submitLoading" @click="handleConfirm">提 交</el-button>
          <el-button @click="handleClose">取 消</el-button>
        </el-form-item>
        <el-form-item label-width="0">
          <div class="password-tip">
            <div class="title"><icon-svg icon-class="icon-tishi" class="icon"></icon-svg>温馨提示</div>
            <p>1、同一账号在吉因加公司系统中的密码均相同。</p>
            <p>2、本次修改成功后，登录吉因加公司其他系统时也请记得使用新密码。</p>
            <p>3、密码规则：必须8~16位，必须数字、大写字母、小写字母和特殊字符四种中任意3种组合而成，不能使用连续或相同的三位以上数字或者字母组合，如123，456，abc，111，aaa</p>
          </div>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { awaitWrap, validatePassword, validatePhone } from 'Util'
import { getPhoneCode, forgetPassword } from '@/api/system/login'
import mixins from '@/util/mixins'
import IconSvg from '@/components/common/iconSvg'
export default {
  name: 'loginForgetPassDialog',
  mixins: [mixins.dialogBaseInfo],
  components: { IconSvg },
  data () {
    const validateCheckPass = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请再次输入密码'))
      } else if (value !== this.form.newPassword) {
        callback(new Error('两次输入密码不一致!'))
      } else {
        callback()
      }
    }
    return {
      submitLoading: false,
      showPasswordTypes: [],
      form: {
        phone: '',
        newPassword: '',
        checkPassword: '',
        verifyingCode: ''
      },
      rules: {
        phone: [
          { required: true, message: '输入手机号', trigger: 'blur' },
          { validator: validatePhone, trigger: 'blur' }
        ],
        newPassword: [
          { required: true, message: '请填入密码', trigger: 'blur' },
          { validator: validatePassword, trigger: 'blur' }
        ],
        checkPassword: [
          { required: true, message: '请填入确认密码', trigger: 'blur' },
          { validator: validateCheckPass, trigger: 'blur' }
        ],
        verifyingCode: [
          { required: true, message: '请填入验证码', trigger: 'blur' }
        ]
      },
      times: 0,
      timer: null
    }
  },
  methods: {
    handleOpen () {
      this.$nextTick(this.$refs.form.resetFields)
    },
    async handleSendPhoneCode () {
      this.times = 60
      const { res, err } = await awaitWrap(getPhoneCode(this.form.phone))
      if (err || (!res || res.code !== this.SUCCESS_CODE)) {
        this.times = 0
      }
      this.timer = setInterval(() => {
        this.times--
        if (this.times <= 0) {
          clearInterval(this.timer)
          this.timer = null
        }
      }, 1000)
    },
    async handleConfirm () {
      await this.validateForm()
      const params = this.setParams()
      this.submitLoading = true
      const { res } = await awaitWrap(forgetPassword(params))
      if (res.code === this.SUCCESS_CODE) {
        this.$emit('dialogConfirmEvent')
        this.visible = false
        await this.$alert('修改密码成功，请重新登录', '成功', {
          confirmButtonText: '确定',
          type: 'success',
          dangerouslyUseHTMLString: true
        })
        this.$store.dispatch('loginOut')
        this.$store.dispatch('delPath')
        this.$router.push({ path: '/login' })
      }
      this.submitLoading = false
    },
    validateForm () {
      return new Promise(resolve => {
        this.$refs.form.validate(valid => {
          if (valid) {
            resolve()
          } else {
            this.$message.error('存在必填字段未填写')
          }
        })
      })
    },
    setParams () {
      return {
        fphone: this.form.phone,
        fnewPassword: this.form.newPassword,
        fcode: this.form.verifyingCode
      }
    },
    showPasswordIcon (type) {
      return this.showPasswordTypes.includes(type) ? 'icon-yanjing_xianshi' : 'icon-yanjing_yincang'
    },
    showPasswordInputType (type) {
      return this.showPasswordTypes.includes(type) ? 'text' : 'password'
    },
    handlePasswordShow (type) {
      const index = this.showPasswordTypes.indexOf(type)
      index === -1 ? this.showPasswordTypes.push(type) : this.showPasswordTypes.splice(index, 1)
    }
  }
}
</script>

<style scoped>
  >>>.el-input__inner{
    color: black;
    padding: 0 15px;
    height: 32px;
    line-height: 32px;
  }
  .password-tip{
    line-height: 1.5;
    font-size: 12px;
    color: #999999;
    margin-top: 10px;
  }
  .title {
    position: relative;
  }
  .icon {
    color: #000;
    margin-right: 10px;
  }
  .input-inner-icon{
    font-size: 20px;
  }
</style>
