<template>
  <div class="">
    <div class="params-search-form">
      <el-form v-model="form" size="mini" label-width="85px" label-suffix=":" @keyup.enter.native="handleSearch" inline>
        <el-form-item label="子订单号">
          <el-input v-model.trim="form.orderCode" placeholder="请输入子订单编号" clearable />
        </el-form-item>
        <el-form-item label="项目名称">
          <el-input v-model.trim="form.projectName" placeholder="请输入项目名称" clearable />
        </el-form-item>
        <el-form-item label="订单状态">
          <el-select v-model="form.orderStatusList" multiple collapse-tags placeholder="请选择订单状态" clearable>
            <el-option v-for="(value, key) in orderStatusOptions" :key="key" :label="value" :value="key"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="吉因加编号">
          <el-input v-model.trim="form.geneCode" placeholder="请输入吉因加编号" clearable />
        </el-form-item>
        <el-form-item label="芯片号">
          <el-input v-model.trim="form.chipNum" placeholder="请输入芯片号" clearable />
        </el-form-item>
        <el-form-item label="项目编号">
          <el-input v-model.trim="form.projectCode" placeholder="请输入项目编号" clearable />
        </el-form-item>
        <el-form-item label="产品类型">
          <el-input v-model.trim="form.productType" placeholder="请输入产品类型" clearable />
        </el-form-item>
        <el-form-item label="实验模式">
          <el-select v-model="form.experimentTypeList" multiple collapse-tags placeholder="请选择实验模式" clearable>
            <el-option v-for="(value, key) in experimentTypes" :key="key" :label="value" :value="key"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="交付类型">
          <el-select v-model="form.deliverTypeList" multiple collapse-tags placeholder="请选择交付类型" clearable>
            <el-option v-for="(item, index) in deliverTypes" :key="index" :label="item" :value="item"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="交付方式">
          <el-select v-model="form.deliveryMethodList" multiple collapse-tags placeholder="请选择交付方式" clearable>
            <el-option v-for="(value, key) in deliveryMethods" :key="key" :label="value" :value="key"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </div>

    <div class="operate-wrapper">
      <el-radio-group v-model="orderType" size="mini" style="margin-bottom: 12px" @input="handleOrderTypeChange">
        <el-radio-button :label="''">全部</el-radio-button>
        <el-radio-button :label="1">未交付</el-radio-button>
        <el-radio-button :label="2">已交付</el-radio-button>
      </el-radio-group>
      <div class="operate-btns-group btn-wrapper">
        <el-dropdown v-if="$setAuthority('019003001', 'buttons')" @command="handleExport">
          <el-button :loading="exportLoading" size="mini" type="primary">导出数据<i
              class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item :command="0">页面数据</el-dropdown-item>
            <el-dropdown-item :command="1">分析数据</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-dropdown v-if="$setAuthority('019003002', 'buttons')" @command="handleCommandDelivery">
          <el-button :loading="exportLoading" size="mini" type="primary" plain>
            重执行<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item :command="0">数据交付</el-dropdown-item>
            <el-dropdown-item :command="1">重释放</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-button v-if="$setAuthority('019003003', 'buttons')" size="mini" :loading="loading" type="primary" plain
          @click="handleDelivery(1)">关闭交付
        </el-button>
        <el-button v-if="$setAuthority('019003004', 'buttons')" size="mini" :loading="loading" type="primary" plain
          @click="handleDelivery(2)">硬盘交付
        </el-button>
        <el-button v-if="$setAuthority('019003005', 'buttons')" size="mini" type="primary" plain
          @click="handleOpenDetail">查看详情
        </el-button>
        <el-button v-if="$setAuthority('019003006', 'buttons')" size="mini" type="primary" plain
          @click="handleSignData">标记数据
        </el-button>
        <el-button v-if="$setAuthority('019003007', 'buttons')" size="mini" type="primary" plain
          @click="handleDataCount">数据求和
        </el-button>
        <el-button size="mini" type="primary" plain @click="handleOpenManualCreationDialog">
          特殊创建
        </el-button>
        <el-button v-if="$setAuthority('019003008', 'buttons')" size="mini" type="primary" plain
          @click="handleSplitDelivery()">重拆
        </el-button>
        <el-button size="mini" type="primary" plain @click="handleSearch">查询</el-button>
        <el-button size="mini" type="primary" plain @click="handleReset">重置</el-button>
        <el-badge :value="searchParamsKeyNum" :hidden="searchParamsKeyNum === 0" class="item" type="primary">
          <el-button size="mini" plain type="primary" @click="searchDialogVisible = true">高级查询</el-button>
        </el-badge>
      </div>

      <search-params-dialog :pvisible.sync="searchDialogVisible" @reset="handleResetAdvance" @search="handleSearch">
        <el-form v-model="form" size="mini" label-width="85px" label-suffix=":" @keyup.enter.native="handleSearch">
          <el-form-item label="原始样本名">
            <el-input v-model.trim="advanceForm.sampleName" :rows="3" :placeholder="placeholder" type="textarea"
              size="mini" clearable class="form-width" autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item label="吉因加编号">
            <el-input v-model.trim="advanceForm.geneCode" :rows="3" :placeholder="placeholder" type="textarea"
              size="mini" clearable class="form-width" autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item label="项目名称">
            <el-input v-model.trim="advanceForm.projectName" :rows="3" :placeholder="placeholder" type="textarea"
              size="mini" clearable class="form-width" autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item label="子订单号">
            <el-input v-model.trim="advanceForm.orderCode" :rows="3" :placeholder="placeholder" type="textarea"
              size="mini" clearable class="form-width" autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item label="交付订单">
            <el-input v-model.trim="advanceForm.deliverOrderCode" :rows="3" :placeholder="placeholder" type="textarea"
              size="mini" clearable class="form-width" autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item label="交付批次">
            <el-input v-model.trim="advanceForm.deliverBatchCode" :rows="3" :placeholder="placeholder" type="textarea"
              size="mini" clearable class="form-width" autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item label="芯片号">
            <el-input v-model.trim="advanceForm.chipNum" :rows="3" :placeholder="placeholder" type="textarea"
              size="mini" clearable class="form-width" autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item label="全部样本">
            <el-switch v-model="advanceForm.allSample" size="mini" class="form-width"></el-switch>
          </el-form-item>
          <el-form-item label="数据质控">
            <el-radio-group v-model="advanceForm.dataQc" size="mini" class="form-width">
              <el-radio-button :label="1">合格</el-radio-button>
              <el-radio-button :label="0">不合格</el-radio-button>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="交付日期">
            <el-date-picker v-model.trim="advanceForm.time" class="form-long-width" type="daterange"
              value-format="yyyy-MM-dd HH:mm:ss" :default-time="['00:00:00', '23:59:59']" start-placeholder="开始日期"
              end-placeholder="结束日期" style="width: 100%"></el-date-picker>
          </el-form-item>
          <el-form-item label="交付单生成">
            <el-date-picker v-model.trim="advanceForm.deliveryTime" class="form-long-width" type="daterange"
              value-format="yyyy-MM-dd HH:mm:ss" :default-time="['00:00:00', '23:59:59']" start-placeholder="开始日期"
              end-placeholder="结束日期" style="width: 100%"></el-date-picker>
          </el-form-item>
          <el-form-item label="订单状态">
            <el-checkbox-group v-model="advanceForm.orderStatusList">
              <el-checkbox v-for="(value, key) in orderStatusOptions" :key="key" :label="key">{{ value }}</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item label="子文库状态">
            <el-checkbox-group v-model="advanceForm.subLibStatusList">
              <el-checkbox v-for="(value, key) in subLibStatus" :key="key" :label="key">{{ value }}</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item label="订单标签">
            <el-checkbox-group v-model="advanceForm.tags">
              <el-checkbox v-for="(value, key) in orderTagOptions" :key="key" :label="key">{{ value }}</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item label="是否加测">
            <el-radio-group v-model="advanceForm.isAddTestingOrder" size="mini" class="form-width">
              <el-radio-button v-for="(value, key) in addTestingOrderOptions" :key="key" :label="key">{{ value
                }}</el-radio-button>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="特殊创建" prop="personalizeType">
            <el-select v-model="advanceForm.personalizeType" multiple clearable class="form-width" placeholder="请选择">
              <el-option v-for="item in personalizeTypeOptions" :key="item.value" :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </search-params-dialog>
    </div>

    <div>
      <el-table ref="table" :data="tableData" :cell-style="handleRowStyle" class="table" size="mini" border
        style="width: 100%" :row-class-name="handleClassName" :height="tbHeight" @select="handleSelectTable"
        @row-click="handleRowClick" @select-all="handleSelectAll" @expand-change="handleExpandChange">
        <el-table-column type="expand" width="30">
          <template slot-scope="props">
            <div style="margin-bottom: 10px">
              <!-- 子订单列表 -->
              <sub-order-list :ref="props.row.id" :id="props.row.id" :params="params"
                @subOrderSelectedChange="handleSubOrderSelectedChange" />
            </div>
          </template>
        </el-table-column>
        <el-table-column type="selection" width="55" />
        <el-table-column label="订单标签" width="100" show-overflow-tooltip>
          <template slot-scope="scope">
            <el-tag v-if="!scope.row.isCreateDeliverBatch" size="mini" type="success">监控</el-tag>
            <el-tag v-if="scope.row.realData.addTestType === 1" size="mini" type="danger">加测</el-tag>
            <el-tag v-if="scope.row.realData.addTestType === 2" size="mini" type="warning">合</el-tag>
            <el-tag v-if="scope.row.realData.fisRelease" size="mini" type="warning">重</el-tag>
            <el-tooltip v-if="scope.row.realData.fpersonalizeFlagText" placement="top" effect="dark"
              :content="scope.row.realData.fpersonalizeFlagText + (scope.row.realData.fproductionArea)">
              <el-tag v-if="scope.row.realData.fpersonalizeFlag === 'NG006'" size="mini" type="warning">调</el-tag>
              <el-tag v-if="scope.row.realData.fpersonalizeFlag !== 'NG006'" size="mini" type="warning">云</el-tag>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="交付状态" prop="deliverStatusText" min-width="80" show-overflow-tooltip>
          <template slot-scope="scope">
            <span :class="scope.row.className">{{ scope.row.deliverStatusText }}</span>
            <el-tooltip v-if="scope.row.note" placement="top" effect="dark" :content="scope.row.note">
              <i class="el-icon-info"></i>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="科服项目名称" prop="projectName" min-width="140" show-overflow-tooltip />
        <el-table-column label="交付订单" prop="deliverOrderCode" min-width="120" show-overflow-tooltip />
        <el-table-column label="子订单" prop="cosSubOrderCode" min-width="120" show-overflow-tooltip>
          <template slot-scope="scope">
            <span v-if="scope.row.cosSubOrderCode" class="link" @click="handleToOrderDetail(scope.row)">{{
              scope.row.cosSubOrderCode }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="交付类型" prop="deliverType" min-width="80" show-overflow-tooltip />
        <el-table-column label="交付方式" prop="deliveryMethod" min-width="80" show-overflow-tooltip />
        <el-table-column label="样本数量" prop="sampleNum" min-width="140" show-overflow-tooltip />
        <el-table-column label="产品类型" prop="productType" min-width="80" show-overflow-tooltip />
        <el-table-column label="项目编号" prop="projectCode" min-width="120" show-overflow-tooltip />
        <el-table-column label="实验模式" prop="experimentTypeText" min-width="80" show-overflow-tooltip />
      </el-table>

      <div style="display: flex; align-items: center; font-size: 13px;">
        <!-- <span style="color: deepskyblue; height: 28px; line-height: 28px;">
          当前选中 {{ selectedRowsSize }} 条记录
        </span> -->
        <el-pagination :page-sizes="pageSizes" :page-size="pageSize" :current-page.sync="currentPage" :total="totalPage"
          layout="total, sizes, prev, pager, next, jumper, slot" @size-change="handleSizeChange"
          @current-change="handleCurrentChange">
          <button @click="handleRefresh">
            <icon-svg icon-class="icon-refresh" />
          </button>
        </el-pagination>
        <!-- 实时统计信息 -->
        <span v-if="realTimeStats.show" class="real-time-stats">
          已选交付订单: {{ realTimeStats.orderCount }},
          已选杂交子文库: {{ realTimeStats.subLibCount }},
          下机产量总计: {{ realTimeStats.totalDataSize }}G
          <!-- 清空选择按钮 -->
          <el-button size="mini" type="text" icon="el-icon-delete" class="clear-selection-btn" style="color: #F56C6C;"
            @click="clearAllSelections(true)">
            清空
          </el-button>
        </span>
      </div>
    </div>

    <order-detail-dialog :pvisible.sync="orderDetailDialogVisible" :ids="ids"
      :sub-order-ids="subOrderIds"></order-detail-dialog>
    <sign-data-dialog :pvisible.sync="signDataVisible" :ids="ids" :sub-order-ids="subOrderIds"
      :sub-order-length="subOrderLength" @dialogConfirmEvent="handleDialogSuccess"></sign-data-dialog>
    <data-count-dialog :ids="ids" :sub-order-ids="subOrderIds" :pvisible.sync="dataCountVisible"
      @dialogConfirmEvent="handleDialogSuccess" />
    <!-- 个性化弹窗 -->
    <manual-creation-dialog :pvisible.sync="manualDialogVisible" :ids="ids" :sub-order-ids="subOrderIds"
      @dialogConfirmEvent="handleDialogSuccess" />
  </div>
</template>

<script>
import mixins from '../../../util/mixins'
import util, {awaitWrap, computeObjectValidKeyNum, downloadFile, readBlob} from '../../../util/util'
import {exportOrderData, getDeliverOrderList, splitDelivery, updateDeliverStatus} from '../../../api/deliveryManagement'
import subOrderList from './components/SubOrderList'
import OrderDetailDialog from './components/orderDetailDalog'
import SignDataDialog from './components/signDataDialog'
import DataCountDialog from './components/dataCountDialog.vue'
import ManualCreationDialog from './components/ManualCreationDialog.vue'
import {personModule, areaOptions, personTagModule} from './constants/constants'

export default {
  name: 'index',
  mixins: [mixins.tablePaginationCommonData],
  components: {
    DataCountDialog,
    SignDataDialog,
    OrderDetailDialog,
    subOrderList,
    ManualCreationDialog
  },
  mounted () {
    this.$_setTbHeight(74 + 40 + 42 + 32, '.params-search-form')
    // 获取当天的最后一秒的时间

    this.advanceForm.deliveryTime = [
      util.dateFormatter(new Date(new Date().getTime() - 14 * 24 * 60 * 60 * 1000), false) + ' ' + '00:00:00',
      util.dateFormatter(new Date(), false) + ' ' + '23:59:59'
    ]
    this.handleSearch()
  },
  computed: {
    searchParamsKeyNum () {
      return computeObjectValidKeyNum(this.advanceForm, [])
    },

    // 实时统计计算属性
    realTimeStats () {
      // 强制依赖响应式数据
      const selectedRowsSize = this.selectedRows.size
      const subOrderSelectRowSize = this.subOrderSelectRow.size
      // 依赖版本号确保Map变化时重新计算
      const subOrderVersion = this.subOrderSelectRowVersion
      const mainOrderVersion = this.selectedRowsVersion

      const selectedMainOrders = [...this.selectedRows.values()]
      const allSelectedSubOrders = this.getAllSelectedSubOrders()
      const unselectedMainOrderSubOrders = this.getSubOrder()

      // 基础调试信息（开发环境）
      if (process.env.NODE_ENV === 'development') {
        console.log('realTimeStats 基础数据:', {
          subOrderVersion,
          mainOrderVersion,
          selectedRowsSize,
          subOrderSelectRowSize,
          selectedMainOrders: selectedMainOrders.length,
          allSelectedSubOrders: allSelectedSubOrders.length,
          unselectedMainOrderSubOrders: unselectedMainOrderSubOrders.length
        })
      }

      // 如果没有选择任何数据，不显示统计信息
      if (selectedMainOrders.length === 0 && allSelectedSubOrders.length === 0) {
        return {
          show: false,
          orderCount: 0,
          subLibCount: 0,
          totalDataSize: '0.00'
        }
      }

      // 计算选中主订单的样本总数
      const mainOrderSampleCount = selectedMainOrders.reduce((total, row) => {
        return util.add(total, row.totalSampleNum || 0)
      }, 0)

      // 计算总的杂交子文库数量
      // 主订单样本数 + 单独选中的子订单数（不属于已选主订单的）
      const totalSubLibCount = util.add(mainOrderSampleCount, unselectedMainOrderSubOrders.length)

      // 计算数据量
      // 只计算未选主订单的子订单数据量，避免重复计算
      const unselectedSubOrderDataSize = unselectedMainOrderSubOrders.reduce((total, item) => {
        return util.add(total, item.afterFilterDataSize || 0)
      }, 0)

      // 计算已选主订单的数据量
      const orderDataSize = selectedMainOrders.reduce((total, row) => {
        return util.add(total, row.subDataSize || 0)
      }, 0)

      // 总数据量 = 主订单数据量 + 单独选中的子订单数据量
      const totalDataSize = (+util.add(unselectedSubOrderDataSize, orderDataSize)).toFixed(2)

      // 详细计算调试信息（开发环境）
      if (process.env.NODE_ENV === 'development') {
        console.log('realTimeStats 计算详情:', {
          主订单数据量: orderDataSize,
          单独子订单数据量: unselectedSubOrderDataSize,
          总数据量: totalDataSize,
          主订单样本数: mainOrderSampleCount,
          单独子订单数: unselectedMainOrderSubOrders.length,
          总子文库数: totalSubLibCount,
          计算说明: {
            子文库数计算: `${mainOrderSampleCount}(主订单样本) + ${unselectedMainOrderSubOrders.length}(单独子订单) = ${totalSubLibCount}`,
            数据量计算: `${orderDataSize}G(主订单) + ${unselectedSubOrderDataSize}G(单独子订单) = ${totalDataSize}G`
          }
        })
      }

      return {
        show: true,
        orderCount: selectedMainOrders.length,
        subLibCount: totalSubLibCount,
        totalDataSize: totalDataSize
      }
    }
  },
  data () {
    return {
      orderType: '',
      params: {},
      searchDialogVisible: false,
      orderDetailDialogVisible: false,
      signDataVisible: false,
      dataCountVisible: false,
      subOrderLength: 0,
      loading: false,
      manualDialogVisible: false, // 个性化弹窗
      personalizeTypeOptions: personModule,
      tableData: [],
      ids: [], // 选中订单id列表
      subOrderIds: [], // 选中子订单id列表
      placeholder: '请输入, 批量查询用逗号分隔，不区分中英文逗号， 或直接粘粘Excel中整行或整列数据',
      orderStatusOptions: { // 订单状态
        0: '未上机',
        // 1: '执行中',
        2: '执行中',
        3: '已交付',
        4: '已关闭',
        5: '已停止',
        6: '不交付'
      },
      orderTagOptions: {
        0: '监控',
        1: '重'
      },
      addTestingOrderOptions: {
        0: '否',
        1: '是',
        2: '加测合并',
        3: '加测不合并'
      },
      deliverTypes: ['极致交付', '普通交付', '先下先交', '不交付', '慢交付'],
      orderColors: {
        2: 'blue'
      },
      subLibStatus: {
        8: '未上机',
        0: '未投递',
        // 1: '等待投递',
        2: '投递失败',
        3: '待交付',
        110: '已交付',
        5: '已关闭',
        6: '已停止',
        7: '不交付',
        4: '已有交付',
        9: '失败（吉云）'
      },
      experimentTypes: {
        0: '合格自动实验',
        1: '全部自动实验',
        2: '确认后实验'
      },
      deliveryMethods: {
        1: '自动',
        2: '手动',
        3: '硬盘'
      },
      exportLoading: false,
      subOrderSelectRow: new Map(),
      // 用于强制计算属性重新计算的响应式计数器
      subOrderSelectRowVersion: 0,
      // 响应式的子订单选择对象（备用方案）
      subOrderSelectRowObj: {},
      // 主订单选择版本号，确保响应式更新
      selectedRowsVersion: 0,
      advanceForm: {
        allSample: false,
        fisMergeAddTesting: '',
        orderCode: '',
        projectName: '',
        orderStatus: '',
        geneCode: '',
        chipNum: '',
        sampleName: '',
        dataQc: '',
        tags: [],
        personalizeType: [],
        deliverBatchCode: '',
        deliverOrderCode: '',
        deliveryTime: [],
        time: [],
        orderStatusList: [],
        subLibStatusList: [],
        orderTagList: []
      },
      form: {
        orderCode: '',
        projectName: '',
        orderStatus: '',
        projectCode: '',
        geneCode: '',
        chipNum: '',

        experimentTypeList: [],
        deliverTypeList: [],
        deliveryMethodList: [],
        orderStatusList: [],
        subLibStatusList: []
      }
    }
  },
  methods: {
    formatter (data = '') {
      return data.replace(/，/g, ',').replace(/\n/g, ',').replace(/\s+/g, ',').split(',')
    },
    // 设置查询参数
    setParams () {
      const fcosSubOrderCodeList = [this.formSubmit.orderCode, ...this.formatter(this.advanceFormSubmit.orderCode)].filter(v => v)
      const fprojectNameList = [this.formSubmit.projectName, ...this.formatter(this.advanceFormSubmit.projectName)].filter(v => v)
      const fgeneNumList = [this.formSubmit.geneCode, ...this.formatter(this.advanceFormSubmit.geneCode)].filter(v => v)
      const ffcNumList = [this.formSubmit.chipNum, ...this.formatter(this.advanceFormSubmit.chipNum)].filter(v => v)
      const foriSampleNameList = this.formatter(this.advanceFormSubmit.sampleName).filter(v => v)
      const fcosDeliverBatchCodeList = this.formatter(this.advanceFormSubmit.deliverBatchCode).filter(v => v)
      const fdeliverOrderCode = this.formatter(this.advanceFormSubmit.deliverOrderCode).filter(v => v)
      const fdeliverStatusList = this.formSubmit.orderStatusList
      const productType = this.formSubmit.productType
      const time = this.advanceFormSubmit.time || []
      const deliveryTime = this.advanceFormSubmit.deliveryTime || []
      const resultMap = {
        0: [0, null],
        1: [1, null],
        2: [1, 1],
        3: [1, 0]
      }
      const result = resultMap[this.advanceFormSubmit.isAddTestingOrder]
      if (result && result.length === 2) {
        this.advanceFormSubmit.isAddTestingOrder = result[0]
        this.advanceFormSubmit.fisMergeAddTesting = result[1]
      }
      this.formSubmit.tags = this.formSubmit.tags || []
      this.advanceFormSubmit.isCreateDeliverBatch = !this.advanceFormSubmit.tags.includes('0')
      this.advanceFormSubmit.fisRelease = this.advanceFormSubmit.tags.includes('1')
      return {
        fcosSubOrderCodeList: fcosSubOrderCodeList,
        fproductType: productType,
        fpersonalizeTypeList: this.advanceFormSubmit.personalizeType,
        fisAddTestingOrder: this.advanceFormSubmit.isAddTestingOrder,
        fisCreateDeliverBatch: +this.advanceFormSubmit.isCreateDeliverBatch === 1 ? null : 0,
        fisRelease: +this.advanceFormSubmit.fisRelease === 0 ? null : 1,
        fprojectNameList: fprojectNameList,
        fdeliverStatusList: [...new Set(fdeliverStatusList), ...new Set(this.advanceFormSubmit.orderStatusList)],
        fcosDeliverOrderType: this.orderType,
        fgeneNumList: fgeneNumList,
        ffcNumList: ffcNumList,
        fisMergeAddTesting: this.advanceFormSubmit.fisMergeAddTesting,
        fqcResult: this.advanceFormSubmit.dataQc,
        foriSampleNameList: foriSampleNameList,
        fcosDeliverBatchCodeList: fcosDeliverBatchCodeList,
        fdeliverOrderCodeList: fdeliverOrderCode,
        fprojectCode: this.formSubmit.projectCode,
        fdeliverTimeStart: time[0],
        fdeliverTimeEnd: time[1],
        fcosOrderCreateTimeStart: deliveryTime[0],
        fcosOrderCreateTimeTimeEnd: deliveryTime[1],
        fexperimentTypeList: this.formSubmit.experimentTypeList || [],
        fdeliverTypeList: this.formSubmit.deliverTypeList || [],
        fdeliveryMethodList: this.formSubmit.deliveryMethodList || [],
        fsubDeliverStatusList: [...new Set(this.formSubmit.subLibStatusList), ...new Set(this.advanceFormSubmit.subLibStatusList)],
        pageVO: {
          currentPage: this.currentPage,
          pageSize: this.pageSize
        }
      }
    },
    handleOrderTypeChange () {
      if (this.orderType === '') {
        this.orderStatusOptions = this.$options.data().orderStatusOptions
      }
      if (this.orderType === 1) {
        this.orderStatusOptions = {
          0: '未上机',
          2: '执行中',
          6: '不交付'
        }
      }
      if (this.orderType === 2) {
        this.orderStatusOptions = {
          3: '已交付'
        }
      }
      this.form.orderStatusList = this.form.orderStatusList.filter(v => Object.keys(this.orderStatusOptions).includes(v))
      this.handleSearch()
    },
    // 设置片区

    // 查询
    async getData () {
      const {res} = await awaitWrap(getDeliverOrderList(this.setParams(), {loadingDom: '.table'}))
      if (res && res.code === this.SUCCESS_CODE) {
        const data = res.data || {}
        const records = data.records || []
        this.totalPage = data.total || 0
        this.tableData = []
        this.clearMap()

        // 清理不在当前页面的子订单选择
        // 检查当前页面的订单ID，清理不存在的订单的子订单选择
        const currentOrderIds = records.map(v => v.fcosDeliverOrderId)
        const keysToDelete = []

        this.subOrderSelectRow.forEach((_, key) => {
          if (!currentOrderIds.includes(key)) {
            keysToDelete.push(key)
          }
        })

        keysToDelete.forEach(key => {
          this.subOrderSelectRow.delete(key)
          this.$delete(this.subOrderSelectRowObj, key)
        })

        if (keysToDelete.length > 0) {
          console.log('清理了不在当前页面的子订单选择:', keysToDelete)
          this.subOrderSelectRowVersion++
        }
        records.forEach(v => {
          const area = areaOptions.find(item => item.value === v.fproductionAreaCode) || ''
          const module = personTagModule.find(item => item.value === v.fpersonalizeFlag)
          const item = {
            id: v.fcosDeliverOrderId,
            deliverStatus: v.fdeliverStatus,
            className: this.orderColors[v.fdeliverStatus],
            deliverStatusText: this.orderStatusOptions[v.fdeliverStatus],
            projectName: v.fprojectName,
            cosSubOrderCode: v.fcosSubOrderCode,
            deliverOrderCode: v.fdeliverOrderCode,
            fisMergeAddTesting: v.fisMergeAddTesting,
            deliverType: v.fdeliverType,
            projectCode: v.fprojectCode,
            experimentType: v.fexperimentType,
            note: v.fnote,
            experimentTypeText: this.experimentTypes[v.fexperimentType],
            totalSampleNum: v.ftotalSampleNum,
            sampleNum: `完成: ${v.fsuccessSampleNum} / 失败: ${v.ffailSampleNum} / 总数: ${v.ftotalSampleNum}`,
            successSampleNum: v.fsuccessSampleNum,
            isCreateDeliverBatch: v.fisCreateDeliverBatch,
            productType: v.fproductType,
            subDataSize: v.fafterFilterDataSizeNum,
            failSampleNum: v.ffailSampleNum,
            fisRelease: v.fisRelease,
            ft7OrderCode: v.ft7OrderCode,
            orderId: v.ft7OrderId,
            cosOrderType: v.cosOrderType,
            fpersonalizeFlag: v.fpersonalizeFlag,
            fpersonalizeFlagText: module && module.label,
            fproductionArea: v.fpersonalizeFlag === 'NG006' && area ? `: ${area.label}` : '',
            isAddTestingOrder: v.fisAddTestingOrder,
            addTestType: +v.fisAddTestingOrder + (+v.fisMergeAddTesting),
            deliveryMethod: this.deliveryMethods[v.fdeliveryMethod]
          }
          item.realData = JSON.parse(JSON.stringify(item))
          util.setDefaultEmptyValueForObject(item)
          this.tableData.push(item)
        })

        // 数据加载完成后同步表格选择状态
        this.$nextTick(() => {
          this.syncTableSelection()
        })
      }
    },
    handleSearch () {
      this.formSubmit = JSON.parse(JSON.stringify(this.form))
      this.advanceFormSubmit = JSON.parse(JSON.stringify(this.advanceForm))
      this.currentPage = 1
      this.getData()
    },
    handleResetAdvance () {
      this.advanceForm = this.$options.data().advanceForm
      this.handleSearch()
    },
    handleReset () {
      this.form = this.$options.data().form
      this.advanceForm = this.$options.data().advanceForm

      // 清空所有选择状态
      this.clearAllSelections()

      this.advanceForm.deliveryTime = [
        util.dateFormatter(new Date(new Date().getTime() - 14 * 24 * 60 * 60 * 1000), true),
        util.dateFormatter(new Date(), true)
      ]
      this.handleSearch()
    },
    // 展开表格
    handleExpandChange (row, expandedRows) {
      // 不再清空子订单选择，保持已选择的状态
      // this.subOrderSelectRow = new Map()

      if (expandedRows.length > 0) {
        this.params = this.setParams()
        // 处理全部样本
        if (this.advanceForm.allSample) {
          this.params.ffcNumList = []
        }
        console.log(this.advanceForm.allSample, this.params)
        this.params.pageVO = null
        this.$nextTick(async () => {
          await this.$refs[row.id].getData(row.id)

          // 恢复该订单的子订单选择状态
          if (this.subOrderSelectRow.has(row.id)) {
            const selectedSubOrders = this.subOrderSelectRow.get(row.id)
            console.log(`准备恢复订单 ${row.id} 的子订单选择状态:`, {
              selectedCount: selectedSubOrders.length,
              selectedIds: selectedSubOrders.map(s => s.id)
            })

            // 等待子订单表格渲染完成后恢复选择状态
            this.$nextTick(() => {
              this.restoreSubOrderSelection(row.id, selectedSubOrders)
            })
          }

          if (this.$refs.table) {
            if (this.$refs.table) this.$refs.table.doLayout()
          }
        })
      } else {
        // 折叠时，可以选择保留该订单下的子订单选择状态
        // 如果需要清理特定订单的子订单选择，可以在这里处理
        console.log(`订单 ${row.id} 已折叠，保持子订单选择状态`)
      }
    },
    handleToOrderDetail (row) {
      console.log(row)
      this.$store.commit({
        type: 'old/setValue',
        category: 'libraryOperatingData',
        libraryOperatingData: {
          type: 2, // type 1编辑 2 只读
          orderId: row.orderId,
          status: 2,
          code: row.ft7OrderCode,
          name: 'lims'
        }
      })
      let path = ''
      if (row.cosOrderType === '1') path = '/business/subpage/technologyService/entryIlluminaLibraryOrder'
      if (row.cosOrderType === '2') path = '/business/subpage/technologyService/entryMGILibraryOrder'
      if (row.cosOrderType === '3') path = '/business/subpage/technologyService/entryTissueOrder'
      if (path) util.openNewPage(path)
    },
    // 导出数据
    async handleExport (command) {
      // 获取选择的子订单
      const subOrder = this.getSubOrder()
      // 获取选择的交付订单
      const ids = [...this.selectedRows.keys()]
      if (this.selectedRows.size < 1 && subOrder.length < 1) {
        this.$message.error('请先选择交付订单或交付批次')
        return
      }
      this.exportLoading = true
      const params = {
        fcosDeliverOrderIdList: ids,
        fcosDeliverBatchDetailIdList: subOrder.map(v => v.id)
      }
      const {res} = await awaitWrap(exportOrderData({fexportExcelType: command, ...params}))
      if (res) {
        const {err} = await awaitWrap(readBlob(res.data))
        if (err) {
          this.$message.error(err)
        } else {
          downloadFile(res)
          // 导出成功后清空所有选择状态
          this.clearAllSelections()
        }
      }
      this.exportLoading = false
    },
    // 获取所有选中的子订单（用于统计显示）
    getAllSelectedSubOrders () {
      const subOrderList = []
      // 使用响应式对象确保Vue能检测到变化
      Object.values(this.subOrderSelectRowObj).forEach((value) => {
        subOrderList.push(value)
      })
      return subOrderList.flat()
    },

    // 过滤出订单未选中的子订单（用于操作时避免重复）
    getSubOrder () {
      const ids = [...this.selectedRows.keys()]
      const subOrderList = []
      this.subOrderSelectRow.forEach((value, key) => {
        if (!ids.includes(key)) {
          subOrderList.push(value)
        }
      })
      return subOrderList.flat()
    },
    // 同一个订单勾选提示
    handleCheckIsOneOrder (subOrder) {
      // 仅允许选择同一个交付订单的样本进行操作。
      if (this.selectedRows.size > 1) {
        this.$message.error('所选记录不属于同一个交付订单，无法创建')
        return false
      }
      if (subOrder.length > 1) {
        const deliveryOrderId = subOrder[0].deliveryOrderId
        const isOneOrder = subOrder.every(v => v.deliveryOrderId === deliveryOrderId)
        if (!isOneOrder) {
          this.$message.error('所选记录不属于同一个交付订单，无法创建')
          return false
        }
      }
    },
    async handleSplitDelivery () {
      const subOrder = this.getSubOrder()
      // 在当前页面选中至少一个样本进行操作。
      if (this.selectedRows.size < 1 && subOrder.length < 1) {
        this.$message.error('未选择任何记录')
        return
      }
      if (!this.handleCheckIsOneOrder) return
      // 仅允许选择“已关闭交付”的样本进行操作。
      // const isCloseDelivery = subOrder.every(v => v.deliveryStatus === 0)

      // 提示：是否确认进行重新交付？请确认样本已执行重拆后进行操作
      await this.$confirm(`是否确认进行重新交付？请确认样本已执行重拆后进行操作`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      // 发送请求
      const ids = [...this.selectedRows.keys()]
      const {res} = await awaitWrap(splitDelivery({
        fcosDeliverOrderIdList: ids,
        fcosDeliverBatchDetailIdList: subOrder.map(v => v.id)
      }))
      if (res && res.code === this.SUCCESS_CODE) {
        this.$message.success('重新交付成功')

        // 操作成功后清空所有选择状态
        this.clearAllSelections()

        await this.getData()
      }
    },
    // 特殊创建
    handleOpenManualCreationDialog () {
      // 获取主订单选中样本和子订单选中样本
      this.subOrderIds = this.getSubOrder().map(v => v.id)
      this.ids = [...this.selectedRows.keys()]
      this.manualDialogVisible = true
    },
    /**
     * 数据交付或重释放
     * @param command
     */
    handleCommandDelivery (command) {
      command === 0 ? this.handleDelivery(0) : this.handleDelivery(3)
    },
    async handleTips (status) {
      const title = {
        0: '数据交付',
        1: '关闭交付',
        2: '硬盘交付',
        3: '重释放'
      }

      // 使用统一的计算逻辑
      const stats = this.realTimeStats

      const operateMessage = `
        已选交付订单: ${stats.orderCount}, 已选杂交子文库: ${stats.subLibCount}, 下机产量总计: ${stats.totalDataSize}G,
        是否进行${title[status]}`

      await this.$confirm(operateMessage, title[status], {
        cancelButtonText: '取消',
        confirmButtonText: '确定',
        type: 'warning'
      })
    },
    /**
     * 更改交付状态
     * @param status 0 数据交付 or 1关闭交付 or 2硬盘交付
     */
    async handleDelivery (status) {
      const subOrder = this.getSubOrder()
      if (this.selectedRows.size < 1 && subOrder.length < 1) {
        this.$message.error('请先选择交付订单或交付批次')
        return
      }
      // 获取选择的交付订单
      const ids = [...this.selectedRows.keys()]
      await this.handleTips(status)
      this.loading = true
      const {res} = await awaitWrap(updateDeliverStatus({
        fisDeliverOrderType: status,
        fcosDeliverOrderIdList: ids,
        fcosDeliverBatchDetailIdList: subOrder.map(v => v.id)
      }))
      if (res && res.code === this.SUCCESS_CODE) {
        const message = {
          0: '操作成功，已生成新的交付订单并投递吉云',
          1: '关闭成功，相关数据已停止交付',
          2: '操作成功，已生成新的交付订单并投递吉云',
          3: '操作成功，已生成新的交付订单并投递吉云'
        }
        this.$message.success(message[status])

        // 操作成功后清空所有选择状态
        this.clearAllSelections()

        this.handleSearch()
      }
      this.loading = false
    },
    // 产看详情
    handleOpenDetail () {
      // 获取选择的子订单
      const subOrder = this.getSubOrder()
      if (this.selectedRows.size < 1 && subOrder.length < 1) {
        this.$message.error('请先选择需要查看的数据')
        return
      }
      // 获取选择的交付订单
      this.ids = [...this.selectedRows.keys()]
      this.subOrderIds = subOrder.map(v => v.id)
      this.orderDetailDialogVisible = true
    },
    // 数据统计
    handleDataCount () {
      const subOrder = this.getSubOrder()
      if (this.selectedRows.size < 1 && subOrder.length < 1) {
        this.$message.error('请先选择交付订单或交付批次')
        return
      }
      this.ids = [...this.selectedRows.keys()]
      this.subOrderIds = subOrder.map(v => v.id)
      this.dataCountVisible = true
    },
    // 标记数据
    handleSignData () {
      const subOrder = this.getSubOrder()
      if (this.selectedRows.size < 1 && subOrder.length < 1) {
        this.$message.error('请先选择交付订单或交付批次')
        return
      }

      // 使用统一的计算逻辑
      this.subOrderLength = this.realTimeStats.subLibCount
      this.ids = [...this.selectedRows.keys()]
      this.subOrderIds = subOrder.map(v => v.id)
      this.signDataVisible = true
    },

    // 对话框操作成功后的统一处理
    handleDialogSuccess () {
      console.log('对话框操作成功，清空选择状态并刷新数据')

      // 清空所有选择状态
      this.clearAllSelections()

      // 刷新数据
      this.getData()
    },

    // 主订单选择处理（单个选择）
    handleSelectTable (selection, row) {
      console.log('主订单选择变化:', {
        rowId: row.id,
        currentlySelected: this.selectedRows.has(row.id),
        selectionLength: selection ? selection.length : 'undefined'
      })

      if (this.selectedRows.has(row.id)) {
        this.selectedRows.delete(row.id)
      } else {
        this.selectedRows.set(row.id, row)
      }

      // 更新选择数量和版本号
      this.selectedRowsSize = this.selectedRows.size
      this.selectedRowsVersion++

      console.log('selectedRows 更新后:', {
        size: this.selectedRows.size,
        version: this.selectedRowsVersion,
        keys: [...this.selectedRows.keys()]
      })

      // 强制触发响应式更新
      this.$forceUpdate()
    },

    // 主订单全选处理
    handleSelectAll (selection) {
      console.log('主订单全选:', {selectionLength: selection.length})

      this.selectedRows.clear()
      selection.forEach((row) => {
        this.selectedRows.set(row.id, row)
      })

      // 更新选择数量和版本号
      this.selectedRowsSize = this.selectedRows.size
      this.selectedRowsVersion++

      console.log('全选后 selectedRows:', {
        size: this.selectedRows.size,
        version: this.selectedRowsVersion,
        keys: [...this.selectedRows.keys()]
      })

      // 强制触发响应式更新
      this.$forceUpdate()
    },

    // 恢复子订单选择状态
    restoreSubOrderSelection (orderId, selectedSubOrders) {
      console.log(`开始恢复订单 ${orderId} 的子订单选择状态...`)

      if (!this.$refs[orderId]) {
        console.warn(`订单 ${orderId} 的子订单组件未找到`)
        return
      }

      const subOrderComponent = this.$refs[orderId]

      // 等待子订单数据加载完成
      this.$nextTick(() => {
        if (!subOrderComponent.$refs.table) {
          console.warn(`订单 ${orderId} 的子订单表格未找到`)
          return
        }

        console.log(`恢复订单 ${orderId} 的选择状态:`, {
          selectedSubOrders: selectedSubOrders.length,
          tableData: subOrderComponent.tableData.length
        })

        // 清空子订单组件的当前选择状态
        subOrderComponent.selectedRows.clear()
        subOrderComponent.selectedRowsSize = 0
        subOrderComponent.$refs.table.clearSelection()

        // 根据保存的选择状态恢复
        selectedSubOrders.forEach(savedSubOrder => {
          // 在当前表格数据中查找对应的行
          const currentRow = subOrderComponent.tableData.find(row => row.id === savedSubOrder.id)
          if (currentRow) {
            // 更新子订单组件的内部状态
            subOrderComponent.selectedRows.set(currentRow.id, currentRow)
            // 更新表格选择状态
            subOrderComponent.$refs.table.toggleRowSelection(currentRow, true)
            console.log(`恢复子订单选择: ${currentRow.id}`)
          } else {
            console.warn(`子订单 ${savedSubOrder.id} 在当前数据中未找到`)
          }
        })

        // 更新选择数量
        subOrderComponent.selectedRowsSize = subOrderComponent.selectedRows.size

        console.log(`订单 ${orderId} 子订单选择状态恢复完成:`, {
          restoredCount: subOrderComponent.selectedRows.size,
          expectedCount: selectedSubOrders.length
        })
      })
    },

    // 同步表格选择状态
    syncTableSelection () {
      if (!this.$refs.table) return

      console.log('同步表格选择状态...')

      // 清空表格当前选择
      this.$refs.table.clearSelection()

      // 根据 selectedRows 重新设置表格选择状态
      this.tableData.forEach(row => {
        if (this.selectedRows.has(row.id)) {
          this.$refs.table.toggleRowSelection(row, true)
        }
      })

      console.log('表格选择状态同步完成:', {
        selectedRowsSize: this.selectedRows.size,
        tableDataLength: this.tableData.length
      })
    },

    // 行点击处理
    handleRowClick (row, column) {
      // 如果点击的是展开列，不处理选择
      if (column && column.type === 'expand') {
        return
      }

      this.$refs.table.toggleRowSelection(row, !this.selectedRows.has(row.id))
      this.handleSelectTable(undefined, row)
    },

    // 子订单选择改变
    handleSubOrderSelectedChange ({key, data}) {
      console.log('子订单选择变化:', {
        orderId: key,
        dataLength: data.length,
        selectedIds: data.map(d => d.id),
        timestamp: new Date().toLocaleTimeString()
      })

      if (data.length === 0) {
        // 如果没有选中的数据，删除这个key
        this.subOrderSelectRow.delete(key)
        this.$delete(this.subOrderSelectRowObj, key)
        console.log(`清空订单 ${key} 的子订单选择`)
      } else {
        // 有选中的数据，设置到Map和响应式对象中
        this.subOrderSelectRow.set(key, data)
        this.$set(this.subOrderSelectRowObj, key, data)
        console.log(`更新订单 ${key} 的子订单选择:`, data.length)
      }

      console.log('subOrderSelectRow 全局状态:', {
        totalOrders: this.subOrderSelectRow.size,
        totalSubOrders: [...this.subOrderSelectRow.values()].flat().length,
        orderDetails: [...this.subOrderSelectRow.entries()].map(([orderId, subOrders]) => ({
          orderId,
          count: subOrders.length
        }))
      })

      // 更新版本号，触发计算属性重新计算
      this.subOrderSelectRowVersion++

      // 触发计算属性更新，实时显示统计信息
      this.$forceUpdate()
    },

    // 清空所有选择的方法
    clearAllSelections (showMessage = false) {
      console.log('清空所有选择状态...')

      // 清空主订单选择
      this.selectedRows.clear()
      this.selectedRowsSize = 0
      this.selectedRowsVersion++

      // 清空子订单选择
      this.subOrderSelectRow.clear()
      this.subOrderSelectRowObj = {}
      // 重置版本号
      this.subOrderSelectRowVersion++

      // 清空表格选择状态
      if (this.$refs.table) {
        this.$refs.table.clearSelection()
      }

      // 清空所有展开的子订单表格选择
      this.tableData.forEach(row => {
        if (this.$refs[row.id] && this.$refs[row.id].$refs.table) {
          this.$refs[row.id].$refs.table.clearSelection()
          // 同时清空子订单组件的选择状态
          if (this.$refs[row.id].selectedRows) {
            this.$refs[row.id].selectedRows.clear()
            this.$refs[row.id].selectedRowsSize = 0
          }
        }
      })

      console.log('选择状态清空完成:', {
        selectedRowsSize: this.selectedRows.size,
        subOrderSelectRowSize: this.subOrderSelectRow.size,
        selectedRowsVersion: this.selectedRowsVersion,
        subOrderSelectRowVersion: this.subOrderSelectRowVersion
      })

      // 只有手动清空时才显示消息
      if (showMessage) {
        this.$message.success('已清空所有选择')
      }
    },

    // 重写 mixins 中的 clearMap 方法，确保版本号更新
    clearMap () {
      this.selectedRows.clear()
      this.selectedRowsSize = 0
      this.selectedRowsVersion++

      console.log('clearMap 执行:', {
        selectedRowsSize: this.selectedRowsSize,
        selectedRowsVersion: this.selectedRowsVersion
      })
    },

    // 强制清空所有子订单选择（调试用）
    forceCleanSubOrderSelections () {
      console.log('强制清空所有子订单选择...')

      // 清空父组件的子订单选择数据
      this.subOrderSelectRow.clear()
      this.subOrderSelectRowObj = {}
      this.subOrderSelectRowVersion++

      // 清空所有子订单组件的选择状态
      this.tableData.forEach(row => {
        if (this.$refs[row.id]) {
          const subOrderComponent = this.$refs[row.id]
          if (subOrderComponent.selectedRows) {
            subOrderComponent.selectedRows.clear()
            subOrderComponent.selectedRowsSize = 0
          }
          if (subOrderComponent.$refs.table) {
            subOrderComponent.$refs.table.clearSelection()
          }
        }
      })

      this.$forceUpdate()
      console.log('所有子订单选择已强制清空')
    }
  }
}
</script>

<style scoped lang="scss">
.operate-wrapper {
  display: flex;
  align-items: center;

  .btn-wrapper {
    margin-left: 20px;
  }
}

.blue {
  color: $color
}

.green {
  color: $success-color
}

.red {
  color: $fail-color
}

/* 实时统计信息样式 */
.real-time-stats {
  display: inline-flex;
  align-items: center;
  padding: 4px 12px;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border: 1px solid #67C23A;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  color: #67C23A;
  box-shadow: 0 2px 4px rgba(103, 194, 58, 0.1);
  transition: all 0.3s ease;
}

.real-time-stats:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(103, 194, 58, 0.15);
}

.clear-selection-btn {
  margin-left: 8px;
  padding: 2px 8px;
  font-size: 11px;
  border-radius: 12px;
  transition: all 0.2s ease;
}

.clear-selection-btn:hover {
  background-color: #fef2f2;
  border-color: #fca5a5;
}
</style>
