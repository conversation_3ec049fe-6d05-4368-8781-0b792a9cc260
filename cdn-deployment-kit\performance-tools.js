/**
 * 性能工具集合
 * 集成性能分析、并行构建优化、CSS优化等功能
 */

const fs = require('fs')
const path = require('path')
const gzipSize = require('gzip-size')
const chalk = require('chalk')
// 导入统一日志系统
const { logger } = require('./logger')

// 创建带上下文的日志器
const log = logger.child('performance-tools')

class PerformanceAnalyzer {
  constructor () {
    this.startTime = null
    this.endTime = null
    this.buildStats = {
      duration: 0,
      bundleSize: {},
      gzipSize: {},
      assetCount: 0,
      chunkCount: 0
    }
  }

  start () {
    this.startTime = Date.now()
    log.analyze('开始性能分析...')
  }

  end () {
    this.endTime = Date.now()
    this.buildStats.duration = this.endTime - this.startTime
    log.success(`性能分析完成，耗时: ${this.buildStats.duration}ms`)
  }

  async analyzeBuild (distPath) {
    if (!distPath || !fs.existsSync(distPath)) {
      log.error('dist目录不存在')
      return
    }

    await this.analyzeAssets(distPath)
    this.generateReport()
  }

  async analyzeAssets (distDir) {
    const staticDir = path.join(distDir, 'static')

    if (fs.existsSync(staticDir)) {
      await this.analyzeDirectory(staticDir, 'static')
    }

    // 分析HTML文件
    const indexPath = path.join(distDir, 'index.html')
    if (fs.existsSync(indexPath)) {
      const stats = fs.statSync(indexPath)
      const content = fs.readFileSync(indexPath)
      this.buildStats.bundleSize['index.html'] = stats.size
      this.buildStats.gzipSize['index.html'] = await gzipSize(content)
      this.buildStats.assetCount++
    }
  }

  async analyzeDirectory (dir, prefix) {
    const items = fs.readdirSync(dir)

    for (const item of items) {
      const itemPath = path.join(dir, item)
      const stat = fs.statSync(itemPath)

      if (stat.isDirectory()) {
        await this.analyzeDirectory(itemPath, `${prefix}/${item}`)
      } else if (stat.isFile() && !item.endsWith('.gz')) {
        const content = fs.readFileSync(itemPath)
        const relativePath = `${prefix}/${item}`

        this.buildStats.bundleSize[relativePath] = stat.size
        this.buildStats.gzipSize[relativePath] = await gzipSize(content)
        this.buildStats.assetCount++
      }
    }
  }

  generateReport () {
    const totalSize = Object.values(this.buildStats.bundleSize).reduce((sum, size) => sum + size, 0)
    const totalGzipSize = Object.values(this.buildStats.gzipSize).reduce((sum, size) => sum + size, 0)

    log.plain('')
    log.analyze('性能分析报告')
    log.separator('=', 50)
    log.time(`构建时间: ${this.buildStats.duration}ms (${(this.buildStats.duration / 1000).toFixed(2)}s)`)
    log.file(`总文件数: ${this.buildStats.assetCount}`)
    log.debug(`代码块数: ${this.buildStats.chunkCount}`)
    log.size(`总大小: ${this.formatSize(totalSize)}`)
    log.size(`Gzip后: ${this.formatSize(totalGzipSize)} (压缩率: ${((1 - totalGzipSize / totalSize) * 100).toFixed(1)}%)`)

    // 最大文件
    const largestFiles = this.getLargestFiles(5)
    log.performance('最大的5个文件:')
    largestFiles.forEach((file, index) => {
      log.debug(`  ${index + 1}. ${file.name}: ${this.formatSize(file.size)} (gzip: ${this.formatSize(file.gzipSize)})`)
    })

    // 性能建议
    this.generateSuggestions(totalSize, totalGzipSize)

    // 保存报告到文件
    this.saveReportToFile()
  }

  getLargestFiles (count) {
    return Object.entries(this.buildStats.bundleSize)
      .map(([name, size]) => ({
        name,
        size,
        gzipSize: this.buildStats.gzipSize[name] || 0
      }))
      .sort((a, b) => b.size - a.size)
      .slice(0, count)
  }

  generateSuggestions (totalSize, totalGzipSize) {
    console.log(chalk.cyan('\n💡 性能优化建议:'))

    if (this.buildStats.duration > 60000) {
      console.log(chalk.red('  ⚠️  构建时间过长，建议启用更多缓存和并行处理'))
    }

    if (totalSize > 5 * 1024 * 1024) {
      console.log(chalk.red('  ⚠️  总包大小过大，建议进一步代码分割'))
    }

    if (totalGzipSize > 2 * 1024 * 1024) {
      console.log(chalk.red('  ⚠️  Gzip后大小仍然较大，建议优化资源'))
    }

    const largestFile = this.getLargestFiles(1)[0]
    if (largestFile && largestFile.size > 1024 * 1024) {
      console.log(chalk.red(`  ⚠️  文件 ${largestFile.name} 过大，建议拆分`))
    }

    log.success('已启用Gzip压缩')
    log.success('已启用代码分割')
    log.success('已启用缓存优化')
  }

  formatSize (bytes) {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  saveReportToFile () {
    const report = {
      timestamp: new Date().toISOString(),
      duration: this.buildStats.duration,
      assetCount: this.buildStats.assetCount,
      bundleSize: this.buildStats.bundleSize,
      gzipSize: this.buildStats.gzipSize,
      largestFiles: this.getLargestFiles(10)
    }

    const reportPath = path.resolve(process.cwd(), 'performance-report.json')
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2))
    console.log(chalk.gray(`📄 详细报告已保存到: ${reportPath}`))
  }
}

class ParallelBuildOptimizer {
  constructor (projectRoot = process.cwd()) {
    this.projectRoot = projectRoot
    this.cpuCount = require('os').cpus().length
    // 紧急修复：减少并行进程数量以降低内存使用和文件数量
    this.parallelCount = Math.max(1, Math.max(4, this.cpuCount - 2))
    this.cacheDir = path.resolve(this.projectRoot, 'node_modules/.cache')
    this.buildCacheDir = path.join(this.cacheDir, 'build-optimizer')
    this.ensureCacheDir()
  }

  // 确保缓存目录存在
  ensureCacheDir () {
    const fs = require('fs')
    if (!fs.existsSync(this.cacheDir)) {
      fs.mkdirSync(this.cacheDir, { recursive: true })
    }
    if (!fs.existsSync(this.buildCacheDir)) {
      fs.mkdirSync(this.buildCacheDir, { recursive: true })
    }
  }

  // 获取缓存版本
  getCacheVersion () {
    try {
      const packageJson = require(path.join(this.projectRoot, 'package.json'))
      return `${packageJson.version || '1.0.0'}-${process.env.NODE_ENV || 'production'}`
    } catch (error) {
      return '1.0.0-production'
    }
  }

  // 获取并行处理配置
  getParallelConfig () {
    const parallelism = this.parallelCount

    log.performance(`启用并行处理: ${parallelism} 个工作进程 (总CPU核心: ${this.cpuCount})`)

    return {
      // Webpack并行配置
      webpack: {
        parallelism: parallelism,
        cache: true,
        profile: false // 生产环境关闭性能分析
      },

      // UglifyJS并行配置
      uglifyjs: {
        parallel: parallelism,
        cache: path.join(this.buildCacheDir, 'uglifyjs-cache'),
        sourceMap: false // 生产环境关闭source map以提升速度
      },

      // Babel并行配置
      babel: {
        cacheDirectory: path.join(this.buildCacheDir, 'babel-cache'),
        cacheCompression: false, // 关闭缓存压缩以提升速度
        compact: false // 关闭代码压缩以提升编译速度
      },

      // CSS处理并行配置
      css: {
        parallel: parallelism,
        cacheDirectory: path.join(this.buildCacheDir, 'css-cache')
      }
    }
  }

  // 获取增量构建配置
  getIncrementalBuildConfig () {
    return {
      // 文件监听配置
      watchOptions: {
        ignored: /node_modules/,
        aggregateTimeout: 300,
        poll: false
      },

      // 缓存配置
      cache: {
        type: 'filesystem',
        cacheDirectory: path.join(this.buildCacheDir, 'webpack-cache'),
        buildDependencies: {
          config: [__filename]
        },
        // 缓存版本控制
        version: this.getCacheVersion()
      },

      // 快照配置
      snapshot: {
        managedPaths: [path.resolve(this.projectRoot, 'node_modules')],
        immutablePaths: [],
        buildDependencies: {
          hash: true,
          timestamp: true
        },
        module: {
          timestamp: true
        },
        resolve: {
          timestamp: true
        }
      }
    }
  }

  // 获取模块解析优化配置
  getResolveOptimization () {
    return {
      // 模块解析缓存
      unsafeCache: true,

      // 符号链接优化
      symlinks: false,

      // 缓存解析结果
      cacheWithContext: false
    }
  }

  // 获取Loader优化配置
  getLoaderOptimization () {
    const parallelConfig = this.getParallelConfig()

    return {
      // Vue Loader优化
      vue: {
        cacheDirectory: path.join(this.buildCacheDir, 'vue-loader-cache'),
        cacheIdentifier: this.getCacheVersion()
      },

      // Babel Loader优化
      babel: {
        ...parallelConfig.babel,
        include: [
          path.resolve(this.projectRoot, 'src'),
          path.resolve(this.projectRoot, 'test')
        ],
        exclude: /node_modules/
      },

      // CSS Loader优化
      css: {
        minimize: true,
        sourceMap: false,
        importLoaders: 2
      },

      // 文件Loader优化
      file: {
        name: 'static/img/[name].[hash:8].[ext]',
        limit: 10000 // 10KB以下的文件转为base64
      }
    }
  }

  // 获取插件优化配置
  getPluginOptimization () {
    const parallelConfig = this.getParallelConfig()

    return {
      // DefinePlugin优化
      define: {
        'process.env.NODE_ENV': JSON.stringify('production'),
        'process.env.VUE_APP_VERSION': JSON.stringify(this.getProjectVersion())
      },

      // UglifyJsPlugin优化
      uglifyjs: {
        ...parallelConfig.uglifyjs,
        uglifyOptions: {
          compress: {
            warnings: false,
            drop_console: true,
            drop_debugger: true,
            pure_funcs: ['console.log', 'console.info']
          },
          mangle: {
            safari10: true
          },
          output: {
            comments: false,
            beautify: false
          }
        }
      },

      // 模块连接插件
      moduleConcat: {
        // 启用作用域提升
        concatenateModules: true
      }
    }
  }

  // 获取项目版本
  getProjectVersion () {
    try {
      const packageJson = require(path.join(this.projectRoot, 'package.json'))
      return packageJson.version || '1.0.0'
    } catch (error) {
      return '1.0.0'
    }
  }

  // 获取性能监控配置
  getPerformanceConfig () {
    return {
      // 性能提示
      performance: {
        hints: 'warning',
        maxEntrypointSize: 2000000, // 2MB
        maxAssetSize: 1000000, // 1MB
        assetFilter: (assetFilename) => {
          return !assetFilename.endsWith('.map')
        }
      },

      // 统计信息配置
      stats: {
        colors: true,
        modules: false,
        children: false,
        chunks: false,
        chunkModules: false,
        timings: true,
        assets: true,
        assetsSort: 'size',
        reasons: false,
        source: false,
        errorDetails: true
      }
    }
  }

  // 清理过期缓存
  cleanExpiredCache () {
    console.log(chalk.blue('🧹 清理过期缓存...'))

    const fs = require('fs')
    const maxAge = 7 * 24 * 60 * 60 * 1000 // 7天
    const now = Date.now()
    let cleanedFiles = 0

    const cleanDirectory = (dir) => {
      if (!fs.existsSync(dir)) return

      const files = fs.readdirSync(dir)
      files.forEach(file => {
        const filePath = path.join(dir, file)
        const stat = fs.statSync(filePath)

        if (stat.isDirectory()) {
          cleanDirectory(filePath)
          // 如果目录为空，删除它
          try {
            fs.rmdirSync(filePath)
          } catch (e) {
            // 目录不为空，忽略错误
          }
        } else if (now - stat.mtime.getTime() > maxAge) {
          fs.unlinkSync(filePath)
          cleanedFiles++
        }
      })
    }

    cleanDirectory(this.buildCacheDir)

    if (cleanedFiles > 0) {
      console.log(chalk.green(`✅ 清理了 ${cleanedFiles} 个过期缓存文件`))
    } else {
      console.log(chalk.gray('📁 没有发现过期缓存文件'))
    }
  }

  // 生成优化后的Webpack配置 - 核心方法（完全独立版）
  generateOptimizedWebpackConfig (baseConfig) {
    const parallelConfig = this.getParallelConfig()
    const resolveConfig = this.getResolveOptimization()
    const pluginConfig = this.getPluginOptimization()
    const performanceConfig = this.getPerformanceConfig()

    log.config('应用独立的并行构建优化配置...')

    // 安全地克隆配置，保留插件实例
    const optimizedConfig = {
      ...baseConfig,
      // 保留原始插件数组的引用，稍后单独处理
      plugins: baseConfig.plugins ? [...baseConfig.plugins] : []
    }

    // 安全地应用并行处理配置
    optimizedConfig.parallelism = parallelConfig.webpack.parallelism
    optimizedConfig.cache = parallelConfig.webpack.cache
    optimizedConfig.profile = parallelConfig.webpack.profile

    // 安全地合并解析优化
    if (!optimizedConfig.resolve) {
      optimizedConfig.resolve = {}
    }
    Object.assign(optimizedConfig.resolve, resolveConfig)

    // 安全地应用性能配置
    optimizedConfig.performance = performanceConfig.performance
    optimizedConfig.stats = performanceConfig.stats

    // 动态优化babel-loader配置（不修改原项目文件）
    if (optimizedConfig.module && optimizedConfig.module.rules) {
      optimizedConfig.module.rules = optimizedConfig.module.rules.map(rule => {
        if (rule.test && rule.test.toString().includes('\\.js$') && rule.loader === 'babel-loader') {
          log.config('动态启用babel-loader缓存')
          return {
            ...rule,
            options: {
              ...(rule.options || {}),
              cacheDirectory: path.join(this.buildCacheDir, 'babel-cache')
            }
          }
        }
        return rule
      })
    }

    // 增强的UglifyJS并行配置
    if (optimizedConfig.plugins) {
      optimizedConfig.plugins = optimizedConfig.plugins.map(plugin => {
        if (plugin.constructor.name === 'UglifyJsPlugin') {
          log.config('优化现有UglifyJsPlugin配置（保持兼容性）')

          // 安全地克隆并优化现有插件，而不是创建新实例
          try {
            // 创建一个新的插件实例，使用相同的构造函数
            const PluginConstructor = plugin.constructor
            const optimizedOptions = {
              ...plugin.options,
              parallel: parallelConfig.uglifyjs.parallel,
              cache: path.join(this.buildCacheDir, 'uglifyjs-cache'),
              sourceMap: parallelConfig.uglifyjs.sourceMap
            }

            return new PluginConstructor(optimizedOptions)
          } catch (error) {
            console.warn(chalk.yellow('⚠️  无法创建优化插件实例，修改原实例'))
            console.log(chalk.gray(`   错误详情: ${error.message}`))

            // 回退方案：直接修改原插件的选项
            try {
              if (plugin.options) {
                plugin.options.parallel = parallelConfig.uglifyjs.parallel
                plugin.options.cache = path.join(this.buildCacheDir, 'uglifyjs-cache')
                console.log(chalk.green('✅ 成功修改原插件配置'))
              }
            } catch (e) {
              console.log(chalk.gray('   无法修改原插件配置，保持原样'))
            }
            return plugin
          }
        }
        return plugin
      })
    }

    // 尝试添加thread-loader支持（可选）
    this.tryEnableThreadLoader(parallelConfig.webpack.parallelism)

    // 尝试添加cache-loader支持（可选）
    this.tryEnableCacheLoader()

    // 应用CSS优化配置
    const cssOptimizer = new CSSOptimizer(this.projectRoot)
    let finalOptimizedConfig = cssOptimizer.optimizeWebpackConfig(optimizedConfig)

    // 应用JavaScript优化配置
    const jsOptimizer = new JSOptimizer(this.projectRoot)
    finalOptimizedConfig = jsOptimizer.optimizeWebpackConfig(finalOptimizedConfig)

    log.success(`独立并行配置已应用: ${parallelConfig.webpack.parallelism} 个进程`)
    log.success(`缓存目录: ${this.buildCacheDir}`)
    log.success('Babel缓存: 已启用')
    log.success('CSS代码分割: 已启用')
    log.success('JavaScript代码分割: 已启用')

    return finalOptimizedConfig
  }

  // 尝试启用thread-loader（紧急修复：暂时禁用）
  tryEnableThreadLoader (parallelism) {
    // 紧急性能修复：暂时禁用thread-loader以减少复杂性和内存使用
    log.warn('thread-loader已暂时禁用 (紧急性能修复)')
    return false

    /* 原始代码已暂时注释
    try {
      const threadLoader = require(path.join(__dirname, 'node_modules/thread-loader'))
      console.log(chalk.blue('🔧 启用thread-loader多线程处理'))

      threadLoader.warmup({
        workers: parallelism,
        workerParallelJobs: 50,
        poolTimeout: 2000
      }, [
        'babel-loader',
        'vue-loader'
      ])
      return true
    } catch (error) {
      log.debug('thread-loader不可用，跳过多线程loader优化')
      return false
    }
    */
  }

  // 尝试启用cache-loader（可选功能）
  tryEnableCacheLoader () {
    try {
      // eslint-disable-next-line no-unused-vars
      const cacheLoader = require(path.join(__dirname, 'node_modules/cache-loader'))
      log.config('cache-loader可用')
      return true
    } catch (error) {
      log.debug('cache-loader不可用，跳过缓存loader优化')
      return false
    }
  }

  // 生成构建优化报告
  generateOptimizationReport () {
    const os = require('os')
    const fs = require('fs')

    const report = {
      timestamp: new Date().toISOString(),
      system: {
        cpuCount: this.cpuCount,
        platform: os.platform(),
        arch: os.arch(),
        totalMemory: Math.round(os.totalmem() / 1024 / 1024 / 1024) + 'GB',
        freeMemory: Math.round(os.freemem() / 1024 / 1024 / 1024) + 'GB'
      },
      optimization: {
        parallelism: this.parallelCount,
        cacheEnabled: true,
        incrementalBuild: true,
        resolveOptimization: true,
        loaderOptimization: true
      },
      recommendations: [
        `使用 ${this.parallelCount} 个并行进程进行构建`,
        '启用文件系统缓存以提升重复构建速度',
        '优化模块解析路径减少查找时间',
        '使用增量构建避免重复编译',
        '定期清理过期缓存文件'
      ]
    }

    const reportPath = path.resolve(this.projectRoot, 'build-optimization-report.json')
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2))

    console.log(chalk.gray(`📄 构建优化报告已保存到: ${reportPath}`))
    return report
  }

  // 主优化流程
  optimize () {
    log.performance('启动并行构建优化...')

    // 清理过期缓存
    this.cleanExpiredCache()

    // 生成优化报告
    const report = this.generateOptimizationReport()

    console.log('')
    console.log(chalk.blue('🚀 并行构建优化配置:'))
    console.log(chalk.white(`  💻 CPU核心数: ${this.cpuCount}`))
    console.log(chalk.white(`  ⚡ 并行进程数: ${report.optimization.parallelism}`))
    console.log(chalk.white(`  💾 缓存目录: ${this.buildCacheDir}`))
    console.log(chalk.white(`  🔄 增量构建: ${report.optimization.incrementalBuild ? '启用' : '禁用'}`))

    return report
  }
}

class CSSOptimizer {
  constructor (projectRoot = process.cwd()) {
    this.projectRoot = projectRoot
    this.distPath = path.join(projectRoot, 'dist')
    this.cssPath = path.join(this.distPath, 'static/css')
  }

  // 优化webpack配置以支持CSS代码分割
  optimizeWebpackConfig (webpackConfig) {
    log.config('优化CSS配置以确保Vue组件样式正确提取...')

    // 克隆配置避免修改原对象
    const optimizedConfig = { ...webpackConfig }

    if (optimizedConfig.plugins) {
      optimizedConfig.plugins = optimizedConfig.plugins.map(plugin => {
        // 修复ExtractTextPlugin配置以确保Vue组件scoped样式正确提取
        if (plugin.constructor.name === 'ExtractTextPlugin') {
          log.config('修复ExtractTextPlugin配置 - 确保Vue组件样式提取')
          log.config('关键修复: allChunks=true, ignoreOrder=false')

          const ExtractTextPlugin = require('extract-text-webpack-plugin')

          // 修复关键配置：allChunks必须为true以确保Vue组件样式被提取
          const fixedPlugin = new ExtractTextPlugin({
            filename: 'static/css/[name].[contenthash:8].css',
            allChunks: true, // 修复：设置为true确保所有chunk的CSS都被提取，包括Vue组件的scoped样式
            ignoreOrder: false // 修复：保持CSS顺序以避免样式覆盖问题
          })

          log.success('ExtractTextPlugin已修复 - Vue组件scoped样式现在应该能正确提取')
          return fixedPlugin
        }

        // 优化OptimizeCSSPlugin配置
        if (plugin.constructor.name === 'OptimizeCSSPlugin' || plugin.constructor.name === 'OptimizeCssAssetsPlugin') {
          log.config('优化CSS压缩配置')

          try {
            const OptimizeCSSPlugin = require('optimize-css-assets-webpack-plugin')
            return new OptimizeCSSPlugin({
              cssProcessorOptions: {
                safe: true,
                autoprefixer: { disable: true },
                mergeLonghand: false,
                discardComments: {
                  removeAll: true
                }
              },
              canPrint: false
            })
          } catch (error) {
            console.warn(chalk.yellow('⚠️  无法优化CSS压缩插件，使用原配置'))
            return plugin
          }
        }

        return plugin
      })
    }

    // 修复：移除可能干扰Vue组件样式提取的CSS分割逻辑
    // Vue组件的scoped样式需要与组件代码保持在同一个chunk中才能正确提取

    // 不添加额外的CSS CommonsChunkPlugin，避免干扰Vue组件样式提取
    log.config('跳过CSS chunk分割以确保Vue组件scoped样式正确提取')

    // 确保vue-loader能够正确处理scoped样式
    if (optimizedConfig.module && optimizedConfig.module.rules) {
      optimizedConfig.module.rules = optimizedConfig.module.rules.map(rule => {
        // 确保vue-loader规则保持原始配置
        if (rule.test && rule.test.toString().includes('\\.vue')) {
          log.config('保持vue-loader原始配置以确保scoped样式提取')
          return rule // 保持vue-loader配置不变
        }
        return rule
      })
    }

    log.success('CSS配置修复完成 - Vue组件样式提取已优化')
    return optimizedConfig
  }

  // 分析现有CSS文件
  async analyzeCSSFiles () {
    console.log(chalk.blue('🔍 分析现有CSS文件...'))

    if (!fs.existsSync(this.cssPath)) {
      console.log(chalk.yellow('⚠️  CSS目录不存在，跳过分析'))
      return { files: [], totalSize: 0 }
    }

    const cssFiles = []
    const files = fs.readdirSync(this.cssPath)

    for (const file of files) {
      if (file.endsWith('.css')) {
        const filePath = path.join(this.cssPath, file)
        const stats = fs.statSync(filePath)
        const sizeInMB = (stats.size / 1024 / 1024).toFixed(2)

        cssFiles.push({
          name: file,
          path: filePath,
          size: stats.size,
          sizeFormatted: `${sizeInMB} MB`
        })

        console.log(chalk.white(`  📄 ${file}: ${sizeInMB} MB`))
      }
    }

    const totalSize = cssFiles.reduce((sum, file) => sum + file.size, 0)
    const totalSizeMB = (totalSize / 1024 / 1024).toFixed(2)

    console.log(chalk.cyan(`📊 总计: ${cssFiles.length} 个文件, ${totalSizeMB} MB`))

    return { files: cssFiles, totalSize }
  }

  // 生成CSS优化报告
  async generateOptimizationReport () {
    const analysis = await this.analyzeCSSFiles()

    const report = {
      timestamp: new Date().toISOString(),
      analysis: {
        fileCount: analysis.files.length,
        totalSize: analysis.totalSize,
        totalSizeFormatted: `${(analysis.totalSize / 1024 / 1024).toFixed(2)} MB`,
        files: analysis.files.map(f => ({
          name: f.name,
          size: f.size,
          sizeFormatted: f.sizeFormatted
        }))
      },
      recommendations: this.generateRecommendations(analysis),
      optimizations: {
        extractTextPlugin: 'allChunks设置为false以启用代码分割',
        cssMinification: '启用高级CSS压缩选项',
        chunkSplitting: '添加CSS相关的公共chunk分割'
      }
    }

    const reportPath = path.join(this.projectRoot, 'css-optimization-report.json')
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2))

    console.log(chalk.gray(`📄 CSS优化报告已保存到: ${reportPath}`))
    return report
  }

  generateRecommendations (analysis) {
    const recommendations = []

    // 检查大文件
    const largeFiles = analysis.files.filter(f => f.size > 1024 * 1024) // > 1MB
    if (largeFiles.length > 0) {
      recommendations.push({
        type: 'critical',
        message: `发现 ${largeFiles.length} 个大型CSS文件 (>1MB)`,
        action: '建议启用CSS代码分割和按需加载',
        files: largeFiles.map(f => f.name)
      })
    }

    // 检查总大小
    if (analysis.totalSize > 5 * 1024 * 1024) { // > 5MB
      recommendations.push({
        type: 'critical',
        message: `CSS总大小过大: ${(analysis.totalSize / 1024 / 1024).toFixed(2)} MB`,
        action: '强烈建议实施CSS代码分割和Tree Shaking'
      })
    }

    // 检查文件数量
    if (analysis.files.length === 1 && analysis.totalSize > 1024 * 1024) {
      recommendations.push({
        type: 'warning',
        message: '所有CSS被合并到单个文件中',
        action: '建议拆分为多个较小的文件以提高加载性能'
      })
    }

    return recommendations
  }

  async optimize () {
    console.log(chalk.blue('🎨 开始CSS深度优化...'))

    try {
      // 1. 分析现有CSS文件
      const analysis = await this.analyzeCSSFiles()

      // 2. 生成优化报告
      const report = await this.generateOptimizationReport()

      // 3. 显示优化建议
      console.log('')
      console.log(chalk.cyan('📊 CSS优化分析报告'))
      console.log('==================================================')
      console.log(chalk.white(`📦 CSS文件数量: ${analysis.files.length}`))
      console.log(chalk.white(`📏 总大小: ${(analysis.totalSize / 1024 / 1024).toFixed(2)} MB`))

      if (report.recommendations.length > 0) {
        console.log('')
        console.log(chalk.yellow('⚠️  优化建议:'))
        report.recommendations.forEach((rec, index) => {
          const icon = rec.type === 'critical' ? '🔴' : '🟡'
          console.log(chalk.white(`  ${icon} ${index + 1}. ${rec.message}`))
          console.log(chalk.gray(`     ${rec.action}`))
        })
      }

      console.log('')
      console.log(chalk.green('✅ CSS优化分析完成！'))
      console.log(chalk.cyan('💡 建议: 在webpack配置中应用CSS代码分割优化'))

      return report
    } catch (error) {
      console.error(chalk.red('❌ CSS优化失败:'), error.message)
      throw error
    }
  }
}

class JSOptimizer {
  constructor (projectRoot = process.cwd()) {
    this.projectRoot = projectRoot
    this.distPath = path.join(projectRoot, 'dist')
    this.jsPath = path.join(this.distPath, 'static/js')
    this.packageJson = this.loadPackageJson()
  }

  loadPackageJson () {
    try {
      return require(path.join(this.projectRoot, 'package.json'))
    } catch (error) {
      console.warn(chalk.yellow('⚠️  无法加载package.json'))
      return { dependencies: {}, devDependencies: {} }
    }
  }

  // 优化webpack配置以支持JavaScript代码分割
  optimizeWebpackConfig (webpackConfig) {
    log.config('优化JavaScript配置以支持代码分割...')

    // 克隆配置避免修改原对象
    const optimizedConfig = { ...webpackConfig }

    // 1. 优化CommonsChunkPlugin配置
    optimizedConfig.plugins = this.optimizeCommonsChunkPlugins(optimizedConfig.plugins || [])

    // 2. 添加性能配置
    optimizedConfig.performance = this.getPerformanceConfig(optimizedConfig.performance)

    // 3. 优化resolve配置以减少包大小
    optimizedConfig.resolve = this.optimizeResolveConfig(optimizedConfig.resolve || {})

    log.success('JavaScript配置优化完成')
    return optimizedConfig
  }

  optimizeCommonsChunkPlugins (plugins) {
    log.config('应用保守的CommonsChunkPlugin配置 (紧急性能修复)')

    // 移除现有的CommonsChunkPlugin，用保守版本替换
    const filteredPlugins = plugins.filter(plugin =>
      plugin.constructor.name !== 'CommonsChunkPlugin'
    )

    try {
      const webpack = require('webpack')

      // 紧急修复：使用更保守的代码分割策略，减少文件数量

      // 1. 只提取所有第三方库到单个vendor chunk
      filteredPlugins.push(
        new webpack.optimize.CommonsChunkPlugin({
          name: 'vendor',
          chunks: ['app'],
          minChunks: function (module) {
            // 只分离node_modules中的模块，避免过度分割
            return module.resource &&
                   /node_modules/.test(module.resource)
          }
        })
      )

      // 2. 提取webpack运行时到manifest
      filteredPlugins.push(
        new webpack.optimize.CommonsChunkPlugin({
          name: 'manifest',
          chunks: ['vendor'],
          minChunks: Infinity
        })
      )

      log.success('已配置保守的代码分割策略 (减少文件数量)')
    } catch (error) {
      console.warn(chalk.yellow('⚠️  无法优化CommonsChunkPlugin:'), error.message)
      return plugins
    }

    return filteredPlugins
  }

  // 识别大型库（通常>500KB）
  isLargeLibrary (resourcePath) {
    const largeLibraries = [
      'element-ui', 'ant-design', 'vuetify', 'bootstrap',
      'echarts', 'three', 'moment', 'lodash', 'rxjs',
      'core-js', 'regenerator-runtime'
    ]

    return largeLibraries.some(lib => resourcePath.includes(lib))
  }

  // 识别中等大小库（通常100-500KB）
  isMediumLibrary (resourcePath) {
    const mediumLibraries = [
      'axios', 'vue-router', 'vuex', 'dayjs',
      'crypto-js', 'qs', 'uuid', 'validator'
    ]

    return mediumLibraries.some(lib => resourcePath.includes(lib))
  }

  getPerformanceConfig (existingConfig) {
    return {
      ...existingConfig,
      hints: 'warning',
      maxAssetSize: 1024 * 1024, // 1MB
      maxEntrypointSize: 2 * 1024 * 1024, // 2MB
      assetFilter: function (assetFilename) {
        return assetFilename.endsWith('.js') || assetFilename.endsWith('.css')
      }
    }
  }

  optimizeResolveConfig (existingResolve) {
    // 保守的resolve优化，避免破坏现有模块解析
    const optimizedResolve = {
      ...existingResolve
    }

    // 只在确保不会破坏现有依赖的情况下添加别名
    if (!optimizedResolve.alias) {
      optimizedResolve.alias = {}
    }

    // 保守地添加别名，只针对确实存在的包
    try {
      // 检查是否有moment包，如果有则可以考虑替换为dayjs
      require.resolve('moment')
      require.resolve('dayjs')
      console.log(chalk.blue('🔧 添加moment -> dayjs别名优化'))
      optimizedResolve.alias['moment'] = 'dayjs'
    } catch (e) {
      // 如果包不存在，跳过别名设置
    }

    // 优先使用ES模块版本以支持tree shaking（保守设置）
    if (!optimizedResolve.mainFields) {
      optimizedResolve.mainFields = ['browser', 'module', 'main']
    }

    return optimizedResolve
  }

  // 分析现有JavaScript文件
  async analyzeJSFiles () {
    console.log(chalk.blue('🔍 分析JavaScript文件结构...'))

    if (!fs.existsSync(this.jsPath)) {
      console.log(chalk.yellow('⚠️  JavaScript目录不存在，跳过分析'))
      return { files: [], totalSize: 0 }
    }

    const jsFiles = []
    const files = fs.readdirSync(this.jsPath)

    for (const file of files) {
      if (file.endsWith('.js')) {
        const filePath = path.join(this.jsPath, file)
        const stats = fs.statSync(filePath)
        const sizeInMB = (stats.size / 1024 / 1024).toFixed(2)

        const fileInfo = {
          name: file,
          path: filePath,
          size: stats.size,
          sizeFormatted: `${sizeInMB} MB`,
          type: this.categorizeJSFile(file),
          isOverLimit: stats.size > 1024 * 1024 // >1MB
        }

        jsFiles.push(fileInfo)

        const statusIcon = fileInfo.isOverLimit ? '🔴' : fileInfo.size > 512 * 1024 ? '🟡' : '✅'
        console.log(chalk.white(`  ${statusIcon} ${file}: ${sizeInMB} MB (${fileInfo.type})`))
      }
    }

    const totalSize = jsFiles.reduce((sum, file) => sum + file.size, 0)
    const totalSizeMB = (totalSize / 1024 / 1024).toFixed(2)
    const overLimitFiles = jsFiles.filter(f => f.isOverLimit)

    console.log(chalk.cyan(`📊 总计: ${jsFiles.length} 个文件, ${totalSizeMB} MB`))
    if (overLimitFiles.length > 0) {
      console.log(chalk.red(`  ⚠️  发现 ${overLimitFiles.length} 个超大JavaScript文件 (>1MB)`))
    }

    return { files: jsFiles, totalSize, overLimitFiles }
  }

  categorizeJSFile (filename) {
    if (filename.includes('vendor')) return 'vendor'
    if (filename.includes('app')) return 'app'
    if (filename.includes('manifest')) return 'manifest'
    if (filename.includes('common')) return 'common'
    if (/^\d+\./.test(filename)) return 'async-chunk'
    return 'unknown'
  }

  // 生成JavaScript优化建议
  generateOptimizationRecommendations (analysis) {
    const recommendations = []

    // 检查超大文件（安全检查）
    if (analysis.overLimitFiles && analysis.overLimitFiles.length > 0) {
      analysis.overLimitFiles.forEach(file => {
        if (file.type === 'vendor') {
          recommendations.push({
            priority: 'critical',
            type: 'vendor-splitting',
            file: file.name,
            currentSize: file.sizeFormatted,
            issue: `Vendor包过大 (${file.sizeFormatted})`,
            solution: '实施多层级vendor分割策略',
            expectedImprovement: '将vendor包拆分为3-4个较小的包',
            implementation: 'CommonsChunkPlugin多实例配置'
          })
        } else if (file.type === 'async-chunk') {
          recommendations.push({
            priority: 'high',
            type: 'async-optimization',
            file: file.name,
            currentSize: file.sizeFormatted,
            issue: `异步块过大 (${file.sizeFormatted})`,
            solution: '优化动态导入和路由分割',
            expectedImprovement: '减少初始加载时间',
            implementation: '路由级别的代码分割'
          })
        } else if (file.type === 'app') {
          recommendations.push({
            priority: 'medium',
            type: 'app-optimization',
            file: file.name,
            currentSize: file.sizeFormatted,
            issue: `应用包过大 (${file.sizeFormatted})`,
            solution: '提取更多公共代码到vendor',
            expectedImprovement: '减少应用包大小',
            implementation: '调整minChunks参数'
          })
        }
      })

      // 检查总大小
      if (analysis.totalSize > 5 * 1024 * 1024) { // >5MB
        recommendations.push({
          priority: 'warning',
          type: 'total-size',
          issue: `JavaScript总大小过大: ${(analysis.totalSize / 1024 / 1024).toFixed(2)} MB`,
          solution: '实施全面的代码分割和tree shaking',
          expectedImprovement: '显著减少初始加载时间',
          implementation: '多维度优化策略'
        })
      }

      // 添加通用优化建议
      recommendations.push({
        priority: 'medium',
        type: 'tree-shaking',
        issue: '可能存在未使用的代码',
        solution: '启用tree shaking和dead code elimination',
        expectedImprovement: '10-30%包大小减少',
        implementation: 'ES模块和UglifyJS优化'
      })

      return recommendations
    }
  }

  async optimize () {
    console.log(chalk.blue('📦 开始JavaScript深度优化...'))

    try {
      // 1. 分析现有JavaScript文件
      const analysis = await this.analyzeJSFiles()

      // 2. 生成优化建议
      const recommendations = this.generateOptimizationRecommendations(analysis)

      // 3. 生成优化报告
      const report = await this.generateJSOptimizationReport(analysis, recommendations)

      // 4. 显示优化建议
      this.displayOptimizationSummary(analysis, recommendations)

      return report
    } catch (error) {
      console.error(chalk.red('❌ JavaScript优化失败:'), error.message)
      throw error
    }
  }

  async generateJSOptimizationReport (analysis, recommendations) {
    const report = {
      timestamp: new Date().toISOString(),
      analysis: {
        fileCount: analysis.files.length,
        totalSize: analysis.totalSize,
        totalSizeFormatted: `${(analysis.totalSize / 1024 / 1024).toFixed(2)} MB`,
        overLimitFiles: analysis.overLimitFiles.length,
        files: analysis.files.map(f => ({
          name: f.name,
          size: f.size,
          sizeFormatted: f.sizeFormatted,
          type: f.type,
          isOverLimit: f.isOverLimit
        }))
      },
      recommendations: recommendations,
      optimizations: {
        vendorSplitting: '多层级vendor包分割',
        asyncOptimization: '异步块大小优化',
        performanceHints: '包大小警告配置',
        treeShaking: 'ES模块优化支持'
      },
      dependencies: {
        production: Object.keys(this.packageJson.dependencies || {}),
        development: Object.keys(this.packageJson.devDependencies || {}),
        heavyDependencies: this.identifyHeavyDependencies()
      }
    }

    const reportPath = path.join(this.projectRoot, 'js-optimization-report.json')
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2))

    console.log(chalk.gray(`📄 JavaScript优化报告已保存到: ${reportPath}`))
    return report
  }

  identifyHeavyDependencies () {
    const dependencies = Object.keys(this.packageJson.dependencies || {})
    const heavyPackages = [
      'element-ui', 'ant-design-vue', 'vuetify', 'bootstrap',
      'echarts', 'three', 'moment', 'lodash', 'rxjs',
      'core-js', 'regenerator-runtime', 'babel-polyfill'
    ]

    return dependencies.filter(dep =>
      heavyPackages.some(heavy => dep.includes(heavy))
    )
  }

  displayOptimizationSummary (analysis, recommendations) {
    console.log('')
    console.log(chalk.cyan('📊 JavaScript优化分析报告'))
    console.log('==================================================')
    console.log(chalk.white(`📦 JavaScript文件数量: ${analysis.files.length}`))
    console.log(chalk.white(`📏 总大小: ${(analysis.totalSize / 1024 / 1024).toFixed(2)} MB`))
    console.log(chalk.white(`🔴 超大文件数量: ${analysis.overLimitFiles.length} (>1MB)`))

    if (analysis.overLimitFiles.length > 0) {
      console.log('')
      console.log(chalk.red('🚨 超大文件详情:'))
      analysis.overLimitFiles.forEach((file, index) => {
        console.log(chalk.white(`  ${index + 1}. ${file.name}: ${file.sizeFormatted} (${file.type})`))
      })
    }

    if (recommendations.length > 0) {
      console.log('')
      console.log(chalk.yellow('💡 优化建议:'))
      recommendations.forEach((rec, index) => {
        const priorityIcon = rec.priority === 'critical' ? '🔴'
          : rec.priority === 'high' ? '🟡' : '📈'
        console.log(chalk.white(`  ${priorityIcon} ${index + 1}. ${rec.issue}`))
        console.log(chalk.gray(`     解决方案: ${rec.solution}`))
        console.log(chalk.cyan(`     预期改善: ${rec.expectedImprovement}`))
      })
    }

    console.log('')
    console.log(chalk.green('✅ JavaScript优化分析完成！'))
    console.log(chalk.cyan('💡 建议: 在webpack配置中应用JavaScript代码分割优化'))
  }
}

module.exports = {
  PerformanceAnalyzer,
  ParallelBuildOptimizer,
  CSSOptimizer,
  JSOptimizer
}
