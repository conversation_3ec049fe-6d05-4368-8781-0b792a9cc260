<template>
  <div>
    <el-dialog
      :title="title"
      :visible.sync="visible"
      :close-on-click-modal="false" :before-close="handleClose"
      width="95%"
      @open="handleOpen">
      <div class="main">
        <div class="deliveryInfo">
          <el-form ref="form" :model="form" :rules="rules" label-width="80px" size="mini" label-suffix=":" label-position="top">
            <el-form-item label="物流单号" prop="materialNumber">
              <el-input v-model="form.materialNumber" ref="materialNumber" clearable placeholder="请输入" max="20"></el-input>
            </el-form-item>
            <el-form-item label="物流公司" prop="logisticsCompany">
              <el-select v-model="form.logisticsCompany" clearable placeholder="请选择" style="width: 100%;">
                <el-option
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                  v-for="item in logisticsCompanyList">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="样本属性" prop="sampleAttributes">
              <el-select v-model="form.sampleAttributes" clearable placeholder="请选择" style="width: 100%;">
                <el-option
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                  v-for="item in sampleAttributesList">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="包裹温度" prop="packageTemperature">
              <el-input v-model.trim="form.packageTemperature" maxlength="10" clearable placeholder="请输入"></el-input>
            </el-form-item>
            <el-form-item label="异常描述" prop="exceptionRemark">
              <el-input v-model.trim="form.exceptionRemark" maxlength="1000" clearable placeholder="请输入"></el-input>
            </el-form-item>
            <el-form-item label="样本明细" prop="sampleDetails">
              <el-input v-model.trim="form.sampleDetails" clearable placeholder="请输入" @keyup.enter.native="handleAddSampleCodeList"></el-input>
            </el-form-item>
            <el-form-item label="">
              <div style="border: 1px solid #dcdfe6; border-radius: 5px;height: 150px; overflow: auto;">
                <template v-for="(item, index) in sampleCodeList">
                  <div :key="index" class="item">{{item}}<i class="el-icon-close" style="cursor: pointer;" @click="handleDelete(index)"></i></div>
                </template>
              </div>
            </el-form-item>
          </el-form>
        </div>
        <div class="picture">
          <div class="pictureList">
            <div class="title">样本图片</div>
            <div class="content">
              <scroll-pane style="height: 100%;width: 100%;">
                <template v-for="(item, index) in pictureList">
                  <div :key="'picture-' + index" class="pictureItem">
                    <el-image :src="item.fileAbsolutePath || item.base64" :preview-src-list="previewList" style="display: inline-block; height: 100%; width: 100%;">
                      <div slot="placeholder" class="image-slot">
                        加载中<span class="dot">...</span>
                      </div>
                    </el-image>
                    <span class="deleteIcon"  @click="handleDeletePicture(index)">
                      <i v-if="type !== 2" class="el-icon-delete"></i>
                    </span>
                    <!--<icon-svg class="deleteIcon" icon-class="icon-delete" @click.native="handleDeletePicture(index)"></icon-svg>-->
                  </div>
                </template>
              </scroll-pane>
            </div>
          </div>
          <div class="camera">
            <div class="title">拍摄</div>
            <div class="content">
              <div class="photo">
                <div ref="cameraCtl" style="width: 593px; height: 353px;margin: 0 auto"></div>
                <!--<img id="CameraPhoto" :src="src" style="display: inline-block; width: 100%;height: 100%;" alt="失败">-->
              </div>
              <div class="operation">
                <el-button :disabled="type === 2" type="primary" size="mini" @click="handleGetImage">拍摄</el-button>
                <br/>
                <el-button :disabled="type === 2" type="primary" size="mini" @click="zoomIn">放大</el-button>
                <br/>
                <el-button :disabled="type === 2" type="primary" size="mini" @click="zoomOut">缩小</el-button>
              </div>
            </div>
          </div>
        </div>
        <div class="list">
          <div class="list-title">最近更新</div>
          <div class="list-content">
            <div :key="item.id" v-for="item in expressList" class="list-item">
              <p class="list-item-code">{{item.materialNumber}}</p>
              <p class="list-item-time">{{item.recordTime}}</p>
            </div>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <template v-if="type !== 2">
          <el-button :loading="loading" size="mini" @click="handleClose">取 消</el-button>
          <el-button :loading="loading" v-if="type === 0" type="primary" size="mini" @click="handleConfirm(0)">保存并继续</el-button>
          <el-button :loading="loading" type="primary" size="mini" @click="handleConfirm(1)">保存并关闭</el-button>
        </template>
        <template v-else>
          <el-button size="mini" @click="handleClose">关 闭</el-button>
        </template>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import mixins from '../../../util/mixins'
export default {
  name: 'expressDeliveryManagementSaveDialog',
  mixins: [mixins.dialogBaseInfo],
  components: {},
  props: {
    pid: {
      type: [Number, String],
      default: '',
      required: true
    },
    ptype: {
      type: Number,
      default: 0,
      required: true
    }
  },
  mounted () {
    this.$once('hook:beforeDestroy', () => {
      if (this.isSocketConnect) {
        this.close()
      }
    })
  },
  watch: {},
  computed: {},
  data () {
    return {
      loading: false,
      type: 0, // 0:新增，1: 编辑，2: 查看
      id: '',
      title: '',
      rules: {
        packageTemperature: [
          {required: true, message: '请输入包裹温度', trigger: 'blur'},
          {pattern: /^[+-]?\d+(\.\d+)?$/, message: '只能填写正负数，包括小数', trigger: 'blur'}
        ],
        materialNumber: [
          {required: true, message: '请输入物流单号', trigger: 'blur'}
        ],
        logisticsCompany: [
          {required: true, message: '请选择物流公司', trigger: ['blur', 'change']}
        ],
        sampleAttributes: [
          {required: true, message: '请选择样本属性', trigger: ['blur', 'change']}
        ]
      },
      form: {
        materialNumber: '',
        logisticsCompany: '',
        sampleDetails: '',
        exceptionRemark: ''
      },
      sampleCodeList: [],
      logisticsCompanyList: [
        {
          value: '顺丰快递',
          label: '顺丰快递'
        },
        {
          value: '中铁快运',
          label: '中铁快运'
        },
        {
          value: '生生物流',
          label: '生生物流'
        },
        {
          value: '城市映急',
          label: '城市映急'
        },
        {
          value: '中集冷云',
          label: '中集冷云'
        },
        {
          value: '48同城',
          label: '48同城'
        },

        {
          value: '闪送',
          label: '闪送'
        },
        {
          value: '自送样',
          label: '自送样'
        },
        {
          value: '其他',
          label: '其他'
        }
      ],
      sampleAttributesList: [
        {
          value: '科研',
          label: '非自动'
        },
        {
          value: '临床',
          label: '自动'
        },
        // {
        //   value: '科研+临床',
        //   label: '科研+临床'
        // },
        {
          value: '测序工厂',
          label: '测序工厂'
        }, {
          value: '药厂',
          label: '药厂'
        }
      ],
      pictureList: [], // 图片列表
      previewList: [], // 预览图片
      expressList: [], // 快递列表
      // 高拍仪参数
      getImageLoading: false,
      src: '',
      socket: null,
      isSocketConnect: false,
      lockReconnect: false,
      openFlagA: false,
      isOpenMainCamera: false,
      MainCanvas: '',
      MainContext: '',
      pMainShowStartX: 0,
      pMainShowStartY: 0,
      isMouseDown: false,
      pALastX: 0,
      pALastY: 0,
      pACurrentX: 0,
      pACurrentY: 0,
      MainCamCutMode: 0
    }
  },
  methods: {
    handleOpen () {
      this.$nextTick(() => {
        this.$refs.materialNumber.focus()
        this.$refs.cameraCtl.innerHTML = ''
        this.id = this.pid
        this.type = this.ptype
        if (this.type !== 2) {
          this.webSocketConnect()
          this.initCanvas(this.$refs.cameraCtl, 0, 0, 650, 353)
        }
        switch (this.type) {
          case 0:
            this.title = '新增包裹'
            break
          case 1:
            this.title = '编辑包裹'
            this.getData()
            break
          case 2:
            this.title = '查看包裹'
            this.getData()
            break
        }
        this.getCurrentExpressLits()
        this.form = {
          materialNumber: '',
          logisticsCompany: '',
          sampleDetails: '',
          exceptionRemark: ''
        }
        this.$refs.form.resetFields()
        this.sampleCodeList = []
        this.pictureList = []
        // this.previewList = []
      })
    },
    // 获取当前列表
    getCurrentExpressLits () {
      this.$ajax({
        loadingDom: '.list',
        url: '/package/get_package_samples',
        data: {
          page: {
            current: 1,
            size: 15
          }
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.expressList = []
          let rows = result.data.rows || []
          rows.forEach(v => {
            let item = {
              id: v.id,
              materialNumber: v.materialNumber,
              recordTime: v.recordTime
            }
            this.expressList.push(item)
          })
        } else {
          this.$message.error(result.message)
        }
      })
    },
    handleClose () {
      if (this.isSocketConnect) {
        this.close()
      }
      this.$emit('saveDialogCloseEvent')
    },
    handleConfirm (type) {
      this.$refs.form.validate(valid => {
        if (valid) {
          let multipartFiles = []
          let pics = []
          this.pictureList.forEach(v => {
            if (v.base64) {
              multipartFiles.push(v.file)
            } else {
              pics.push({
                fileAbsolutePath: v.fileAbsolutePath,
                fileName: v.fileName,
                group: v.group,
                path: v.path
              })
            }
          })
          this.loading = true
          this.$ajax({
            url: '/package/save_package_sample',
            isFormData: true,
            data: {
              id: this.id,
              packageTemperature: this.form.packageTemperature || '',
              exceptionRemark: this.form.exceptionRemark || '',
              materialNumber: this.form.materialNumber.trim() || '',
              logisticsCompany: this.form.logisticsCompany || '',
              sampleAttributes: this.form.sampleAttributes || '',
              sampleDetails: this.sampleCodeList.toString() || '',
              multipartFiles: multipartFiles || '',
              pics: pics.length > 0 ? JSON.stringify(pics) : ''
            }
          }).then(result => {
            if (result.code === this.SUCCESS_CODE) {
              this.$message.success('保存成功')
              if (type === 0) {
                this.getCurrentExpressLits()
                this.form = {
                  materialNumber: '',
                  logisticsCompany: '',
                  sampleDetails: ''
                }
                this.$refs.form.resetFields()
                this.sampleCodeList = []
                this.pictureList = []
                this.$refs.materialNumber.focus()
              } else {
                if (this.isSocketConnect) {
                  this.close()
                }
                this.handleClose()
              }
            } else {
              this.$message.error(result.message)
            }
          }).finally(() => {
            this.loading = false
          })
        }
      })
    },
    getData () {
      this.$ajax({
        url: '/package/get_package_samples_data',
        method: 'get',
        data: {
          id: this.id
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          let data = result.data
          this.form = {
            materialNumber: data.materialNumber,
            logisticsCompany: data.logisticsCompany,
            sampleAttributes: data.sampleAttributes,
            packageTemperature: data.packageTemperature,
            exceptionRemark: data.exceptionRemark,
            sampleDetails: ''
          }
          this.sampleCodeList = data.sampleDetails.split(',')
          this.pictureList = data.fileInfo || []
          this.previewList = this.pictureList.map(v => v.fileAbsolutePath)
        } else {
          this.$message.error(result.message)
        }
      })
    },
    initCanvas (DivMainBox, mX, mY, mwidth, mheight) {
      if (this.MainCanvas) {
        this.MainCanvas = ''
      }
      // var DivMainBox = $("CameraCtl");
      if (mwidth !== 0 && mheight !== 0) {
        this.MainCanvas = document.createElement('canvas')
        // this.MainCanvas.style.border = 'solid 1px #A0A0A0'
        this.MainCanvas.id = 'MainCamCanvas'
        this.MainCanvas.width = mwidth
        this.MainCanvas.height = mheight
        this.MainContext = this.MainCanvas.getContext('2d')
        DivMainBox.appendChild(this.MainCanvas) // 添加画布
        // MainCanvas.onmousedown = MainCanvasMouseDown
        // MainCanvas.onmousemove = MainCanvasMouseMove
        // MainCanvas.onmouseup = MainCanvasMouseUp
        // MainCanvas.onmouseout = MainCanvasMouseOut
        // addEvent(MainCanvas, 'mousewheel', onMouseWheel)
        // addEvent(MainCanvas, 'DOMMouseScroll', onMouseWheel)
        // this.TrueSize()
      }
    },
    webSocketConnect () {
      this.socket = new WebSocket('ws://localhost:22225')
      this.socket.binaryType = 'arraybuffer'
      try {
        this.socket.onopen = (event) => {
          // heartCheck.reset().start();
          this.isSocketConnect = true
          // if (isOpenMainCamera === false)
          // this.Cam_getDevCount()
          console.log('socket.onopen')
          this.openCamera()
          this.setFileType(1)
        }

        this.socket.onclose = (event) => {
          console.log('socket.onclose')
          // this.reconnect()
          this.isSocketConnect = false
          // $('TextInfo').value = 'websocket connect close!'
        }

        this.socket.onerror = (event) => {
          console.log(event)
          console.log('socket.onclose')
          this.isSocketConnect = false
          this.reconnect()
          // $('TextInfo').value = 'websocket connect error!'
        }

        this.socket.onmessage = (event) => {
          // heartCheck.reset().start();
          let rDataArr = new Uint8Array(event.data)
          if (rDataArr.length > 0) {
            if (rDataArr[0] === 0x55 && rDataArr[1] === 0x66) {
              // 摄像头数目返回
              if (rDataArr[2] === 0x50) {
                let devCount = rDataArr[3]
                let devNameBufLen = rDataArr.length - 4
                let devNameData = new Uint8Array(devNameBufLen)
                for (let i = 0; i < devNameBufLen; i++) {
                  devNameData[i] = rDataArr[4 + i]
                }
                // let AllCamName = uint8ArrayToString(devNameData);
                let str = this.byteToString(devNameData)
                let AllCamName = decodeURIComponent(str)
                let camName = []
                camName = AllCamName.split('|')
                this.getDevCountAndNameResultCB(devCount, camName)
              }
              //
              // // 摄像头分辨率返回
              // if (rDataArr[2] === 0x51) {
              //   let resCount = rDataArr[3]
              //   let resArr = []
              //   for (let i = 0; i < resCount; i++) {
              //     let width = rDataArr[4 + i * 4 + 0] * 256 + rDataArr[4 + i * 4 + 1]
              //     let height = rDataArr[4 + i * 4 + 2] * 256 + rDataArr[4 + i * 4 + 3]
              //     let resStr = '' + width + '*' + height
              //     resArr.push(resStr)
              //   }
              //   GetResolutionResultCB(resCount, resArr)
              // }
              //
              // 摄像头开启状态返回
              if (rDataArr[2] === 0x01) {
                if (rDataArr[3] === 0x01) {
                  this.isOpenMainCamera = true
                  this.getCameraOnOffStatus(0)
                }
                if (rDataArr[3] === 0x03) {
                  this.isOpenMainCamera = false
                  this.getCameraOnOffStatus(1)
                }
              }
              //
              // if (rDataArr[2] === 0xA5) {
              //   let flag
              //   if (rDataArr[3] === 0x00) {
              //     flag = 2
              //   }
              //   if (rDataArr[3] === 0x01) {
              //     flag = 0
              //   }
              //   if (flag === 0) {
              //     let ResultStr = ''
              //     let pDataLen = rDataArr.length - 4
              //     if (pDataLen > 0) {
              //       let pData = new Uint8Array(pDataLen)
              //       for (let i = 0; i < pDataLen; i++) {
              //         pData[i] = rDataArr[4 + i]
              //       }
              //       let str = byteToString(pData)
              //       console.log(str)
              //       ResultStr = decodeURIComponent(str)
              //     }
              //     console.log(ResultStr)
              //   }
              // }
              //
              // 当前拍照状态返回
              // if (rDataArr[2] === 0x10) {
              //   console.log(rDataArr[3]);
              //   GetCaptureStatusResultCB(rDataArr[3]);
              // }

              // 拍照结果返回
              if (rDataArr[2] === 0x10) {
                let flag
                if (rDataArr[3] === 0x01) {
                  flag = 0
                }
                if (rDataArr[3] === 0x02) {
                  flag = 2
                }
                let imgpathLen = rDataArr[4] * 256 + rDataArr[5]
                if (imgpathLen === 0) {
                  let base64Len = rDataArr[6] * 65536 + rDataArr[7] * 256 + rDataArr[8]
                  let imgPathStr = ''
                  let base64Data = new Uint8Array(base64Len)
                  for (let i = 0; i < base64Len; i++) {
                    base64Data[i] = rDataArr[9 + imgpathLen + i]
                  }
                  let base64Str = this.uint8ArrayToString(base64Data)
                  this.getCaptureImgResultCB(flag, imgPathStr, base64Str)
                } else {
                  let base64Len = rDataArr[6] * 65536 + rDataArr[7] * 256 + rDataArr[8]
                  let pData = new Uint8Array(imgpathLen)
                  for (let i = 0; i < imgpathLen; i++) {
                    pData[i] = rDataArr[9 + i]
                  }
                  let str = this.byteToString(pData)
                  let imgPathStr = decodeURIComponent(str)

                  let base64Data = new Uint8Array(base64Len)
                  for (let i = 0; i < base64Len; i++) {
                    base64Data[i] = rDataArr[9 + imgpathLen + i]
                  }
                  let base64Str = this.uint8ArrayToString(base64Data)

                  this.getCaptureImgResultCB(flag, imgPathStr, base64Str)
                }
              }

              // 身份证信息返回
              // if (rDataArr[2] === 0x80) {
              //   let flag
              //   if (rDataArr[3] === 0x01) {
              //     flag = 0
              //   }
              //   if (rDataArr[3] === 0x02) {
              //     flag = 2
              //   }
              //   let pDataLen = rDataArr.length - 4
              //   let pData = new Uint8Array(pDataLen)
              //   for (let i = 0; i < pDataLen; i++) {
              //     pData[i] = rDataArr[4 + i]
              //   }
              //   let str = byteToString(pData)
              //   let AllInfoStr = decodeURIComponent(str)
              //   let resultStr = []
              //   resultStr = AllInfoStr.split(';')
              //   // console.log(resultStr);
              //   GetIdCardInfoResultCB(flag, resultStr[0], resultStr[1], resultStr[2], resultStr[3], resultStr[4], resultStr[5], resultStr[6], resultStr[7], resultStr[8], resultStr[9])
              // }
              //
              // // 上传结果返回
              // if (rDataArr[2] === 0x90) {
              //   let flag
              //   if (rDataArr[3] === 0x01) {
              //     flag = 0
              //   }
              //   if (rDataArr[3] === 0x02) {
              //     flag = 2
              //   }
              //   let pDataLen = rDataArr.length - 6
              //   if (pDataLen <= 0) {
              //     HttpResultCB(flag, ResultStr)
              //   }
              //   let pData = new Uint8Array(pDataLen)
              //   for (let i = 0; i < pDataLen; i++) {
              //     pData[i] = rDataArr[6 + i]
              //   }
              //   let str = byteToString(pData)
              //   let ResultStr = decodeURIComponent(str)
              //   // console.log(ResultStr);
              //   HttpResultCB(flag, ResultStr)
              // }
              //
              // // 条码二维码识别结果返回
              // if (rDataArr[2] === 0x91) {
              //   let flag
              //   if (rDataArr[3] === 0x00) {
              //     flag = 2 // 未识别到内容
              //   }
              //   if (rDataArr[3] === 0x01) {
              //     flag = 0 // 识别到内容
              //   }
              //   let ResultStr = ''
              //   let pDataLen = rDataArr.length - 4
              //   if (flag === 0 && pDataLen > 0) {
              //     let pData = new Uint8Array(pDataLen)
              //     for (let i = 0; i < pDataLen; i++) {
              //       pData[i] = rDataArr[4 + i]
              //     }
              //     let str = byteToString(pData)
              //     console.log(str)
              //     ResultStr = decodeURIComponent(str)
              //   }
              //   console.log(ResultStr)
              //   QrBarCodeRecogResultCB(flag, ResultStr)
              // }
              //
              // // 添加合并PDF文件结果返回
              // if (rDataArr[2] === 0x31) {
              //   let flag
              //   let base64Len = rDataArr.length - 4
              //   let base64Str = ''
              //   if (base64Len > 0) {
              //     let base64Data = new Uint8Array(base64Len)
              //     for (let i = 0; i < base64Len; i++) {
              //       base64Data[i] = rDataArr[4 + i]
              //     }
              //     base64Str = uint8ArrayToString(base64Data)
              //   }
              //   if (rDataArr[3] === 0x01) {
              //     flag = 0
              //   }
              //   if (rDataArr[3] === 0x02) {
              //     flag = 2
              //   }
              //   AddImgFileToPDFResultCB(flag, base64Str)
              // }
              //
              // // 合并PDF结果返回
              // if (rDataArr[2] === 0x32) {
              //   let flag
              //   let base64Len = rDataArr.length - 4
              //   let base64Str = ''
              //   if (base64Len > 0) {
              //     let base64Data = new Uint8Array(base64Len)
              //     for (let i = 0; i < base64Len; i++) {
              //       base64Data[i] = rDataArr[4 + i]
              //     }
              //     base64Str = uint8ArrayToString(base64Data)
              //   }
              //   if (rDataArr[3] === 0x01) {
              //     flag = 0
              //   }
              //   if (rDataArr[3] === 0x02) {
              //     flag = 2
              //   }
              //   PdfCombineResultCB(flag, base64Str)
              // }
              //
              // // 获取驱动盘符
              // if (rDataArr[2] === 0xA6) {
              //   let strLen = rDataArr.length - 3
              //   if (strLen > 0) {
              //     let driveData = new Uint8Array(strLen)
              //     for (let i = 0; i < strLen; i++) {
              //       driveData[i] = rDataArr[3 + i]
              //     }
              //     let driveStr = uint8ArrayToString(driveData)
              //     GetDriveResultCB(driveStr)
              //   } else {
              //     GetDriveResultCB('')
              //   }
              // }

              // 摄像头数据
              if (rDataArr[2] === 0xcc) {
                let ww = rDataArr[3] * 256 + rDataArr[4]
                let hh = rDataArr[5] * 256 + rDataArr[6]
                this.pMainShowStartX = rDataArr[7] * 256 + rDataArr[8]
                this.pMainShowStartY = rDataArr[9] * 256 + rDataArr[10]
                this.MainContext.clearRect(0, 0, this.MainCanvas.width, this.MainCanvas.height)
                let imgData = this.MainContext.createImageData(ww, hh)
                let dataNum = 0
                dataNum = dataNum + 11
                for (let i = 0; i < imgData.data.length; i += 4) {
                  imgData.data[i] = rDataArr[dataNum]
                  imgData.data[i + 1] = rDataArr[dataNum + 1]
                  imgData.data[i + 2] = rDataArr[dataNum + 2]
                  imgData.data[i + 3] = 255
                  dataNum = dataNum + 3
                }
                this.MainContext.putImageData(imgData, 0, 0)

                if (this.MainCamCutMode === 1) {
                  this.MainContext.strokeStyle = 'red' // 设置线条的颜色
                  this.MainContext.lineWidth = 2 // 设置线条的宽度
                  this.MainContext.beginPath() // 绘制直线
                  this.MainContext.rect(this.pALastX, this.pALastY, (this.pACurrentX - this.pALastX), (this.pACurrentY - this.pALastY))
                  this.MainContext.closePath()
                  this.MainContext.stroke()
                }
              }
            }
          }
        }
      } catch (ex) {
        alert('异常错误!')
      }
    },
    handleAddSampleCodeList () {
      if (this.form.sampleDetails && this.sampleCodeList.indexOf(this.form.sampleDetails) === -1) {
        this.sampleCodeList.push(this.form.sampleDetails)
        this.$set(this.form, 'sampleDetails', '')
      }
    },
    handleDelete (index) {
      this.sampleCodeList.splice(index, 1)
    },
    handleGetImage () {
      if (this.isSocketConnect) {
        this.takePhoto()
      } else {
        this.$message.error('未连接')
        console.log('未连接')
      }
    },
    handlegetDevCount () {
      this.getDevCount()
    },
    reconnect () {
      if (this.lockReconnect) return
      this.lockReconnect = true
      setTimeout(() => {
        this.webSocketConnect()
        this.lockReconnect = false
        console.log('reconnect...')
      }, 2000)
    },
    stringToUint8Array (str) {
      let arr = []
      for (let i = 0, j = str.length; i < j; ++i) {
        arr.push(str.charCodeAt(i))
      }
      arr.push('\0')
      let tmpUint8Array = new Uint8Array(arr)
      return tmpUint8Array
    },
    uint8ArrayToString (fileData) {
      let dataString = ''
      for (let i = 0; i < fileData.length; i++) {
        dataString += String.fromCharCode(fileData[i])
      }
      return dataString
    },
    stringToByte (str) {
      let bytes = []
      let len, c
      len = str.length
      for (let i = 0; i < len; i++) {
        c = str.charCodeAt(i)
        if (c >= 0x010000 && c <= 0x10FFFF) {
          bytes.push(((c >> 18) & 0x07) | 0xF0)
          bytes.push(((c >> 12) & 0x3F) | 0x80)
          bytes.push(((c >> 6) & 0x3F) | 0x80)
          bytes.push((c & 0x3F) | 0x80)
        } else if (c >= 0x000800 && c <= 0x00FFFF) {
          bytes.push(((c >> 12) & 0x0F) | 0xE0)
          bytes.push(((c >> 6) & 0x3F) | 0x80)
          bytes.push((c & 0x3F) | 0x80)
        } else if (c >= 0x000080 && c <= 0x0007FF) {
          bytes.push(((c >> 6) & 0x1F) | 0xC0)
          bytes.push((c & 0x3F) | 0x80)
        } else {
          bytes.push(c & 0xFF)
        }
      }
      return bytes
    },
    byteToString (arr) {
      if (typeof arr === 'string') {
        return arr
      }
      let str = ''
      let _arr = arr
      let one = ''
      let v = false
      for (let i = 0; i < _arr.length; i++) {
        one = _arr[i].toString(2)
        v = one.match(/^1+?(?=0)/)
        if (v && one.length === 8) {
          let bytesLength = v[0].length
          let store = _arr[i].toString(2).slice(7 - bytesLength)
          for (let st = 1; st < bytesLength; st++) {
            store += _arr[st + i].toString(2).slice(2)
          }
          str += String.fromCharCode(parseInt(store, 2))
          i += bytesLength - 1
        } else {
          str += String.fromCharCode(_arr[i])
        }
      }
      return str
    },
    dataURItoBlob (base64Data) {
      // console.log(base64Data);//data:image/png;base64,
      let byteString
      if (base64Data.split(',')[0].indexOf('base64') >= 0) byteString = atob(base64Data.split(',')[1])// base64 解码
      else {
        byteString = unescape(base64Data.split(',')[1])
      }
      let mimeString = base64Data.split(',')[0].split(':')[1].split(';')[0]// mime类型 -- image/png

      // let arrayBuffer = new ArrayBuffer(byteString.length); //创建缓冲数组
      // let ia = new Uint8Array(arrayBuffer);//创建视图
      let ia = new Uint8Array(byteString.length)// 创建视图
      for (let i = 0; i < byteString.length; i++) {
        ia[i] = byteString.charCodeAt(i)
      }
      let blob = new Blob([ia], {
        type: mimeString
      })
      return blob
    },
    getCaptureImgResultCB (flag, path, base64Str) {
      if (flag === 0) {
        this.pictureList.unshift({
          base64: 'data:image/png;base64,' + base64Str,
          file: this.dataURItoBlob('data:image/png;base64,' + base64Str)
        })
        this.previewList.push('data:;base64,' + base64Str)
        // console.log(this.dataURItoBlob('data:image/png;base64,' + base64Str))
        if (path === '') {
          console.log('拍照成功')
        } else {
          console.log('拍照成功，图片保存位置：' + path)
        }
      } else {
        console.log('拍照失败!')
      }
      this.getImageLoading = false
    },
    takePhoto () {
      // let name = this.formatDate(new Date().getTime())
      // let obj = document.getElementById('FileType')
      // let path = 'D:\\' + name + '.jpg'
      // console.log('主摄像头拍照')
      this.camPhoto() // 主摄像头拍照
      // camPhoto("");  //主摄像头拍照
    },
    setFileType (filetype = 0) {
      if (this.isSocketConnect) {
        let aDataArray = new Uint8Array(4)
        aDataArray[0] = 0x77
        aDataArray[1] = 0x88
        aDataArray[2] = 0x28
        aDataArray[3] = filetype // 0为jpg格式
        if (filetype === 1) {
          aDataArray[3] = 2// png格式
        }
        if (filetype === 2) {
          aDataArray[3] = 3 // tif格式
        }
        if (filetype === 3) {
          aDataArray[3] = 4 // pdf格式
        }
        this.sendBuffer(aDataArray.buffer)
      }
    },
    camPhoto (fileAddr) {
      if (this.MainCamCutMode === 1) {
        this.SetCutRect(this.pALastX, this.pALastY, (this.pACurrentX - this.pALastX), (this.pACurrentY - this.pALastY)) // 手动裁剪区域
      }
      console.log('camPhoto')
      console.log('fileAddr')
      this.captureImage('')
    },
    captureImage (fileAddr) {
      if (this.isSocketConnect) {
        this.getImageLoading = true
        // let pathArray = this.stringToUint8Array(fileAddr)
        if (fileAddr === '') {
          let packageCount = 1
          let len = 0
          let pindex = 0
          let totalLen = 12
          let aDataArray = new Uint8Array(totalLen)
          aDataArray[0] = 0x77
          aDataArray[1] = 0x88
          aDataArray[2] = 0x10
          aDataArray[3] = 0x00
          aDataArray[4] = len >> 16 & 0xff
          aDataArray[5] = len >> 8 & 0xff
          aDataArray[6] = len & 0xff
          aDataArray[7] = packageCount >> 8 & 0xff // 包总数
          aDataArray[8] = packageCount & 0xff // 包总数
          aDataArray[9] = 0 // 分包长度
          aDataArray[10] = pindex >> 8 & 0xff // 包序号
          aDataArray[11] = pindex & 0xff // 包序号
          console.log('无地址')
          console.log('pindex:' + pindex)
          console.log(aDataArray.buffer)
          this.sendBuffer(aDataArray.buffer)
        } else {
          let path = encodeURI(fileAddr)
          // console.log(path);
          let pathArray = this.stringToByte(path)
          console.log(pathArray)
          let len = pathArray.length
          console.log(len)

          let packageCount = 0
          let tmpLen = len
          console.log('tmpLen', tmpLen)
          while (tmpLen > 0) {
            tmpLen = tmpLen - 90
            packageCount++
          }

          console.log('packageCount:' + packageCount)

          let pindex = 0
          tmpLen = len
          console.log(tmpLen)
          while (tmpLen > 0) {
            tmpLen = tmpLen - 90

            if (tmpLen > 0) {
              let totalLen = 90 + 12
              let aDataArray = new Uint8Array(totalLen)
              aDataArray[0] = 0x77
              aDataArray[1] = 0x88
              aDataArray[2] = 0x10
              aDataArray[3] = 0x00
              aDataArray[4] = len >> 16 & 0xff
              aDataArray[5] = len >> 8 & 0xff
              aDataArray[6] = len & 0xff
              aDataArray[7] = packageCount >> 8 & 0xff // 包总数
              aDataArray[8] = packageCount & 0xff // 包总数
              aDataArray[9] = 90 // 分包长度
              aDataArray[10] = pindex >> 8 & 0xff // 包序号
              aDataArray[11] = pindex & 0xff // 包序号
              console.log('pindex:' + pindex)
              for (let i = 0; i < 90; i++) {
                aDataArray[12 + i] = pathArray[i + pindex * 90]
              }
              this.sendBuffer(aDataArray.buffer)
            } else {
              let totalLen = 90 + tmpLen + 12 // 此时tmpLen为负数，做加法运算
              console.log('totalLen', totalLen)
              let aDataArray = new Uint8Array(totalLen)
              aDataArray[0] = 0x77
              aDataArray[1] = 0x88
              aDataArray[2] = 0x10
              aDataArray[3] = 0x00
              aDataArray[4] = len >> 16 & 0xff
              aDataArray[5] = len >> 8 & 0xff
              aDataArray[6] = len & 0xff
              aDataArray[7] = packageCount >> 8 & 0xff // 包总数
              aDataArray[8] = packageCount & 0xff // 包总数
              aDataArray[9] = 90 + tmpLen // 分包长度
              aDataArray[10] = pindex >> 8 & 0xff // 包序号
              aDataArray[11] = pindex & 0xff // 包序号
              console.log('pindex:' + pindex)
              for (let i = 0; i < (90 + tmpLen); i++) {
                aDataArray[12 + i] = pathArray[i + pindex * 90]
              }
              this.sendBuffer(aDataArray.buffer)
            }
            pindex++
            this.toSleep(80)
          }
        }
      }
    },
    formatDate (time) {
      let date = new Date(time)
      let year = date.getFullYear()
      let month = date.getMonth() + 1
      let day = date.getDate()
      let hour = date.getHours()
      let min = date.getMinutes()
      let sec = date.getSeconds()
      let newTime = year +
        (month < 10 ? '0' + month : month) +
        (day < 10 ? '0' + day : day) +
        (hour < 10 ? '0' + hour : hour) +
        (min < 10 ? '0' + min : min) +
        (sec < 10 ? '0' + sec : sec)
      return newTime
    },
    getDevCount () {
      let aDataArray = new Uint8Array(3)
      aDataArray[0] = 0x77
      aDataArray[1] = 0x88
      aDataArray[2] = 0x50
      this.sendBuffer(aDataArray.buffer)
    },
    getDevCountAndNameResultCB (devCount, devNameArr) {
      if (devCount > 0) {
        console.log(devCount)
        console.log(devNameArr)
        // let obj = document.getElementById('DevName')
        // obj.options.length = 0
        // for (let i = 0; i < devCount; i++) {
        //   let objOption = document.createElement('option')
        //   objOption.text = devNameArr[i]
        //   objOption.value = i
        //   obj.options.add(objOption)
        // }
        // obj.selectedIndex = 0
        // let CamID = obj.selectedIndex

        // 获取分辨率
        // Cam_GetDevResolution(CamID)
      } else {
        console.log('没有发现合适的设备！')
      }
    },
    SetCutRect (rectX, rectY, rectW, rectH) {
      if (this.isSocketConnect) {
        let aDataArray = new Uint8Array(12)
        aDataArray[0] = 0x77
        aDataArray[1] = 0x88
        aDataArray[2] = 0x13 // 设置裁剪模式
        aDataArray[3] = 0x00
        aDataArray[4] = rectX >> 8 & 0xff
        aDataArray[5] = rectX & 0xff
        aDataArray[6] = rectY >> 8 & 0xff
        aDataArray[7] = rectY & 0xff
        aDataArray[8] = rectW >> 8 & 0xff
        aDataArray[9] = rectW & 0xff
        aDataArray[10] = rectH >> 8 & 0xff
        aDataArray[11] = rectH & 0xff
        this.sendBuffer(aDataArray.buffer)
      }
    },
    toSleep (milliSeconds) {
      console.log('sleep...')
      let startTime = new Date().getTime()
      while (new Date().getTime() < startTime + milliSeconds);
      console.log('sleep end')
    },
    close () {
      try {
        if (this.isSocketConnect) {
          this.closeCamera() // 关闭摄像头
          this.isOpenMainCamera = false
          this.getImageLoading = false
          this.releaseSocketPro()
          this.socket.close()
          this.socket = null
          this.$refs.cameraCtl.innerHTML = ''
        }
      } catch (ex) {
        console.log(ex)
      }
      console.log('onbeforeunload')
    },
    closeCamera () {
      if (this.isSocketConnect) {
        let aDataArray = new Uint8Array(4)
        aDataArray[0] = 0x77
        aDataArray[1] = 0x88
        aDataArray[2] = 0x02 // 关闭摄像头
        aDataArray[3] = 0x00
        this.sendBuffer(aDataArray.buffer)
      }
    },
    releaseSocketPro () {
      if (this.isSocketConnect) {
        let aDataArray = new Uint8Array(3)
        aDataArray[0] = 0x77
        aDataArray[1] = 0x88
        aDataArray[2] = 0xFF
        this.sendBuffer(aDataArray.buffer)
      }
    },
    getCameraOnOffStatus (status) {
      if (status === 0) {
        console.log('设备开启成功')
      } else {
        console.log('设备开启失败!')
      }
    },
    openCamera (iCamNo, width, height) {
      this.MainCanvas = {
        width: 600,
        height: 600
      }
      this.pALastX = 0
      this.pALastY = 0
      this.pACurrentX = 0
      this.pACurrentY = 0

      if (this.isSocketConnect) {
        let aDataArray = new Uint8Array(12)
        aDataArray[0] = 0x77
        aDataArray[1] = 0x88
        aDataArray[2] = 0x01 // 打开摄像头
        aDataArray[3] = iCamNo
        aDataArray[4] = this.MainCanvas.width >> 8 & 0xff
        aDataArray[5] = this.MainCanvas.width & 0xff
        aDataArray[6] = this.MainCanvas.height >> 8 & 0xff
        aDataArray[7] = this.MainCanvas.height & 0xff
        aDataArray[8] = width >> 8 & 0xff
        aDataArray[9] = width & 0xff
        aDataArray[10] = height >> 8 & 0xff
        aDataArray[11] = height & 0xff

        this.sendBuffer(aDataArray.buffer)
      }
    },
    zoomIn () {
      if (this.isSocketConnect) {
        let aDataArray = new Uint8Array(4)
        aDataArray[0] = 0x77
        aDataArray[1] = 0x88
        aDataArray[2] = 0x03 // 放大
        aDataArray[3] = 0x00
        this.sendBuffer(aDataArray.buffer)
      }
    },
    zoomOut () {
      if (this.isSocketConnect) {
        let aDataArray = new Uint8Array(4)
        aDataArray[0] = 0x77
        aDataArray[1] = 0x88
        aDataArray[2] = 0x04 // 缩小
        aDataArray[3] = 0x00
        this.sendBuffer(aDataArray.buffer)
      }
    },
    bestSize () {
      if (this.isSocketConnect) {
        let aDataArray = new Uint8Array(4)
        aDataArray[0] = 0x77
        aDataArray[1] = 0x88
        aDataArray[2] = 0x05 // 适合大小
        aDataArray[3] = 0x00
        this.sendBuffer(aDataArray.buffer)
      }
    },
    trueSize () {
      if (this.isSocketConnect) {
        let aDataArray = new Uint8Array(4)
        aDataArray[0] = 0x77
        aDataArray[1] = 0x88
        aDataArray[2] = 0x06 // 1:1
        aDataArray[3] = 0x00
        this.sendBuffer(aDataArray.buffer)
      }
    },
    sendBuffer (buffer) {
      if (this.socket) {
        this.socket.send(buffer)
      }
    },
    // onMouseWheel (ev) { /* 当鼠标滚轮事件发生时，执行一些操作 */
    //   let ev = ev || window.event
    //   let down = true
    //   this.per = 1
    //   down = ev.wheelDelta ? ev.wheelDelta < 0 : ev.detail > 0
    //   if (down) {
    //     this.zoomOut()
    //     // per += 0.05;
    //     // console.log("onMouseWheel down");
    //   } else {
    //     this.zoomIn()
    //     // per -= 0.05;
    //     // console.log("onMouseWheel up");
    //   }
    //   //    if (ev.preventDefault) { /*FF 和 Chrome*/
    //   //        ev.preventDefault(); // 阻止默认事件
    //   //    }
    //   return false
    // },
    // MainCanvasMouseDown (e) {
    //   this.isMouseDown = true
    //   this.pALastX = e.pageX - this.MainCanvas.offsetLeft
    //   this.pALastY = e.pageY - this.MainCanvas.offsetTop
    //   if (this.MainCamCutMode === 1) {
    //     this.pACurrentX = this.pALastX
    //     this.pACurrentY = this.pALastY
    //   }
    // },
    handleDeletePicture (index) {
      this.pictureList.splice(index, 1)
      this.previewList.splice(index, 1)
    }
  }
}
</script>

<style scoped lang="scss">
  >>>.el-dialog__body{
    padding: 10px 20px;
  }
  .main{
    height: 600px;
    display: flex;
    .deliveryInfo{
      width: 250px;
      margin-right: 10px;
      height: 100%;
      .item{
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin: 10px;
        height: 20px;
        line-height: 20px;
      }
    }
    .picture{
      /*padding: 0 10px;*/
      flex: 1;
      height: 100%;
      width: calc(100% - 250px - 250px);
      .pictureList{
        border: 1px solid #DCDFE6;
        border-top: none;
        border-right: none;
        height: 200px;
        width: 100%;
        padding: 0 10px 10px;
        .title{
          font-size: 16px;
          height: 24px;
          line-height: 24px;
        }
        .content{
          height: calc(100% - 19px);
          width: 100%;
          display: flex;
          align-items: center;
          border: 1px solid #DCDFE6;
          border-radius: 10px;
          overflow-x: hidden;
          .pictureItem{
            position: relative;
            height: 100%;
            width: 200px;
            display: inline-block;
            padding-top: 6px;
            margin: 0 10px;
            /*>>>.el-image__inner{*/
              /*position: relative;*/
            /*}*/
          }
          .deleteIcon{
            color: #909399;
            font-size: 20px;
            position: absolute;
            cursor: pointer;
            top: 6px;
            right: 0;
            z-index: 999;
            background: #fff;
            padding: 5px;
            border: 1px solid #EBEEF5;
            &:hover{
              background: #f2f2f2;
              color: #409EFF;
            }
          }
        }
      }
      .camera{
        border-left: 1px solid #DCDFE6;
        height: calc(100% - 200px);
        padding: 0 10px;
        .title{
          font-size: 16px;
          height: 24px;
          line-height: 24px;
        }
        .content{
          height: calc(100% - 25px);
          display: flex;
          .photo{
            border: 1px solid #DCDFE6;
            border-radius: 10px;
            flex: 1;
            padding: 10px;
          }
          .operation{
            margin: auto 0 auto 30px;
            width: 200px;
            height: 200px;
            display: flex;
            flex-direction: column;
            justify-content: space-around;
          }
        }
      }
    }
    .list{
      width: 250px;
      flex-shrink: 0;
      overflow-y: auto;
      position: relative;
      padding: 0 10px;
      border-left: 1px solid #DCDFE6;
      .list-title{
        height: 40px;
        font-weight: 600;
        font-size: 13px;
      }
      .list-content{
        height: calc(100% - 40px);
        /*overflow-y: auto;*/
        overflow: hidden;
        padding-right: 8px;
        .list-item{
          border-bottom: 1px solid #DCDFE6;
          padding: 10px 0;
          p{
            line-height: 20px;
          }
          .list-item-code{
            font-size: 15px;
            color: #303133;
            width: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          .list-item-time{
            font-size: 12px;
            /*text-align: right;*/
          }
        }
      }
    }
  }
</style>
