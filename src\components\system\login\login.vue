<template>
  <div class="login-page" :style="{ background: `url(${bg}) no-repeat`, backgroundSize: 'cover' }">
    <div class="login-wrap">
      <div class="info-path" :style="{ background: `url(${loginWrapBg}) no-repeat` }">
        <h4 class="sys-name">吉因加临床检测系统</h4>
        <p class="copyright">©{{ new Date().getFullYear() }}Geneplus Technology All rights reserved. 吉因加科技 版权所有。
          备案号：京ICP证15040088号 </p>
      </div>
      <div class="login-form">
        <h4>用户登录</h4>
        <el-form :model="form" :rules="rules" ref="form" label-width="80px;">
          <el-form-item prop="username">
            <el-input v-model="form.username" clearable placeholder="请输入账号" @keyup.enter.native="handleLogin">
              <i slot="prefix" class="el-input__icon el-icon-user"></i>
            </el-input>
          </el-form-item>
          <el-form-item prop="password">
            <el-input v-model="form.password" clearable placeholder="请输入密码" type="password"
              @keyup.enter.native="handleLogin">
              <i slot="prefix" class="el-input__icon el-icon-lock"></i>
            </el-input>
          </el-form-item>
          <el-form-item prop="verification">
            <div style="display: flex;width: 100%;">
              <el-input v-model="form.verification" clearable placeholder="请输入验证码" @keyup.enter.native="handleLogin">
                <i slot="prefix" class="el-input__icon el-icon-key"></i>
              </el-input>
            </div>
          </el-form-item>
          <el-form-item>
            <div style="display: flex;width: 100%; align-items: center;">
              <el-image :src="verificationImage" style="width: 180px;height: 60px;"
                v-loading="getImageLoading"></el-image>
              <el-button type="text" @click="initData">
                <i class="el-icon-refresh" style="font-size: 20px;margin-left: 10px;"></i>
              </el-button>
            </div>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" size="large" :loading="loading" style="width: 100%;"
              @click="handleLogin">登录</el-button>
          </el-form-item>
        </el-form>
        <!-- <div class="forget-password" @click="loginForgetPassDialogVisible = true">忘记密码？</div> -->
      </div>
    </div>

    <!-- <login-forget-pass-dialog :pvisible.sync="loginForgetPassDialogVisible" />-->
  </div>
</template>

<script>
// import num from './components/cc'
import util, { randomNum, awaitWrap, readBlob } from 'Util'
import { encryptAES } from '@/util/aes'
import constants from 'Constants'
import { getValidateCodeImage, userLogin } from '@/api/system/login'
// import LoginForgetPassDialog from './forgetPasswordDialog.vue'
import Vue from 'vue'
import Cookies from 'js-cookie'
// import { getNoticeNum } from '@/api/inventoryManagement/noticeManagement'
export default {
  name: 'login',
  // components: { LoginForgetPassDialog },
  mounted () {
    this.initData()
  },
  data () {
    return {
      bg: constants.IMAGE_CDN + '/login_page_bg.jpg',
      loginWrapBg: constants.IMAGE_CDN + '/login_container_bg.png',
      loginForgetPassDialogVisible: false,
      form: {
        username: '',
        password: '',
        verification: ''
      },
      rules: {
        username: [
          { required: true, message: '请输入账号', trigger: 'blur' },
          // 验证邮箱格式 util 内有工具方法
          {
            validator: (rule, value, callback) => {
              // 包含@才校验邮箱
              if (value.includes('@')) {
                util.validateElementEmail(rule, value, callback)
              }
              callback()
            },
            trigger: 'blur'
          }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' }
        ],
        verification: [
          { required: true, message: '请输入验证码', trigger: 'blur' }
        ]
      },
      redirectUrl: '',
      uuid: '',
      loading: false,
      verificationImage: '',
      getImageLoading: false
    }
  },
  methods: {
    initData () {
      if (this.$route.query.redirectUrl) {
        this.redirectUrl = decodeURIComponent(this.$route.query.redirectUrl)
      }
      // 判断是否已经登录
      if (Cookies.get('x-lims-token')) {
        this.getUserInfo()
        return
      }
      this.getUuid()
      this.getValidateCodeImage()
    },
    getUserInfo () {
      this.$ajax({
        url: '/user/get_authority_and_name',
        data: {},
        loadingDom: '.page',
        loadingObject: {
          text: '正在获取信息',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        }
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.dealLoginInfo(res.data)
        }
      })
    },
    // 获取当前系统UUID
    getUuid () {
      this.uuid = new Date().getTime() + randomNum(6)
    },
    async getValidateCodeImage () {
      this.getImageLoading = true
      const { res } = await awaitWrap(getValidateCodeImage(this.uuid))
      if (res) {
        await readBlob(res.data)
        this.verificationImage = window.URL.createObjectURL(new Blob([res.data]))
      }
      this.getImageLoading = false
    },
    // 设置登录参数
    setLoginParams () {
      return {
        userName: this.form.username,
        password: encryptAES(this.form.password),
        code: this.form.verification,
        uuid: this.uuid
      }
    },
    validateFormInput () {
      return new Promise(resolve => {
        this.$refs.form.validate(valid => {
          if (valid) {
            resolve()
          }
        })
      })
    },
    // 处理登录信息
    dealLoginInfo (data) {
      let name = data.userName
      let id = data.userId
      let emailPrefix = data.emailPrefix
      let resources = {
        modules: [],
        menus: [],
        buttons: []
      }
      this.iterateDecorateResource(resources, data.resources)
      util.setSessionInfo('resource', resources)
      Vue.prototype.$myresource = resources
      let area = data.area || []
      this.lab = []
      this.labOptions = []
      // 用户已经设置了的实验室
      let hasSetLab = util.getSessionInfo('currentLab') || []
      let areaIds = area.filter(v => v.production_area_id) || []
      let labs = areaIds.map(v => v.production_area_id) // 清除在资源里面没有的项
      area.forEach(item => {
        let v = {
          label: item.production_area_name,
          value: item.production_area_id
        }
        this.labOptions.push(v)
        if (item.is_default === 1) this.lab.push(item.production_area_id)
      })
      util.setSessionInfo('currentLab', (hasSetLab && hasSetLab.length > 0) ? labs : this.lab)
      Cookies.set('flab', (hasSetLab && hasSetLab.length > 0) ? labs.toString() : this.lab.toString())
      Cookies.set('labOptions', JSON.stringify(this.labOptions))
      this.$store.commit({
        type: 'old/setValue',
        category: 'userInfo',
        userInfo: {
          name,
          id,
          avatar: '',
          emailPrefix: emailPrefix
        }
      })
      resources = this.dealResource(data.resources || [])
      if (data.ssoList) {
        this.handleSSO(data.ssoList, resources) // 处理SSO
      } else {
        this.redirectUser(resources) // 重定向用户
      }
      this.$store.dispatch('setLoginData', {
        userInfo: {
          name,
          id,
          avatar: '',
          emailPrefix: emailPrefix
        },
        token: Cookies.get('x-lims-token'),
        resources
      })
    },
    async handleLogin () {
      await this.validateFormInput()
      const params = this.setLoginParams()
      this.loading = true
      const { res } = await awaitWrap(userLogin(params))
      try {
        if (res.code === this.SUCCESS_CODE) {
          Cookies.set('x-lims-token', res.data.ftoken)
          this.dealLoginInfo(res.data)
        }
      } finally {
        this.loading = false
      }
    },
    redirectUser (resource) {
      if (resource.menuList.length !== 0) {
        this.$message.success('登陆成功') // 登录成功提示
        if (this.redirectUrl) {
          window.location.href = this.redirectUrl
          // this.$router.push({path: this.redirectUrl}) // 重定向到指定路径
          return
        }
        window.location.href = constants.OLD_LIMS_URL
      } else {
        this.$message.error('你没有授权访问该系统，请联系管理员解决问题！') // 权限不足提示
      }
    },
    handleSSO (ssoList, resource) {
      if (ssoList) {
        let jsonpList = ssoList.map(v => {
          return new Promise(resolve => {
            try {
              this.$jsonp(v, {}) // 处理jsonp请求
            } catch (err) {
              console.log(err) // 捕获错误
            } finally {
              resolve() // 解析Promise
            }
          })
        })
        Promise.all(jsonpList).then(() => {
          this.redirectUser(resource) // 重定向用户
        }).catch() // 捕获错误
      }
    },
    // 处理登录资源数据
    dealResource (resources) {
      let menuList = []
      const btnList = []
      const pathList = []
      this.iterateDecorateNewResource(menuList, btnList, pathList, resources)
      menuList = menuList.filter(this.judgeHasFrontModule)
      menuList = menuList.map(menu => {
        menu.children = menu.children.filter(v => {
          v.url = v.url || ''
          return v.url.includes('xfront')
        }).map(v => {
          v.url = v.url || ''
          v.url = v.url.replace('xfront', '')
          return v
        })
        menu.children = menu.children.sort((pre, next) => pre.order - next.order)
        return menu
      })
      return {
        menuList: menuList.sort((pre, next) => pre.order - next.order),
        btnList,
        pathList
      }
    },
    judgeHasFrontModule (MODULE) {
      const children = MODULE.children || []
      return children.some(v => v.url.includes('xfront'))
    },
    iterateDecorateNewResource (menuList, componentList, pathList, source = []) {
      source.forEach((v, i) => {
        let isFrontModule = true
        // 判断当前模块是否包含前端模块
        // if (v.resourceType === 'MODULE') isFrontModule = this.judgeHasFrontModule(v)
        // if (v.resourceType === 'MENU') isFrontModule = v.resourceUrl.includes('xfront')
        if (v.resourceType === 'MODULE' || v.resourceType === 'SUB-MODULE' || v.resourceType === 'MENU') {
          let url = v.resourceUrl
          const item = {
            title: v.resourceName,
            url: url,
            icon: v.resourceIconCls || 'icon-authority',
            order: v.resourceOrder,
            children: []
          }
          if (v.children && v.children.length > 0) {
            this.iterateDecorateNewResource(item.children, componentList, pathList, v.children)
          }
          if (isFrontModule) menuList.push(item)
          if (url && isFrontModule) pathList.push(url)
        } else if (v.resourceType === 'BUTTON') {
          if (isFrontModule) componentList.push(v.resourceLevel)
          if (v.children && v.children.length > 0) {
            this.iterateDecorateResource(null, componentList, pathList, v.children)
          }
        }
      })
    },
    iterateDecorateResource (
      data = {
        modules: [],
        menus: [],
        buttons: []
      },
      resources = []
    ) {
      if (resources && resources.length > 0) {
        resources.forEach(item => {
          switch (item.resourceType) {
            case 'MODULE':
              data.modules.push(item.resourceLevel)
              break
            case 'MENU':
              data.menus.push(item.resourceLevel)
              break
            case 'BUTTON':
              data.buttons.push(item.resourceLevel)
              break
          }
          if (item.children && item.children.length > 0) {
            this.iterateDecorateResource(data, item.children)
          }
        })
      }
    }
  }
}
</script>

<style scoped lang="scss">
.login-page {
  width: 100vw;
  height: 100vh;
  overflow: auto;
  display: flex;

  .login-wrap {
    margin: auto;
    display: flex;
    width: 50%;
    min-width: 750px;
    max-width: 800px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    border-radius: 10px;
    overflow: hidden;
    $child-padding: 20px 40px; // 子元素padding

    .info-path {
      width: 50%;
      background-size: cover !important;
      color: #fff;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      padding: $child-padding;

      .sys-name {
        font-size: 25px;
        letter-spacing: 4px;
        line-height: 80px;
      }

      .copyright {
        font-size: 12px;
      }
    }

    .login-form {
      width: 50%;
      background: #fff;
      padding: $child-padding;

      h4 {
        line-height: 80px;
        font-size: 20px;
        font-weight: 600;
        letter-spacing: 3px;
      }

      .el-input__icon {
        font-size: 20px;
      }

      .forget-password {
        font-size: 13px;
        cursor: pointer;
        color: #909399;
        text-align: right;
      }
    }
  }
}
</style>
