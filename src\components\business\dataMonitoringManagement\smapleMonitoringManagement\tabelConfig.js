// 定义一个常量，用于复用render属性中相同的配置
const inputRenderConfig = {name: '$input', props: {clearable: true}, events: {input: 'handleSaveNote'}}

let idCounter = 1 // 新增全局变量用于追踪递增的ID
// 定义一个函数，用于生成具有共同属性的配置对象，提高代码的可维护性
function createCommonConfig (field, title, renderConfig = null, formaterFN = null, width = 140) {
  return {
    id: idCounter++, // 修改为从0开始递增
    title: title,
    field: field,
    showOverflow: true,
    width: width,
    render: renderConfig,
    isCustomerField: true,
    formater: formaterFN,
    isShow: true
  }
}

// 初始化idCounter，确保每次运行或刷新页面时ID序列重新开始
idCounter = 1

/**
 * 导出一个配置数组，用于表格设置。该数组包含多个配置对象，每个对象代表表格中的一列。
 * 这种方式有助于统一管理和创建表格列配置，提高代码的可维护性和可读性。
 *
 * @returns {Object[]} 返回一个配置对象数组，每个对象包含列的配置信息。
 */
export const tableConfig = [
  // // 吉因加编号
  // createCommonConfig('geneCode', '吉因加编号'),
  // // 原始样本编号
  // createCommonConfig('oldSampleName', '原始样本编号'),
  // 样本当前进度
  createCommonConfig('currentStepText', '样本当前进度'),
  // 质控流转状态
  createCommonConfig('deliverStatusText', '交付状态'),
  // 质控流转状态
  createCommonConfig('qcStatusText', '质控流转状态'),
  // 审核状态
  createCommonConfig('auditStatusText', '审核状态'),
  // 确认时间
  createCommonConfig('confirmTime', '到样时间'),
  // 订单编号
  createCommonConfig('orderCode', '订单编号'),
  // 订单类型
  createCommonConfig('orderTypeText', '订单类型'),
  // 项目编码
  createCommonConfig('projectCode', '项目编码'),
  // 项目名称
  createCommonConfig('projectName', '项目名称'),
  // 样本类型
  createCommonConfig('sampleType', '样本类型'),
  // 产品名称
  createCommonConfig('productName', '产品名称'),
  createCommonConfig('productType', '产品类型'),
  // 文库类型
  createCommonConfig('libraryType', '文库类型'),
  // 是否备份
  createCommonConfig('isBackUpText', '是否备份'),
  // 实验环节
  createCommonConfig('experimentalLink', '实验环节'),
  // 测序平台
  createCommonConfig('instrumentType', '测序平台'),
  // 测序策略
  createCommonConfig('sequenceType', '测序策略'),
  // 包lane数
  createCommonConfig('laneTotal', '包lane数'),
  // 下单数据量
  createCommonConfig('dataSize', '下单数据量'),
  // 加测数据量
  // createCommonConfig('addTestOrderDataSize', '加测数据量'),
  // 数据单位
  createCommonConfig('dataSizeUnit', '数据单位'),
  // 质控结果
  createCommonConfig('qcResult', '样本质控'),
  // 上机产出总量
  createCommonConfig('outputTotal', '上机产出总量'),
  // 上机产出占比
  createCommonConfig('outputProportionText', '产出比'),
  // 交付方式
  createCommonConfig('deliveryMethod', '交付方式'),
  // 质控报告时间
  createCommonConfig('qcReportTime', '质控报告时间'),
  // 启用时间
  createCommonConfig('enableTime', '启动时间'),
  // 建库完成时间
  createCommonConfig('buildLibFinishTime', '建库完成时间'),
  // 上机时间
  createCommonConfig('sequenceTime', '上机时间'),
  // 下机时间
  createCommonConfig('deplaneTime', '下机时间'),
  // 交付时间
  createCommonConfig('deliveryTime', '交付时间'),
  // 标准周期
  createCommonConfig('standardCycle', '标准周期(天)'),
  // 实际周期
  createCommonConfig('actualCycle', '实际周期(天)'),
  // 延期天数
  createCommonConfig('delayDays', '延期天数(天)'),
  // 是否延期
  createCommonConfig('isDelayText', '是否延期'),
  // 重新实验次数
  createCommonConfig('reExperimentNum', '重新实验次数'),
  // 预警
  createCommonConfig('flowWarningText', '自动流转预警'),
  // 交付问题
  createCommonConfig('deliveryProblem', '交付问题', inputRenderConfig),
  // 数据备注
  createCommonConfig('dataNote', '数据备注'),
  createCommonConfig('freleaseTotal', '重释放总量'),
  createCommonConfig('fmergeAddTestingOutputTotal', '加测合并交付-首测数据量', null, null, 180),
  createCommonConfig('orderNote', '订单备注'),
  // 样本备注
  createCommonConfig('sampleNote', '样本备注'),

  // 任务编号
  createCommonConfig('taskCode', '任务编号'),
  // 分析流程
  createCommonConfig('analysisProcessName', '分析流程'),
  // 任务类型
  createCommonConfig('analysisType', '任务类型'),
  // 下分析单时间
  createCommonConfig('analysisSubmitTime', '下分析单时间'),
  // 分析完成时间
  createCommonConfig('analysisFinishTime', '分析完成时间'),
  // 分析交付时间
  createCommonConfig('analysisDeliveryTime', '分析交付时间')
]
