# CDN 部署工具包

专为前端项目设计的企业级 CDN 部署工具包，支持多种云存储服务、自动化构建优化和 Vue 组件样式问题修复。

## 🎯 核心特性

- 🚀 **多云支持**: 阿里云 OSS、腾讯云 COS、七牛云等主流 CDN 服务
- 📦 **智能构建**: 自动化构建优化、代码分割、资源压缩
- 🎨 **样式修复**: 解决 Vue 组件 scoped 样式提取问题
- 📊 **性能分析**: 详细的构建性能分析和优化建议
- 🔄 **版本管理**: 智能缓存策略和版本控制
- 🛠️ **灵活配置**: 多环境支持和自定义配置选项

## 📁 项目结构

```
cdn-deployment-kit/
├── deploy.js                      # 主部署入口
├── cdn-config.js                  # CDN 配置文件
├── oss-uploader.js               # 云存储上传器
├── html-updater.js               # HTML 资源引用更新器
├── performance-tools.js          # 性能优化工具
├── build-performance-analyzer.js # 构建性能分析器
├── webpack-config-updater.js     # Webpack 配置更新器
├── logger.js                     # 日志工具
├── package.json                  # 依赖配置
└── README.md                     # 技术文档
```

## 🚀 快速开始

### 1. 安装依赖

```bash
cd cdn-deployment-kit
npm install
```

### 2. 环境配置

创建项目根目录下的 `.env` 文件：

```env
# 阿里云 OSS 配置
ALI_OSS_ACCESS_KEY_ID=your_access_key_id
ALI_OSS_ACCESS_KEY_SECRET=your_access_key_secret
ALI_OSS_REGION=oss-cn-hangzhou
ALI_OSS_BUCKET=your-bucket-name

# 腾讯云 COS 配置（可选）
TENCENT_COS_SECRET_ID=your_secret_id
TENCENT_COS_SECRET_KEY=your_secret_key
TENCENT_COS_REGION=ap-beijing
TENCENT_COS_BUCKET=your-bucket-name

# 七牛云配置（可选）
QINIU_ACCESS_KEY=your_access_key
QINIU_SECRET_KEY=your_secret_key
QINIU_BUCKET=your-bucket-name
QINIU_DOMAIN=your-domain.com
```

### 3. 基本使用

```bash
# 完整部署流程（构建 + 上传 + 优化）
node cdn-deployment-kit/deploy.js deploy --env test

# 部署到生产环境
node cdn-deployment-kit/deploy.js deploy --env prod

# 仅上传已构建的文件
node cdn-deployment-kit/deploy.js upload --env test

# 性能分析
node cdn-deployment-kit/deploy.js analyze
```

## ⚙️ 配置说明

### CDN 配置 (cdn-config.js)

```javascript
module.exports = {
  // 项目基本信息
  project: {
    name: 'BMS-frontend',
    version: '1.0.0',
    description: '基因检测管理系统前端'
  },

  // 多环境配置
  environments: {
    test: {
      provider: 'aliOSS',           // CDN 服务商
      basePath: 'BMS-frontend/test', // CDN 路径前缀
      cacheControl: 'public, max-age=300',  // 缓存策略
      forceOverwrite: true,         // 强制覆盖
      enableGzip: false            // Gzip 压缩
    },
    prod: {
      provider: 'aliOSS',
      basePath: 'BMS-frontend/prod',
      cacheControl: 'public, max-age=31536000',
      forceOverwrite: false,
      enableGzip: true
    }
  },

  // 构建优化配置
  optimization: {
    enableParallel: true,          // 并行处理
    enableCodeSplitting: true,     // 代码分割
    enableCSSExtraction: true,     // CSS 提取
    maxFileSize: 1024 * 1024,     // 最大文件大小 (1MB)
    compressionLevel: 6           // 压缩级别
  }
}
```

## 📋 命令行选项

### deploy 命令

```bash
node cdn-deployment-kit/deploy.js deploy [options]
```

**选项:**
- `--env <environment>`: 部署环境 (test/staging/prod)
- `--skip-build`: 跳过构建阶段
- `--skip-analyze`: 跳过性能分析
- `--skip-upload`: 跳过CDN上传
- `--config <path>`: 指定配置文件路径
- `--verbose`: 启用详细日志输出

### upload 命令

```bash
node cdn-deployment-kit/deploy.js upload --env <environment>
```

仅上传已构建的文件到CDN，不进行构建。

### analyze 命令

```bash
node cdn-deployment-kit/deploy.js analyze
```

分析现有构建输出，生成性能报告。

## � 核心模块详解

### 1. 主部署工具 (deploy.js)

**功能**: 协调整个部署流程的主入口

**核心流程**:
1. 环境配置验证
2. 构建优化处理
3. 性能分析
4. CDN 文件上传
5. HTML 资源引用更新
6. 部署报告生成

**使用示例**:
```bash
# 完整部署
node deploy.js deploy --env test

# 指定配置文件
node deploy.js deploy --env prod --config custom-config.js

# 跳过构建直接上传
node deploy.js upload --env test --skip-build
```

### 2. OSS 上传器 (oss-uploader.js)

**功能**: 处理文件上传到各种云存储服务

**支持的服务商**:
- 阿里云 OSS
- 腾讯云 COS
- 七牛云
- AWS S3

**核心特性**:
- 批量并发上传
- 断点续传
- 文件去重
- 上传进度显示
- 错误重试机制

### 3. HTML 更新器 (html-updater.js)

**功能**: 更新 HTML 文件中的静态资源引用路径

**处理内容**:
- CSS 文件引用
- JavaScript 文件引用
- 图片资源引用
- 字体文件引用
- 添加跨域属性

**示例转换**:
```html
<!-- 转换前 -->
<link href="static/css/app.css" rel="stylesheet">
<script src="static/js/vendor.js"></script>

<!-- 转换后 -->
<link href="https://cdn.example.com/project/1.0.0/static/css/app.css" rel="stylesheet" crossorigin="anonymous">
<script src="https://cdn.example.com/project/1.0.0/static/js/vendor.js" crossorigin="anonymous"></script>
```

### 4. 性能优化工具 (performance-tools.js)

**功能**: 提供构建优化和性能分析功能

**核心优化**:
- **CSS 优化**: 修复 ExtractTextPlugin 配置，确保 Vue 组件 scoped 样式正确提取
- **JavaScript 优化**: 代码分割、压缩、Tree Shaking
- **并行处理**: 多进程构建加速
- **缓存优化**: 智能缓存策略

**Vue 样式问题修复**:
```javascript
// 关键修复：ExtractTextPlugin 配置
new ExtractTextPlugin({
  filename: 'static/css/[name].[contenthash:8].css',
  allChunks: true,        // 修复：确保所有 chunk 的 CSS 都被提取
  ignoreOrder: false      // 修复：保持 CSS 顺序
})
```

### 5. 构建性能分析器 (build-performance-analyzer.js)

**功能**: 分析构建输出和性能指标

**分析维度**:
- 文件大小分析
- 构建时间统计
- 内存使用情况
- 依赖关系分析
- 性能评分

**输出报告**:
```json
{
  "buildTime": "210.23s",
  "totalAssets": 340,
  "performanceScore": 85,
  "recommendations": [
    "优化大文件分割",
    "启用 Gzip 压缩",
    "减少依赖包大小"
  ]
}
```

### 6. Webpack 配置更新器 (webpack-config-updater.js)

**功能**: 动态更新 Webpack 配置

**主要功能**:
- 动态设置 publicPath
- 优化输出配置
- 插件配置调整
- 兼容性处理

## 🎨 Vue 组件样式问题解决方案

### 问题背景

在使用 ExtractTextPlugin 进行 CSS 提取时，Vue 组件的 scoped 样式可能无法正确提取到最终的 CSS 文件中，导致样式不生效。

### 根本原因

1. **ExtractTextPlugin 配置问题**: `allChunks: false` 导致部分 chunk 的样式被忽略
2. **CSS 顺序问题**: `ignoreOrder: true` 可能导致样式覆盖问题
3. **Vue-loader 兼容性**: 与 Webpack 3.x 的兼容性问题

### 解决方案

在 `performance-tools.js` 的 `optimizeWebpackConfig` 方法中进行了关键修复：

```javascript
// 修复 ExtractTextPlugin 配置
new ExtractTextPlugin({
  filename: 'static/css/[name].[contenthash:8].css',
  allChunks: true,        // 关键修复：确保所有 chunk 的 CSS 都被提取
  ignoreOrder: false      // 关键修复：保持 CSS 顺序
})
```

### 验证方法

1. 检查构建日志中的修复信息：
```
[performance-tools] ✅ ExtractTextPlugin已修复 - Vue组件scoped样式现在应该能正确提取
```

2. 验证 CSS 文件内容：
```bash
# 检查 app.css 是否包含 Vue 组件样式
curl https://cdn.example.com/static/css/app.css | grep "data-v-"
```

## � 性能优化策略

### 1. 构建优化

- **并行处理**: 利用多核 CPU 加速构建
- **缓存机制**: 智能缓存减少重复构建
- **代码分割**: 按需加载减少初始包大小
- **Tree Shaking**: 移除未使用的代码

### 2. 资源优化

- **图片压缩**: 自动压缩图片资源
- **CSS 优化**: 压缩、去重、合并
- **JavaScript 压缩**: UglifyJS 压缩优化
- **Gzip 压缩**: 服务端压缩传输

### 3. 缓存策略

- **长期缓存**: 静态资源使用 hash 文件名
- **分层缓存**: 不同类型资源不同缓存策略
- **版本控制**: 自动版本管理和回滚

## 🔍 故障排除

### 常见问题及解决方案

#### 1. Vue 组件样式不生效

**症状**: Vue 组件中定义的 scoped 样式在页面上不显示

**解决方案**:
1. 检查构建日志确认样式修复已应用
2. 清除浏览器缓存强制刷新
3. 验证 CSS 文件是否包含组件样式

#### 2. CDN 上传失败

**症状**: 文件上传到 CDN 时出现错误

**解决方案**:
1. 检查网络连接和防火墙设置
2. 验证 CDN 服务商配置和权限
3. 检查文件大小是否超出限制

#### 3. 构建性能问题

**症状**: 构建时间过长或内存不足

**解决方案**:
1. 启用并行处理: `enableParallel: true`
2. 调整 Node.js 内存限制: `--max-old-space-size=4096`
3. 清理 node_modules 重新安装依赖

#### 4. HTML 资源引用错误

**症状**: 页面加载时资源 404 错误

**解决方案**:
1. 检查 CDN 配置中的 basePath 设置
2. 验证 HTML 更新器的路径替换逻辑
3. 确认 CDN 文件上传成功

### 调试模式

启用详细日志输出：

```bash
# 启用调试模式
DEBUG=cdn-deployment-kit:* node deploy.js deploy --env test

# 生成详细报告
node deploy.js deploy --env test --verbose --report
```

## � 最佳实践

### 1. 部署流程

1. **开发阶段**: 使用 test 环境进行测试
2. **测试验证**: 确认功能和样式正常
3. **预发布**: 使用 staging 环境最终验证
4. **生产部署**: 部署到 prod 环境

### 2. 配置管理

- 使用环境变量管理敏感信息
- 不同环境使用不同的缓存策略
- 定期备份配置文件

### 3. 监控和维护

- 定期检查 CDN 使用情况
- 监控构建性能指标
- 及时更新依赖包版本

## 📝 更新日志

### v1.0.0 (2025-08-04)

- ✅ 修复 Vue 组件 scoped 样式提取问题
- ✅ 优化 ExtractTextPlugin 配置
- ✅ 改进构建性能和并行处理
- ✅ 完善错误处理和日志输出
- ✅ 代码清理和文档完善

## 🤝 贡献指南

欢迎提交 Issue 和 Pull Request！

### 开发环境

```bash
cd cdn-deployment-kit
npm install
npm test
```

### 代码规范

- 使用 ESLint 进行代码检查
- 遵循 JavaScript Standard Style
- 添加适当的注释和文档

## 📄 许可证

MIT License - 详见 LICENSE 文件

## 🔗 相关链接

- [Webpack 官方文档](https://webpack.js.org/)
- [阿里云 OSS 文档](https://help.aliyun.com/product/31815.html)
- [Vue.js 官方文档](https://vuejs.org/)
- [Node.js 性能优化](https://nodejs.org/en/docs/guides/simple-profiling/)

---

**🎉 享受高效的 CDN 部署体验！**
