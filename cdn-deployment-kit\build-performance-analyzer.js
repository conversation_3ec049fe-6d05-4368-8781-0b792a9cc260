/**
 * CDN部署工具包 - 构建性能分析器
 * 分析构建输出，生成性能报告和优化建议
 */

const fs = require('fs')
const path = require('path')
const chalk = require('chalk')
// 导入统一日志系统
const { logger } = require('./logger')

// 创建带上下文的日志器
const log = logger.child('build-performance-analyzer')

/**
 * 构建性能分析器
 */
class BuildPerformanceAnalyzer {
  constructor (projectRoot = process.cwd()) {
    // 如果在cdn-deployment-kit目录中运行，使用父目录作为项目根目录
    const currentDir = process.cwd()
    const isInCDNKit = currentDir.endsWith('cdn-deployment-kit')
    this.projectRoot = projectRoot || (isInCDNKit ? path.dirname(currentDir) : currentDir)
    this.distPath = path.join(this.projectRoot, 'dist')
    this.staticPath = path.join(this.distPath, 'static')
    this.historyPath = path.join(this.projectRoot, 'cdn-deployment-kit', 'build-history.json')

    log.debug(`分析器项目根目录: ${this.projectRoot}`)
    log.debug(`构建输出目录: ${this.distPath}`)
  }

  /**
   * 分析构建输出
   */
  analyzeBundleOutput (buildResult = null) {
    log.analyze('分析构建输出...')

    // 检查构建输出目录是否存在
    if (!fs.existsSync(this.distPath)) {
      log.warn(`构建输出目录不存在: ${this.distPath}`)
      log.info('请先运行构建命令: node run.js build')
      return {
        js: { files: [], totalSize: 0, overLimitFiles: [], averageSize: 0 },
        css: { files: [], totalSize: 0, overLimitFiles: [], averageSize: 0 },
        other: { files: [], totalSize: 0 },
        total: { files: 0, totalSize: 0 },
        timestamp: new Date().toISOString(),
        buildResult
      }
    }

    const analysis = {
      js: this.analyzeJavaScriptFiles(),
      css: this.analyzeCSSFiles(),
      other: this.analyzeOtherFiles(),
      total: { files: 0, totalSize: 0 },
      timestamp: new Date().toISOString(),
      buildResult
    }

    // 计算总计
    analysis.total.files = analysis.js.files.length + analysis.css.files.length + analysis.other.files.length
    analysis.total.totalSize = analysis.js.totalSize + analysis.css.totalSize + analysis.other.totalSize

    log.success('构建输出分析完成')
    log.debug(`JavaScript: ${analysis.js.files.length} 个文件, ${(analysis.js.totalSize / 1024 / 1024).toFixed(2)} MB`)
    log.debug(`CSS: ${analysis.css.files.length} 个文件, ${(analysis.css.totalSize / 1024 / 1024).toFixed(2)} MB`)
    log.debug(`其他资源: ${analysis.other.files.length} 个文件, ${(analysis.other.totalSize / 1024 / 1024).toFixed(2)} MB`)
    log.debug(`总计: ${analysis.total.files} 个文件, ${(analysis.total.totalSize / 1024 / 1024).toFixed(2)} MB`)

    if (analysis.js.overLimitFiles.length > 0) {
      console.log(chalk.yellow(`   ⚠️  ${analysis.js.overLimitFiles.length} 个JavaScript文件超过1MB限制`))
      analysis.js.overLimitFiles.forEach(file => {
        console.log(chalk.yellow(`      - ${file.name}: ${file.sizeInMB} MB`))
      })
    }

    if (analysis.css.overLimitFiles.length > 0) {
      console.log(chalk.yellow(`   ⚠️  ${analysis.css.overLimitFiles.length} 个CSS文件超过0.5MB限制`))
      analysis.css.overLimitFiles.forEach(file => {
        console.log(chalk.yellow(`      - ${file.name}: ${file.sizeInMB} MB`))
      })
    }

    return analysis
  }

  /**
   * 分析JavaScript文件
   */
  analyzeJavaScriptFiles () {
    const jsPath = path.join(this.staticPath, 'js')
    const analysis = {
      files: [],
      totalSize: 0,
      overLimitFiles: [],
      averageSize: 0
    }

    if (!fs.existsSync(jsPath)) {
      return analysis
    }

    const files = fs.readdirSync(jsPath).filter(file => file.endsWith('.js'))

    for (const file of files) {
      const filePath = path.join(jsPath, file)
      const stats = fs.statSync(filePath)
      const sizeInMB = stats.size / 1024 / 1024

      const fileInfo = {
        name: file,
        path: filePath,
        size: stats.size,
        sizeInMB: parseFloat(sizeInMB.toFixed(2)),
        isOverLimit: sizeInMB > 1
      }

      analysis.files.push(fileInfo)
      analysis.totalSize += stats.size

      if (fileInfo.isOverLimit) {
        analysis.overLimitFiles.push(fileInfo)
      }
    }

    analysis.averageSize = analysis.files.length > 0
      ? analysis.totalSize / analysis.files.length
      : 0

    return analysis
  }

  /**
   * 分析CSS文件
   */
  analyzeCSSFiles () {
    const cssPath = path.join(this.staticPath, 'css')
    const analysis = {
      files: [],
      totalSize: 0,
      overLimitFiles: [],
      averageSize: 0
    }

    if (!fs.existsSync(cssPath)) {
      return analysis
    }

    const files = fs.readdirSync(cssPath).filter(file => file.endsWith('.css'))

    for (const file of files) {
      const filePath = path.join(cssPath, file)
      const stats = fs.statSync(filePath)
      const sizeInMB = stats.size / 1024 / 1024

      const fileInfo = {
        name: file,
        path: filePath,
        size: stats.size,
        sizeInMB: parseFloat(sizeInMB.toFixed(2)),
        isOverLimit: sizeInMB > 0.5 // CSS文件限制为0.5MB
      }

      analysis.files.push(fileInfo)
      analysis.totalSize += stats.size

      if (fileInfo.isOverLimit) {
        analysis.overLimitFiles.push(fileInfo)
      }
    }

    analysis.averageSize = analysis.files.length > 0
      ? analysis.totalSize / analysis.files.length
      : 0

    return analysis
  }

  /**
   * 分析其他文件
   */
  analyzeOtherFiles () {
    const analysis = {
      files: [],
      totalSize: 0,
      averageSize: 0
    }

    if (!fs.existsSync(this.distPath)) {
      return analysis
    }

    // 递归扫描dist目录中的所有非JS/CSS文件
    const scanDirectory = (dirPath, relativePath = '') => {
      const items = fs.readdirSync(dirPath)

      for (const item of items) {
        const itemPath = path.join(dirPath, item)
        const itemRelativePath = path.join(relativePath, item).replace(/\\/g, '/')
        const stats = fs.statSync(itemPath)

        if (stats.isDirectory()) {
          // 跳过static目录，因为JS和CSS已经单独分析了
          if (item !== 'static') {
            scanDirectory(itemPath, itemRelativePath)
          }
        } else {
          // 只分析非JS/CSS文件
          const ext = path.extname(item).toLowerCase()
          if (!['.js', '.css'].includes(ext)) {
            const sizeInMB = stats.size / 1024 / 1024

            const fileInfo = {
              name: item,
              path: itemPath,
              relativePath: itemRelativePath,
              size: stats.size,
              sizeInMB: parseFloat(sizeInMB.toFixed(2)),
              extension: ext
            }

            analysis.files.push(fileInfo)
            analysis.totalSize += stats.size
          }
        }
      }
    }

    scanDirectory(this.distPath)

    analysis.averageSize = analysis.files.length > 0
      ? analysis.totalSize / analysis.files.length
      : 0

    return analysis
  }

  /**
   * 计算性能指标
   */
  calculatePerformanceMetrics (buildResult, bundleAnalysis) {
    log.analyze('计算性能指标...')

    const metrics = {
      buildTime: buildResult?.buildTime || 0,
      buildTimeSeconds: buildResult?.buildTimeSeconds || '0.00',
      bundleSize: bundleAnalysis.total.totalSize,
      fileCount: bundleAnalysis.total.files,
      overLimitFiles: (bundleAnalysis.js.overLimitFiles?.length || 0) + (bundleAnalysis.css.overLimitFiles?.length || 0),
      averageFileSize: bundleAnalysis.total.files > 0
        ? bundleAnalysis.total.totalSize / bundleAnalysis.total.files
        : 0,
      jsFiles: bundleAnalysis.js.files.length,
      cssFiles: bundleAnalysis.css.files.length,
      otherFiles: bundleAnalysis.other?.files.length || 0,
      jsSize: bundleAnalysis.js.totalSize,
      cssSize: bundleAnalysis.css.totalSize,
      otherSize: bundleAnalysis.other?.totalSize || 0,
      compressionRatio: 0,
      memoryUsage: buildResult?.memory || process.memoryUsage(),
      warnings: buildResult?.warnings || 0,
      errors: buildResult?.errors || 0,
      timestamp: new Date().toISOString(),
      overallScore: 0
    }

    // 计算总体评分 (0-100)
    let score = 100

    // 构建时间评分 (最多扣30分)
    if (metrics.buildTime > 180000) { // 3分钟
      score -= 30
    } else if (metrics.buildTime > 120000) { // 2分钟
      score -= 20
    } else if (metrics.buildTime > 60000) { // 1分钟
      score -= 10
    }

    // 包大小评分 (最多扣25分)
    const bundleSizeMB = metrics.bundleSize / 1024 / 1024
    if (bundleSizeMB > 50) {
      score -= 25
    } else if (bundleSizeMB > 30) {
      score -= 15
    } else if (bundleSizeMB > 20) {
      score -= 10
    }

    // 超大文件评分 (最多扣25分)
    if (metrics.overLimitFiles > 5) {
      score -= 25
    } else if (metrics.overLimitFiles > 3) {
      score -= 15
    } else if (metrics.overLimitFiles > 1) {
      score -= 10
    } else if (metrics.overLimitFiles > 0) {
      score -= 5
    }

    // 文件数量评分 (最多扣20分)
    if (metrics.fileCount > 200) {
      score -= 20
    } else if (metrics.fileCount > 150) {
      score -= 15
    } else if (metrics.fileCount > 100) {
      score -= 10
    }

    metrics.overallScore = Math.max(0, score)

    log.success('性能指标计算完成')

    // 保存到构建历史
    this.saveBuildHistory(metrics, bundleAnalysis)

    return metrics
  }

  /**
   * 保存构建历史 (已禁用JSON文件生成)
   */
  saveBuildHistory (metrics, bundleAnalysis) {
    // JSON文件生成已禁用以提升性能
    // 所有数据仍在内存中可用于对比分析
    log.debug('构建历史记录已保存到内存 (JSON文件生成已禁用)')
  }

  /**
   * 获取构建历史对比
   */
  getBuildComparison (currentMetrics) {
    try {
      if (!fs.existsSync(this.historyPath)) {
        return null
      }

      const historyData = fs.readFileSync(this.historyPath, 'utf8')
      const history = JSON.parse(historyData)

      if (history.length < 2) {
        return null
      }

      // 获取上一次构建记录
      const previousBuild = history[history.length - 2]

      const comparison = {
        buildTime: {
          current: currentMetrics.buildTime,
          previous: previousBuild.buildTime,
          change: currentMetrics.buildTime - previousBuild.buildTime,
          changePercent: previousBuild.buildTime > 0
            ? ((currentMetrics.buildTime - previousBuild.buildTime) / previousBuild.buildTime * 100).toFixed(1)
            : 0
        },
        bundleSize: {
          current: currentMetrics.bundleSize,
          previous: previousBuild.bundleSize,
          change: currentMetrics.bundleSize - previousBuild.bundleSize,
          changePercent: previousBuild.bundleSize > 0
            ? ((currentMetrics.bundleSize - previousBuild.bundleSize) / previousBuild.bundleSize * 100).toFixed(1)
            : 0
        },
        fileCount: {
          current: currentMetrics.fileCount,
          previous: previousBuild.fileCount,
          change: currentMetrics.fileCount - previousBuild.fileCount
        },
        overallScore: {
          current: currentMetrics.overallScore,
          previous: previousBuild.overallScore,
          change: currentMetrics.overallScore - previousBuild.overallScore
        }
      }

      return comparison
    } catch (error) {
      console.warn(chalk.yellow(`⚠️  获取构建对比失败: ${error.message}`))
      return null
    }
  }

  /**
   * 生成优化建议
   */
  generateRecommendations (performanceMetrics, bundleAnalysis) {
    log.info('生成优化建议...')

    const recommendations = []

    // 构建时间建议
    if (performanceMetrics.buildTime > 120000) {
      recommendations.push({
        category: '构建速度',
        issue: `构建时间过长: ${(performanceMetrics.buildTime / 1000).toFixed(0)}秒`,
        solution: '启用并行处理和缓存优化',
        priority: 'high'
      })
    }

    // 包大小建议
    const bundleSizeMB = performanceMetrics.bundleSize / 1024 / 1024
    if (bundleSizeMB > 30) {
      recommendations.push({
        category: '包大小',
        issue: `总包大小过大: ${bundleSizeMB.toFixed(2)}MB`,
        solution: '启用tree shaking和代码分割',
        priority: 'high'
      })
    }

    // 超大文件建议
    if (performanceMetrics.overLimitFiles > 0) {
      recommendations.push({
        category: '代码分割',
        issue: `${performanceMetrics.overLimitFiles}个文件超过1MB`,
        solution: '优化代码分割策略',
        priority: 'medium'
      })
    }

    // JavaScript特定建议
    if (bundleAnalysis.js.files.length > 100) {
      recommendations.push({
        category: 'JavaScript',
        issue: `JavaScript文件过多: ${bundleAnalysis.js.files.length}个`,
        solution: '合并小文件，优化chunk策略',
        priority: 'medium'
      })
    }

    // CSS特定建议
    if (bundleAnalysis.css.overLimitFiles.length > 0) {
      recommendations.push({
        category: 'CSS',
        issue: `${bundleAnalysis.css.overLimitFiles.length}个CSS文件过大`,
        solution: '启用CSS代码分割和压缩',
        priority: 'low'
      })
    }

    // 文件数量建议
    if (performanceMetrics.fileCount > 150) {
      recommendations.push({
        category: '文件管理',
        issue: `文件数量过多: ${performanceMetrics.fileCount}个`,
        solution: '优化资源合并策略',
        priority: 'low'
      })
    }

    log.success(`生成了${recommendations.length}条优化建议`)
    return recommendations
  }

  /**
   * 生成性能报告 (已禁用JSON文件生成)
   */
  generatePerformanceReport (performanceMetrics, bundleAnalysis, recommendations) {
    // JSON文件生成已禁用以提升性能
    // 所有报告数据仍在控制台输出中可见
    console.log(chalk.gray('   📄 性能报告已生成 (JSON文件生成已禁用)'))

    return {
      timestamp: new Date().toISOString(),
      performanceMetrics,
      bundleAnalysis,
      recommendations,
      summary: {
        overallScore: performanceMetrics.overallScore,
        buildTime: `${(performanceMetrics.buildTime / 1000).toFixed(2)}s`,
        bundleSize: `${(performanceMetrics.bundleSize / 1024 / 1024).toFixed(2)}MB`,
        fileCount: performanceMetrics.fileCount,
        issueCount: recommendations.length
      }
    }
  }

  /**
   * 显示性能摘要
   */
  displayPerformanceSummary (performanceMetrics, recommendations, bundleAnalysis = null) {
    console.log('')
    console.log(chalk.cyan('📊 构建性能分析摘要'))
    console.log('==================================================')

    // 基础构建信息
    console.log(chalk.blue('🏗️  构建基础信息:'))
    console.log(chalk.white(`   构建时间: ${performanceMetrics.buildTimeSeconds}s`))
    console.log(chalk.white(`   内存使用: ${(performanceMetrics.memoryUsage.heapUsed / 1024 / 1024).toFixed(2)}MB`))
    console.log(chalk.white(`   构建警告: ${performanceMetrics.warnings}个`))
    console.log(chalk.white(`   构建错误: ${performanceMetrics.errors}个`))

    // 文件统计信息
    console.log('')
    console.log(chalk.blue('📦 文件统计信息:'))
    console.log(chalk.white(`   JavaScript文件: ${performanceMetrics.jsFiles}个 (${(performanceMetrics.jsSize / 1024 / 1024).toFixed(2)}MB)`))
    console.log(chalk.white(`   CSS文件: ${performanceMetrics.cssFiles}个 (${(performanceMetrics.cssSize / 1024 / 1024).toFixed(2)}MB)`))
    console.log(chalk.white(`   其他资源: ${performanceMetrics.otherFiles}个 (${(performanceMetrics.otherSize / 1024 / 1024).toFixed(2)}MB)`))
    console.log(chalk.white(`   总计: ${performanceMetrics.fileCount}个文件 (${(performanceMetrics.bundleSize / 1024 / 1024).toFixed(2)}MB)`))

    if (performanceMetrics.overLimitFiles > 0) {
      console.log(chalk.yellow(`   ⚠️  超大文件: ${performanceMetrics.overLimitFiles}个`))
    }

    // 性能评分
    console.log('')
    console.log(chalk.blue('📊 性能评分:'))
    const scoreColor = performanceMetrics.overallScore >= 80 ? chalk.green
      : performanceMetrics.overallScore >= 60 ? chalk.yellow : chalk.red
    console.log(scoreColor(`   总体评分: ${performanceMetrics.overallScore}/100`))

    // 构建对比
    const comparison = this.getBuildComparison(performanceMetrics)
    if (comparison) {
      console.log('')
      console.log(chalk.blue('📈 与上次构建对比:'))

      // 构建时间对比
      const timeChange = parseFloat(comparison.buildTime.changePercent)
      const timeColor = timeChange < 0 ? chalk.green : timeChange > 10 ? chalk.red : chalk.yellow
      const timeSymbol = timeChange < 0 ? '⬇️' : timeChange > 0 ? '⬆️' : '➡️'
      console.log(timeColor(`   构建时间: ${timeSymbol} ${Math.abs(timeChange)}% (${(comparison.buildTime.change / 1000).toFixed(2)}s)`))

      // 包大小对比
      const sizeChange = parseFloat(comparison.bundleSize.changePercent)
      const sizeColor = sizeChange < 0 ? chalk.green : sizeChange > 5 ? chalk.red : chalk.yellow
      const sizeSymbol = sizeChange < 0 ? '⬇️' : sizeChange > 0 ? '⬆️' : '➡️'
      console.log(sizeColor(`   包大小: ${sizeSymbol} ${Math.abs(sizeChange)}% (${(comparison.bundleSize.change / 1024 / 1024).toFixed(2)}MB)`))

      // 文件数量对比
      const fileChange = comparison.fileCount.change
      const fileColor = fileChange === 0 ? chalk.white : fileChange > 0 ? chalk.yellow : chalk.green
      const fileSymbol = fileChange < 0 ? '⬇️' : fileChange > 0 ? '⬆️' : '➡️'
      console.log(fileColor(`   文件数量: ${fileSymbol} ${Math.abs(fileChange)}个`))

      // 评分对比
      const scoreChange = comparison.overallScore.change
      const scoreChangeColor = scoreChange > 0 ? chalk.green : scoreChange < 0 ? chalk.red : chalk.white
      const scoreChangeSymbol = scoreChange > 0 ? '⬆️' : scoreChange < 0 ? '⬇️' : '➡️'
      console.log(scoreChangeColor(`   性能评分: ${scoreChangeSymbol} ${Math.abs(scoreChange)}分`))
    }

    // 优化建议
    if (recommendations && recommendations.length > 0) {
      console.log('')
      console.log(chalk.yellow(`💡 优化建议 (${recommendations.length} 条):`))
      recommendations.slice(0, 5).forEach((rec, index) => {
        const priorityColor = rec.priority === 'high' ? chalk.red
          : rec.priority === 'medium' ? chalk.yellow : chalk.gray
        console.log(priorityColor(`   ${index + 1}. [${rec.category}] ${rec.issue}`))
        console.log(chalk.gray(`      解决方案: ${rec.solution}`))
      })

      if (recommendations.length > 5) {
        console.log(chalk.gray(`   ... 还有 ${recommendations.length - 5} 条建议，查看完整报告了解详情`))
      }
    }

    // 下一步建议
    console.log('')
    console.log(chalk.blue('🚀 建议的下一步操作:'))
    if (performanceMetrics.overallScore < 60) {
      console.log(chalk.yellow('   1. 查看上述优化建议并逐项改进'))
      console.log(chalk.yellow('   2. 重新构建并对比性能提升'))
    } else if (performanceMetrics.overallScore < 80) {
      console.log(chalk.green('   1. 性能良好，可考虑进一步优化'))
      console.log(chalk.green('   2. 上传到CDN: node run.js upload'))
    } else {
      console.log(chalk.green('   1. 性能优秀！可以部署到生产环境'))
      console.log(chalk.green('   2. 上传到CDN: node run.js upload'))
      console.log(chalk.green('   3. 完整部署: node run.js deploy'))
    }
  }
}

module.exports = {
  BuildPerformanceAnalyzer
}
