<template>
  <div class="page">
    <div class="search-form" style="display: flex; align-items: center">
      <div class="left">
        <el-form :model="form" label-width="100px" inline size="mini">
          <el-form-item style="margin-top: 5px">
            <el-input v-model="form.sampleCodeAccurate" :rows="3" type="textarea" class="input-width"
              placeholder="编号精准搜索"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div class="right">
        <div class="search" style="min-height: 60px;">
          <div>
            <el-form :model="form" label-width="100px" inline size="mini" @keyup.enter.native="handleSearch">
              <el-form-item label="样例编号">
                <el-input v-model.trim="form.sampleCode" class="input-width"
                  @keyup.enter.native="handleSearch"></el-input>
              </el-form-item>
              <el-form-item label="信息补录">
                <el-cascader v-model="form.infoSupplement" :show-all-levels="false" :options="infoSupplementOptions"
                  :props="{ multiple: true }" collapse-tags clearable></el-cascader>
              </el-form-item>
              <el-form-item label="我的任务">
                <el-checkbox v-model="form.myTask"></el-checkbox>
              </el-form-item>
              <template>
                <el-form-item label="到样时间">
                  <el-date-picker v-model="form.times[0]" value-format="yyyy-MM-dd" type="date" size="mini" class="date"
                    placeholder="选择日期">
                  </el-date-picker>
                  <span>--</span>
                  <el-date-picker v-model="form.times[1]" type="date" value-format="yyyy-MM-dd" size="mini" class="date"
                    placeholder="选择日期">
                  </el-date-picker>
                </el-form-item>
              </template>
              <el-form-item>
                <el-button type="primary" @click="handleChange">{{showAdvancedSearch ? '隐藏' : '高级'}}查询</el-button>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" size="mini" plain @click="handleSearch">查询</el-button>
                <el-button size="mini" plain @click="handleReset">重置</el-button>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </div>
    </div>
    <!--高级查询-->
    <el-collapse-transition>
      <div class="search advanceSearch" style="height: auto;" v-show="showAdvancedSearch">
        <div>
          <el-form :model="form" label-width="110px" inline size="mini" @keyup.enter.native="handleSearch">
            <el-row>
              <el-col :span="6">
                <el-form-item label="任务状态">
                  <el-select v-model="form.taskStatus" size="mini" class="input-width">
                    <el-option label="全部" value=""></el-option>
                    <el-option :value="0" label="未领取"></el-option>
                    <el-option :value="1" label="已领取"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="姓名">
                  <el-input v-model.trim="form.name" class="input-width"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="身份证/护照">
                  <el-input v-model.trim="form.idCard" class="input-width"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <!-- <el-form-item label="送检单位">
                  <el-input v-model.trim="form.unit" class="input-width"></el-input>
                </el-form-item> -->
              </el-col>
            </el-row>
            <el-row>
              <!-- <el-col :span="6">
                <el-form-item label="送检医生">
                  <el-input v-model.trim="form.doctor" class="input-width"></el-input>
                </el-form-item>
              </el-col> -->
              <el-col :span="6">
                <el-form-item label="订单联系人1">
                  <el-input v-model.trim="form.seller" class="input-width"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="流转方式">
                  <el-select v-model="form.researchClinical" class="input-width">
                    <!-- <el-option :key="k" :label="v" :value="k" v-for="(v, k) in researchClinicalType"></el-option> -->
                    <el-option label="全部" value="2"></el-option>
                    <el-option label="非自动" value="0"></el-option>
                    <el-option label="自动" value="1"></el-option>
                    <el-option label="科研A" value="3"></el-option>
                    <!-- <el-option label="科研B" value="4"></el-option> -->
                    <!--<el-option label="临床" :value="1"></el-option>-->
                    <!--<el-option label="科研" :value="2"></el-option>-->
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="项目名称">
                  <el-input v-model.trim="form.projectName" class="input-width"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="6">
                <el-form-item label="生产片区">
                  <el-select v-model.trim="form.productAreaIds" multiple class="input-width">
                    <el-option :key="item.value" :label="item.label" :value="item.value"
                      v-for="item in lab"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <!-- <el-form-item label="客户名称">
                  <el-input class="input-width" v-model.trim="form.customerName"></el-input>
                </el-form-item> -->
              </el-col>
              <el-col :span="6">
                <el-form-item label="订单联系人2">
                  <el-input class="input-width" v-model.trim="form.orderEntryPerson"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </div>
    </el-collapse-transition>
    <div class="operate-btns-group">
      <el-button v-if="$setAuthority('003009001', 'buttons')" type="primary" size="mini" plain
        @click="handleReceiveTask">
        <icon-svg class="iconClass" icon-class="icon-start-copy"></icon-svg>任务领取
      </el-button>
      <el-button v-if="$setAuthority('003009002', 'buttons')" type="primary" size="mini" plain
        @click="handleInformationSupplement">
        <icon-svg class="iconClass" icon-class="icon-luru1"></icon-svg>信息补录
      </el-button>
      <el-button v-if="$setAuthority('003009003', 'buttons')" type="primary" size="mini" plain
        @click="handleInformationReview">
        <icon-svg class="iconClass" icon-class="icon-shenhe"></icon-svg>信息审核
      </el-button>
      <el-button v-if="$setAuthority('003009004', 'buttons')" type="primary" size="mini" plain
        @click="handleSuspiciousModification">
        <icon-svg class="iconClass" icon-class="icon-luru1"></icon-svg>存疑修改
      </el-button>
      <el-button v-if="$setAuthority('003009005', 'buttons')" type="primary" size="mini" plain
        @click="handleViewDetail">
        <icon-svg class="iconClass" icon-class="icon-mingxi"></icon-svg>查看明细
      </el-button>
      <el-button :loading="exportLoading" v-if="$setAuthority('003009006', 'buttons')" type="primary" size="mini" plain
        @click="handleExport">
        <icon-svg class="iconClass" icon-class="icon-Dzu"></icon-svg>导出
        {{exportLoading ? '中' : ''}}</el-button>
      <el-button v-if="$setAuthority('003009007', 'buttons')" type="primary" size="mini" plain
        @click="handleAuditDialog">
        <icon-svg class="iconClass" icon-class="icon-fuzhi"></icon-svg>操作记录
      </el-button>
    </div>
    <div class="content">
      <div>
        <el-table ref="table" :data="tableData"
          :height="`calc(100vh - 74px - 40px - 83px - 42px - 32px - ${advancedSearchHeight}px)`"
          :cell-style="handleRowStyle" class="clinicalInfoTable" size="mini" border style="width: 100%;"
          @select="handleSelectTable" @row-click="handleRowClick" @select-all="handleSelectAll">
          <el-table-column :selectable="checkedTableCanSelect" type="selection" width="45"
            fixed="left"></el-table-column>
          <el-table-column type="index" label="序号" width="50" fixed="left"></el-table-column>
          <el-table-column prop="testingLink" label="检测环节" width="80" show-overflow-tooltip></el-table-column>
          <el-table-column prop="taskStatusText" label="任务状态" width="70" show-overflow-tooltip></el-table-column>
          <el-table-column prop="sampleCode" label="样例编号" width="90" show-overflow-tooltip></el-table-column>
          <el-table-column prop="name" label="姓名" width="80" show-overflow-tooltip>
            <template slot-scope="scope">
              <desensitization :info="scope.row.name" type="name"></desensitization>
            </template>
          </el-table-column>
          <el-table-column prop="infoText" label="补录状态" width="90" show-overflow-tooltip></el-table-column>
          <el-table-column prop="rejectNotes" label="驳回备注" width="100" show-overflow-tooltip></el-table-column>
          <!-- <el-table-column prop="researchClinicalText" label="科研临床" width="70" show-overflow-tooltip></el-table-column> -->
          <!-- <el-table-column prop="productCode" label="产品/项目编号" width="100" show-overflow-tooltip></el-table-column> -->
          <el-table-column prop="productName" label="项目名称" width="160" show-overflow-tooltip></el-table-column>
          <!-- <el-table-column prop="unit" label="送检单位" width="140" show-overflow-tooltip></el-table-column> -->
          <!-- <el-table-column prop="customerName" label="客户名称" width="140" show-overflow-tooltip></el-table-column> -->
          <el-table-column prop="sampleTime" label="到样时间" min-width="140" show-overflow-tooltip></el-table-column>
          <el-table-column prop="cardType" label="证件类型" min-width="140" show-overflow-tooltip></el-table-column>
          <el-table-column prop="idCard" label="证件号" min-width="140" show-overflow-tooltip>
            <template slot-scope="scope">
              <desensitization :info="scope.row.idCard" type="idCard"></desensitization>
            </template>
          </el-table-column>
          <el-table-column label="订单联系人1" min-width="120" show-overflow-tooltip>
            <template slot-scope="scope">
              <template v-if="scope.row.salesType !== '-'">
                {{scope.row.sales}} / {{scope.row.salesType}}
              </template>
              <template v-else>
                {{scope.row.sales}}
              </template>
            </template>
          </el-table-column>
          <el-table-column prop="factotumName" label="订单联系人2" min-width="100" show-overflow-tooltip></el-table-column>
          <el-table-column prop="phone" label="联系电话" min-width="100" show-overflow-tooltip>
            <template slot-scope="scope">
              <desensitization :info="scope.row.phone" type="phone"></desensitization>
            </template>
          </el-table-column>
          <!-- <el-table-column prop="doctor" label="送检医生" width="80" show-overflow-tooltip></el-table-column> -->
          <el-table-column prop="createTime" label="下单时间" min-width="140" show-overflow-tooltip></el-table-column>
          <el-table-column prop="makeUpPerson" label="补录人" min-width="80" show-overflow-tooltip></el-table-column>
          <el-table-column prop="makeUpTime" label="补录时间" min-width="140" show-overflow-tooltip></el-table-column>
          <el-table-column prop="reviewer" label="审核人" min-width="80" show-overflow-tooltip></el-table-column>
          <el-table-column prop="reviewTime" label="审核时间" min-width="180" show-overflow-tooltip></el-table-column>
          <el-table-column prop="doubtMan" label="存疑人" min-width="80" show-overflow-tooltip></el-table-column>
          <el-table-column prop="doubtTime" label="存疑时间" min-width="140" show-overflow-tooltip></el-table-column>
          <el-table-column prop="doubtRemark" label="存疑备注" min-width="140" show-overflow-tooltip></el-table-column>
          <el-table-column prop="patientId" label="患者编号" min-width="120" show-overflow-tooltip></el-table-column>
          <el-table-column prop="productAreaText" label="生产片区" min-width="80" show-overflow-tooltip></el-table-column>
        </el-table>
        <el-pagination :page-sizes="pageSizes" :page-size="pageSize" :current-page.sync="currentPage" :total="totalPage"
          layout="total, sizes, prev, pager, next, jumper, slot" @size-change="handleSizeChange"
          @current-change="handleCurrentChange">
          <button @click="handleRefresh"><icon-svg icon-class="icon-refresh" /></button>
        </el-pagination>
      </div>
    </div>
    <save-info-dialog :key="saveInfoDialogKey" :pvisible.sync="saveInfoDialogVisible" :type="saveInfoDialogData.type"
      :sample-basic-id="saveInfoDialogData.sampleBasicId" :sample-num="saveInfoDialogData.sampleNum"
      @saveInfoDialogConfirmEvent="handleSaveInfoDialogConfirm"></save-info-dialog>
    <audit-drawer :pvisible.sync="auditDrawerData.visible" :sample-basic-id="auditDrawerData.sampleBasicId" />
  </div>
</template>

<script>
import Cookies from 'js-cookie'
import mixins from '../../../util/mixins'
import util from '../../../util/util'
import saveInfoDialog from './pathogenSampleSaveInfoDialog'
import auditDrawer from '../sample/clinicalInfoAuditDrawer' // 审核记录

export default {
  name: 'pathogenSampleManagement',
  mixins: [mixins.tablePaginationCommonData],
  components: {
    saveInfoDialog,
    auditDrawer
  },
  // beforeMount () {
  //   this.$_setTbHeight()
  //   window.addEventListener('resize', this.$_setTbHeight)
  //   this.$once('hook:beforeDestroy', () => {
  //     window.removeEventListener('resize', this.$_setTbHeight)
  //   })
  // },
  mounted () {
    let lab = Cookies.get('flab').split(',') || []
    let labOptions = JSON.parse(Cookies.get('labOptions'))
    this.lab = labOptions.filter(v => {
      return lab.includes(`${v.value}`)
    })
    const startDate = new Date(new Date().setMonth(new Date().getMonth() - 1))
    this.form.times = [util.dateFormatter(startDate, false), util.dateFormatter(new Date(), false)]
    this.handleSearch()
  },
  computed: {
    username () {
      return this.$store.getters.getValue('userInfo').name
    },
    emailPrefix () {
      return this.$store.getters.getValue('userInfo').emailPrefix
    }
  },
  data () {
    return {
      lab: [], // 生产片区列表
      selectedRows: new Map(),
      showAdvancedSearch: false, // 显示高级查询
      advancedSearchHeight: 0, // 高级查询高度
      viewMicroscopyResultDialogData: { // 查看镜检结果弹窗数据
        visible: false,
        sampleNum: ''
      },
      countDialogData: { // 信息补录统计弹窗数据
        visible: false
      },
      copyInfoDialogData: { // 信息复制弹窗数据
        visible: false,
        sampleNum: ''
      },
      mergeDialogData: { // 合并弹窗数据
        visible: false,
        tableData: []
      },
      fastEntryData: { // 快速录入弹窗
        visible: false
      },
      auditDrawerData: { // 审核记录
        visible: false,
        sampleBasicId: 0
      },
      exportLoading: false,
      form: {
        sampleCode: '',
        infoSupplement: [[5, 0], [5, 1], [5, 2], [5, 3]],
        myTask: false,
        taskStatus: '',
        name: '',
        idCard: '',
        researchClinical: '2',
        sampleCodeAccurate: '',
        productAreaIds: [], // 生产片区
        unit: '',
        projectName: '',
        approvalNum: '', // 审批号
        searchTimeType: '', // 时间类型
        times: ['', ''],
        doctor: '',
        seller: '',
        patientCode: '',
        fisName: '',
        fisMultiTag: '',
        fisPastTag: '',
        fisAddTag: '',
        fisUpdateTag: '',
        fisFollowUps: '',
        customerName: '',
        orderEntryPerson: ''
      },
      tableHeight: 0,
      formSubmit: {}, // 提交的form
      infoSupplementOptions: [
        {
          label: '全选',
          value: 5,
          children: [
            // {value: 1, label: '前端未补录'},
            {value: 0, label: '未补录'},
            {value: 1, label: '补录中'},
            {value: 2, label: '驳回'},
            {value: 3, label: '待审'},
            {value: 4, label: '审核通过'},
            {value: 6, label: '前端未补录'}
          ]
        }
      ],
      infoSupplement: {
        0: '未补录',
        1: '补录中',
        4: '审核通过',
        2: '驳回',
        3: '待审',
        7: '样本暂存'
      },
      taskStatusType: {
        0: '未领取',
        1: '已领取'
      },
      researchClinicalType: {
        2: '全部',
        0: '非自动',
        1: '自动',
        3: '科研A',
        4: '科研B'
      },
      productAreaType: {
        1: '北京',
        2: '深圳'
      },
      searchTimeType: 'sampleTime', // 高级查询的事件类型 到样时间：sampleTime 补录时间：makeUpTime 审核时间：reviewTime
      saveInfoDialogVisible: false,
      saveInfoDialogKey: 1, // 保存弹窗的key
      saveInfoDialogData: {
        type: 0,
        sampleBasicId: 0,
        sampleNum: '',
        fisFollowUp: 3
      }
    }
  },
  methods: {
    checkedTableCanSelect (row) {
      return row.infoText !== '样本暂存'
    },
    handleChange () {
      this.showAdvancedSearch = !this.showAdvancedSearch
      this.showAdvancedSearch ? this.advancedSearchHeight = 191 : this.advancedSearchHeight = 0
    },
    // $_setTbHeight () {
    //   let h1 = document.documentElement.clientHeight - 1
    //   this.tableHeight = h1 - 64 - 50 - 20 - 60 - 45 - 42 - 20
    // },
    // 打开审核记录抽屉组件
    handleAuditDialog () {
      if (this.selectedRows.size !== 1) {
        this.$message.error('请选择一行数据')
        return
      }
      let row = [...this.selectedRows.values()][0]
      this.auditDrawerData.visible = true
      this.auditDrawerData.sampleBasicId = row.id
    },
    // 选中行样式改变
    handleRowStyle ({row, rowIndex}) {
      if (this.selectedRows.has(row.id)) {
        return {backgroundColor: '#c7e1ff'}
      }
      return ''
    },
    // 搜索
    handleSearch () {
      this.formSubmit = {...this.form}
      // this.form.infoSupplement = [[5, 1], [5, 2]]
      if (this.form.infoSupplement.length === 0) {
        this.formSubmit.infoSupplement = ''
      } else if (this.form.infoSupplement.length === this.infoSupplementOptions[0].children.length) { // 全选
        this.formSubmit.infoSupplement = 5
      } else {
        this.formSubmit.infoSupplement = this.form.infoSupplement.map(v => {
          return v[1]
        })
      }
      this.currentPage = 1
      this.getData()
    },
    // 获取病原体样本列表数据
    getData () {
      const types = {
        0: '其他',
        1: '居民身份证',
        2: '护照',
        3: '军官证',
        4: '港澳通行证',
        5: '社保卡'
      }
      this.$ajax({
        url: '/sample/basic/get_sample_list',
        data: {
          page: {
            current: this.currentPage,
            size: this.pageSize
          },
          params: {
            sampleType: 2, // 样本类型 1 肿瘤 2 病原
            sampleNum: this.formSubmit.sampleCode,
            sampleStatus: this.formSubmit.infoSupplement.toString(),
            isExpress: this.formSubmit.myTask ? this.emailPrefix : '',
            clinicalStatus: this.formSubmit.taskStatus,
            name: this.formSubmit.name,
            idcard: this.formSubmit.idCard,
            productAreaIds: this.formSubmit.productAreaIds, // 生产片区
            sampleUseType: this.formSubmit.researchClinical, // 病原样本没有科研临床筛选
            geneSampleNums: this.formSubmit.sampleCodeAccurate.replace(/\s+|，/g, ',').split(',').filter(v => v).join(','),
            inspectionUnit: this.formSubmit.unit,
            productName: this.formSubmit.projectName,
            projectNum: this.formSubmit.approvalNum,
            doctor: this.formSubmit.doctor,
            sampleCollectMan: this.formSubmit.seller,
            patientId: this.formSubmit.patientCode,
            isAuditOrNot: this.formSubmit.searchTimeType,
            timeStart: this.formSubmit.times[0],
            timeEnd: this.formSubmit.times[1],
            fisName: this.formSubmit.fisName,
            fisMultiTag: this.formSubmit.fisMultiTag,
            fisPastTag: this.formSubmit.fisPastTag,
            fisAddTag: this.formSubmit.fisAddTag,
            fisUpdateTag: this.formSubmit.fisUpdateTag,
            fisFollowUps: this.formSubmit.fisFollowUps,
            customerName: this.formSubmit.customerName,
            orderEntryPerson: this.formSubmit.orderEntryPerson
          }
        },
        loadingDom: '.clinicalInfoTable'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.selectedRows.clear()
          this.totalPage = res.data.total
          let rows = res.data.rows || []
          this.tableData = []
          rows.forEach(v => {
            let item = {
              id: v.sampleBasicId,
              sampleTag: v.isMuliCheck ? v.isMuliCheck.split(',') : [],
              testingLink: v.currentStep,
              taskStatus: v.clinicalStatus,
              taskStatusText: this.taskStatusType[v.clinicalStatus] || '',
              sampleCode: v.sampleNum,
              name: v.name,
              info: v.clinicalMkSta,
              infoText: this.setInfo(v),
              rejectNotes: v.rejectRmk,
              createTime: v.createTime,
              researchClinical: v.samleUseType,
              researchClinicalText: this.researchClinicalType[v.samleUseType] || '',
              productCode: v.proCode,
              productName: v.proName,
              unit: v.hospital,
              doctor: v.doctor,
              sales: v.salesManName,
              salesType: v.salesManType,
              factotumName: v.factotumName,
              phone: v.salesManPhone,
              sampleTime: v.sampleConfirmTime,
              preDeliveryTime: v.preDeliverDate,
              makeUpPerson: v.clinicalMkMan,
              makeUpTime: v.clinicalMkTime,
              reviewer: v.clinicalAuditMan,
              reviewTime: v.clinicalAuditTime,
              doubtMan: v.doubtMan,
              doubtTime: v.doubtTime,
              idCard: v.idCard,
              cardType: types[v.cardType],
              doubtRemark: v.doubtRemark,
              patientId: v.patientId,
              productArea: v.productArea,
              productAreaText: this.productAreaType[v.productArea] || '',
              isBmsStatus: v.isBmsStatus,
              isName: v.fisName,
              fisAddTag: v.fisAddTag,
              fisUpdateTag: v.fisUpdateTag,
              fisPastTag: v.fisPastTag,
              fisFollowUp: v.fisFollowUp,
              customerName: v.customerName,
              temporaryStorageStatus: v.temporaryStorageStatus // 为Y时表示暂存样本
            }
            item.realData = util.deepCopy(item)
            util.setDefaultEmptyValueForObject(item)
            this.tableData.push(item)
          })
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 设置补录状态
    setInfo (v) {
      let r = ''
      if (v.clinicalMkSta === 7) {
        r = this.infoSupplement[v.clinicalMkSta]
      } else if (v.isBmsStatus === 0) {
        r = '前端未补录'
      } else {
        r = this.infoSupplement[v.clinicalMkSta] || ''
      }
      return r
    },
    // 选择时间的改变
    handleSearchTimeChange () {
      this.form.times = ['', '']
    },
    // 重置
    handleReset () {
      this.$refs.table.clearFilter()
      this.form = {
        sampleCode: '',
        infoSupplement: [[5, 0], [5, 1], [5, 2], [5, 3]],
        myTask: false,
        taskStatus: '',
        name: '',
        idCard: '',
        researchClinical: '2',
        sampleCodeAccurate: '',
        productAreaIds: [], // 生产片区
        unit: '',
        projectName: '',
        approvalNum: '', // 审批号
        searchTimeType: '', // 时间类型
        times: ['', ''],
        doctor: '',
        seller: '',
        patientCode: '',
        fisName: '',
        fisMultiTag: '',
        fisPastTag: '',
        fisAddTag: '',
        fisUpdateTag: '',
        fisFollowUps: '',
        customerName: '',
        orderEntryPerson: ''
      }
      const startDate = new Date(new Date().setMonth(new Date().getMonth() - 1))
      this.form.times = [util.dateFormatter(startDate, false), util.dateFormatter(new Date(), false)]
      this.handleSearch()
    },
    // 任务领取 => 未领取且未补录
    handleReceiveTask () {
      if (this.selectedRows.size === 0) {
        this.$message.error('请选择需要领取的任务')
        return
      }
      let values = [...this.selectedRows.values()]
      let keys = [...this.selectedRows.keys()]
      // 只有未领取且未补录才能领取
      let coincidence = values.every(item => {
        return item.taskStatus === 0
      })
      if (!coincidence) {
        this.$alert('只能选择未领取的样本', '提示', {type: 'error'})
        return
      }
      this.$ajax({
        url: '/sample/basic/receive_clinical_task',
        data: {
          sampleType: 2, // 样本类型 1 肿瘤 2 病原
          sampleBasicIds: keys.toString()
        },
        loadingDom: '.page'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.$message.success('领取成功')
          this.getData()
        } else {
          this.$message.error(res.message)
        }
      })
    },
    // 信息补录弹窗的重新加载展示
    reloadSaveInfoDialog () {
      this.saveInfoDialogKey += 1
      setTimeout(() => {
        this.saveInfoDialogVisible = true
        this.handleRecord()
      })
    },
    // 审核记录
    handleRecord () {
      let operationName = ''
      switch (this.saveInfoDialogData.type) {
        case 1: operationName = '样本补录'
          break
        case 2: operationName = '信息审核'
          break
        case 3: operationName = '存疑修改'
          break
      }
      this.$ajax({
        url: '/sample/basic/save_operation_record',
        method: 'post',
        data: {
          sampleBasicId: this.saveInfoDialogData.sampleBasicId,
          operationName: operationName
        }
      })
    },
    // 信息补录
    handleInformationSupplement () {
      if (this.selectedRows.size !== 1) {
        this.$message.error('请选择一条数据')
        return
      }
      let row = [...this.selectedRows.values()][0]
      if (row.taskStatus === 0) {
        this.$alert('请先领取任务!', '提示', {type: 'error'})
        return
      }
      // 驳回状态 => 存疑审核驳回不能走补录
      if (row.info === 2 && row.realData.doubtMan) {
        this.$alert('当前状态，只能由存疑人操作“存疑修改”进行修改信息！', '提示', {type: 'error'})
        return
      }
      if (this.emailPrefix !== row.makeUpPerson) {
        this.$alert('您不是任务领取人，不能补录！', '提示', {type: 'error'})
        return
      }
      if (row.taskStatus !== 1) {
        this.$alert('请先领取任务！', '提示', {type: 'error'})
        return
      }
      if (row.info !== 0 && row.info !== 1 && row.info !== 2) {
        this.$alert('只能选择未补录，补录中或驳回的记录进行补录！', '提示', {type: 'error'})
        return
      }
      this.saveInfoDialogData = {
        type: 1,
        sampleBasicId: row.id,
        sampleNum: row.sampleCode,
        fisFollowUp: row.fisFollowUp
      }
      this.reloadSaveInfoDialog()
      // this.saveInfoDialogVisible = true
    },
    // 信息审核
    handleInformationReview () {
      if (this.selectedRows.size !== 1) {
        this.$message.error('请选择一条数据')
        return
      }
      let row = [...this.selectedRows.values()][0]
      if (row.taskStatus === 0) {
        this.$alert('请先领取任务!', '提示', {type: 'error'})
        return
      }
      if (row.info !== 3) {
        this.$alert('必须是待审样例才能审核！', '提示', {type: 'error'})
        return
      }
      if (this.emailPrefix === row.makeUpPerson) {
        this.$alert('补录人和审核人不能是同一个人！', '提示', {type: 'error'})
        return
      }
      this.saveInfoDialogData = {
        type: 2,
        sampleBasicId: row.id,
        sampleNum: row.sampleCode
      }
      this.reloadSaveInfoDialog()
      // this.saveInfoDialogVisible = true
    },
    // 存疑修改
    handleSuspiciousModification () {
      if (this.selectedRows.size !== 1) {
        this.$message.error('请选择一条数据')
        return
      }
      let row = [...this.selectedRows.values()][0]
      if (row.taskStatus === 0) {
        this.$alert('请先领取任务!', '提示', {type: 'error'})
        return
      }
      if (row.info !== 4 && row.info !== 2) {
        this.$alert('必须是已审样例或驳回样例才能存疑修改！', '提示', {type: 'error'})
        return
      }
      // 驳回 => 只能由存疑人进行修改信息
      if (row.info === 2 && row.doubtMan !== this.emailPrefix) {
        this.$alert('当前状态下只能由存疑人进行修改信息！', '提示', {type: 'error'})
        return
      }
      this.saveInfoDialogData = {
        type: 3,
        sampleBasicId: row.id,
        sampleNum: row.sampleCode,
        fisFollowUp: row.fisFollowUp
      }
      this.reloadSaveInfoDialog()
      // this.saveInfoDialogVisible = true
    },
    // 查看明细
    handleViewDetail () {
      if (this.selectedRows.size !== 1) {
        this.$message.error('请选择一条数据')
        return
      }
      let row = [...this.selectedRows.values()][0]
      this.$store.commit({
        type: 'old/setValue',
        category: 'clinicalInfo',
        clinicalInfo: {
          sampleBasicId: row.id,
          sampleNum: row.sampleCode,
          fisFollowUp: row.fisFollowUp
        }
      })
      util.openNewPage('/business/sub/pathogenInfoManagementInfoDetail')
    },
    // 导出
    handleExport () {
      if (this.selectedRows.size === 0) {
        this.$message.error('请至少选择一条数据')
        return
      }
      let NotAudit = []
      let sampleBasicId = [...this.selectedRows.values()].map(v => {
        if (v.info !== 4) {
          NotAudit.push(v.id)
        }
        return v.id
      })
      if (NotAudit.length > 0) {
        this.$message.error(`样本编号${NotAudit.join(',')}未通过审核, 请选择审核通过的样本！`)
        return
      }
      this.exportLoading = true
      this.$ajax({
        url: '/sample/basic/download_pathogeny_sample_excel',
        data: {
          sampleBasicIds: sampleBasicId.join(',')
        },
        method: 'get',
        responseType: 'blob'
      }).then(res => {
        util.readBlob(res.data).then(() => {
          util.downloadFile(res, true)
        }).catch(msg => {
          this.$message.error(msg)
        })
      }).finally(() => {
        this.exportLoading = false
      })
    },
    // 保存信息弹窗确认
    handleSaveInfoDialogConfirm () {
      this.saveInfoDialogVisible = false
      this.getData()
    },
    // 保存信息关闭
    handleSaveInfoDialogClose () {
      this.saveInfoDialogVisible = false
      this.getData()
    },
    // 点击行
    handleRowClick (row, c) {
      this.$refs.table.toggleRowSelection(row, !this.selectedRows.has(row.id))
      this.handleSelectTable(undefined, row)
    },
    // 选中行
    handleSelectTable (selection, row) {
      this.selectedRows.has(row.id) ? this.selectedRows.delete(row.id) : this.selectedRows.set(row.id, row)
    },
    // 全选
    handleSelectAll (selection) {
      console.log(selection)
      this.selectedRows.clear()
      selection.forEach((row) => {
        this.selectedRows.set(row.id, row)
      })
    }
  }
}
</script>

<style scoped lang="scss">
.input-width{
  width: 150px;
}
.search{
  background-color: #ffffff;
  display: flex;
  align-items: center;
  .search-row{
    display: flex;
    width: auto;
    margin-bottom: 20px;
    & > div{
      margin-right: 30px;
      label{
        font-size: 14px;
        color: #606266;
        display: inline-block;
        width: 100px;
        text-align: right;
        padding: 0 12px 0 0;
        box-sizing: border-box;
      }
      .input{
        width: 200px;
      }
    }
  }
}
.content{
  background-color: #ffffff;
  .buttonGroup{
    height: 45px;
    display: flex;
    align-items: center;
    //margin: 0 20px;
    >>>.el-button--mini{
      padding: 5px 10px;
    }
    .iconClass{
      padding-right: 5px;
      font-size: 16px;
    }
  }
}
.advanceSearch {
  transition: all .3s;
}
>>>.el-pagination{
  padding: 7px 2em;
}
</style>
