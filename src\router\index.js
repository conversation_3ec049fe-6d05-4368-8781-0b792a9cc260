import Vue from 'vue'
import Router from 'vue-router'
import HelloWorld from '@/components/HelloWorld'
import Main from '@/components/main'
import constants from '@/util/constants'
import axios from 'axios'
import util from '../util/util'

// vue-router在v3.1版本会有一个报重复错误的bug
// 现在可以回退router版本到3.0
// 也可以如下重写 push 方法
const routerPush = Router.prototype.push
Router.prototype.push = function push (location) {
  return routerPush.call(this, location).catch(error => error)
}

Vue.use(Router)

const routes = new Router({
  mode: 'history',
  routes: [
    {
      path: '/',
      name: 'HelloWorld',
      component: HelloWorld
    },
    {
      path: '/500',
      component: () => import('@/components/common/errorPage.vue')
    },
    {
      path: '/login',
      component: () => import('@/components/system/login/login.vue')
    },

    /**
     * 科服科服illumina订单详情订单修改
     * */
    {
      path: '/technologyService/entryIlluminaChangeInfo',
      component: () => import('@/components/business/sample/technologyService/orderReview/orderDetail/illumina/changeInfo.vue')
    },

    /**
     * 科服MGI订单修改
     * */
    {
      path: '/technologyService/entryMGIChangeInfo',
      component: () => import('@/components/business/sample/technologyService/orderReview/orderDetail/mgi/changeInfo.vue')
    },
    /**
     * 科服组织或核酸订单修改
     * */
    {
      path: '/technologyService/entryTissueChangeInfo',
      component: () => import('@/components/business/sample/technologyService/orderReview/orderDetail/tissueOrNucleicAcid/changeInfo.vue')
    },
    /**
     * 科服组织或核酸订单修改
     * */
    {
      path: '/technologyService/singleCell',
      component: () => import('@/components/business/sample/technologyService/orderReview/orderDetail/singleCell/changeInfo.vue')
    },
    {
      path: '/test',
      meta: {
        title: '个性化MRD样本监控'
      },
      component: () => import('@/components/business/test/index.vue')
    },
    {
      path: '/business',
      component: Main,
      children: [
        {
          path: 'view',
          component: () => import('@/components/layout/index.vue'),
          redirect: 'view/containerManagement',
          children: [
            {
              path: 'mrdMonitorManagement',
              meta: {
                title: '个性化MRD样本监控'
              },
              component: () => import('@/components/business/statistical/mrdMonitorManagement/index.vue')
            },
            {
              path: 'containerManagement',
              meta: {
                title: '容器管理'
              },
              component: () => import('@/components/business/containerManagement/overview.vue')
            },
            {
              path: 'sampleSearch',
              meta: {
                title: '样本查询'
              },
              component: () => import('@/components/business/sampleLibraryManagement/sampleSearch.vue')
            },
            {
              path: 'turnoverLibraryManagement',
              meta: {
                title: '出入库管理'
              },
              component: () => import('@/components/business/sampleLibraryManagement/turnoverLibraryManagement.vue')
            },
            {
              path: 'applicationForStorage',
              meta: {
                title: '出入库申请'
              },
              component: () => import('@/components/business/sampleLibraryManagement/applicationForStorage.vue')
            },
            {
              path: 'abnormalSampleManagement',
              meta: {
                title: '异常样本管理'
              },
              component: () => import('@/components/business/sampleLibraryManagement/abnormalSampleManagement.vue')
            },
            {
              path: 'backSampleManagement',
              meta: {
                title: '返样管理'
              },
              component: () => import('@/components/business/sampleLibraryManagement/backSampleManagement.vue')
            },
            {
              path: 'templateManagement',
              meta: {
                title: '报告模板管理'
              },
              component: () => import('@/components/business/templateManagement/overview.vue')
            },
            {
              path: 'moduleManagement',
              meta: {
                title: '报告模块管理'
              },
              component: () => import('@/components/business/moduleManagement/overview.vue')
            },
            {
              path: 'patientInfoDetail',
              meta: {
                title: '患者信息详情'
              },
              component: () => import('@/components/business/sample/patientDetail/patientInfoDetail.vue')
            },
            {
              path: 'sample/abnormalSampleManagement',
              meta: {
                title: '样例异常处理'
              },
              component: () => import('@/components/business/sample/abnormalSampleManagement.vue')
            },
            {
              path: 'processFlowManagement',
              meta: {
                title: '工序流程管理'
              },
              component: () => import('@/components/business/basicDataManagement/processFlowManagement.vue')
            },
            {
              path: 'processStepManagement',
              meta: {
                title: '工序步骤管理'
              },
              component: () => import('@/components/business/basicDataManagement/processStepManagement.vue')
            },
            {
              path: 'controlStandardManagement',
              meta: {
                title: '对照标准管理'
              },
              component: () => import('@/components/business/basicDataManagement/controlStandardManagement.vue')
            },
            {
              path: 'reportNodulusManagement',
              meta: {
                title: '报告小结配置'
              },
              component: () => import('@/components/business/basicDataManagement/reportNodulusManagement/index.vue')
            },
            {
              path: 'clinicalInfoManagement',
              meta: {
                title: '样本信息管理'
              },
              component: () => import('@/components/business/sample/clinicalInfoManagement.vue')
            },
            {
              path: 'pathogenManagement',
              meta: {
                title: '病原样本管理'
              },
              component: () => import('@/components/business/pathogenSample/pathogenSampleManagement.vue')
            },
            {
              path: 'sampleReturnManagement',
              meta: {
                title: '返样管理'
              },
              component: () => import('@/components/business/sample/returnSampleManagement.vue')
            },
            {
              path: 'sampleSigningManagement',
              meta: {
                title: '样本签收管理'
              },
              component: () => import('@/components/business/sample/sampleSigningManagement.vue')
            },
            {
              path: 'technologyService',
              component: () => import('@/components/business/sample/technologyService/index.vue'),
              children: [
                {
                  path: 'orderReview',
                  meta: {
                    title: '科服订单审核'
                  },
                  component: () => import('@/components/business/sample/technologyService/orderReview/overview.vue')
                },
                {
                  path: 'sampleConfirm',
                  meta: {
                    title: '样本管理'
                  },
                  component: () => import('@/components/business/sample/technologyService/sampleConfirm/overview.vue')
                },
                {
                  path: 'sampleManagement',
                  meta: {
                    title: '样本处理'
                  },
                  component: () => import('@/components/business/sample/technologyService/sampleManagement/overview.vue')
                }
              ]
            },
            {
              path: 'orderReview',
              meta: {
                title: '订单审核'
              },
              component: () => import('@/components/business/orderManagement/orderReview/orderReview.vue')
            },
            {
              path: 'sampleAbnormal',
              meta: {
                title: '异常样本'
              },
              component: () => import('@/components/business/orderManagement/sampleAbnormal/abnormalSample.vue')
            },
            {
              path: 'orderKanban',
              meta: {
                title: '订单看板'
              },
              component: () => import('@/components/business/orderManagement/orderKanban/orderKanban.vue')
            },
            {
              path: 'expressDeliveryManagement',
              meta: {
                title: '快递签收管理'
              },
              component: () => import('@/components/business/sample/expressDeliveryManagement.vue')
            },
            {
              path: 'qcReport',
              meta: {
                title: '质控报告生成'
              },
              component: () => import('@/components/business/scientificResearchProject/qcReport/qcReport.vue')
            },
            {
              path: 'subOrderManagement',
              meta: {
                title: '子订单管理'
              },
              component: () => import('@/components/business/scientificResearchProject/subOrderManagement/subOrderManagement.vue')
            },
            {
              path: 'qcReportAuditManagement',
              meta: {
                title: '质控报告审核'
              },
              component: () => import('@/components/business/scientificResearchProject/qcReportAuditManagement/qcReportAuditManagement.vue')
            },
            {
              path: 'suborderDataDelivery',
              meta: {
                title: '子订单交付管理'
              },
              component: () => import('@/components/business/scientificResearchDataDelivery/suborderDataDelivery/overview.vue')
            },
            {
              path: 'sampleDelivery',
              meta: {
                title: '样本交付查询'
              },
              component: () => import('@/components/business/scientificResearchDataDelivery/sampleDelivery/overview.vue')
            },
            {
              path: 'orderDelivery',
              meta: {
                title: '交付订单管理'
              },
              component: () => import('@/components/business/deliveryManagement/index.vue')
            },
            {
              path: 'deliveryConfigManagement',
              meta: {
                title: '交付配置管理'
              },
              component: () => import('@/components/business/deliveryManagement/deliveryConfigManagement/index.vue')
            },
            {
              path: 'unTestSampleManagement',
              meta: {
                title: '待测样本管理'
              },
              component: () => import('@/components/business/sequencingManagement/unTestSampleManagement/overview.vue')
            },
            {
              path: 'scheduleTaskManagement',
              meta: {
                title: '排单任务管理'
              },
              component: () => import('@/components/business/sequencingManagement/scheduleTaskManagement/overview.vue')
            },
            {
              path: 'pooling',
              meta: {
                title: 'pooling'
              },
              component: () => import('@/components/business/sequencingManagement/pooling/overview.vue')
            },
            {
              path: 'translation',
              meta: {
                title: '转化'
              },
              component: () => import('@/components/business/sequencingManagement/translation/overview.vue')
            },
            {
              path: 'cyclization',
              meta: {
                title: '环化'
              },
              component: () => import('@/components/business/sequencingManagement/cyclization/overview.vue')
            },
            {
              path: 'makeDNB',
              meta: {
                title: 'makeDNB'
              },
              component: () => import('@/components/business/sequencingManagement/makeDNB/overview.vue')
            },
            {
              path: 'errorManagement',
              meta: {
                title: '上机-异常处理'
              },
              component: () => import('@/components/business/sequencingManagement/errorManagement/overview.vue')
            },
            {
              path: 'singleCell/dissociation',
              meta: {
                title: '解离'
              },
              component: () => import('@/components/business/singleCell/dissociation/index.vue')
            },
            {
              path: 'singleCell/buildLib',
              meta: {
                title: '建库'
              },
              component: () => import('@/components/business/singleCell/buildLib/index.vue')
            },
            {
              path: 'singleCell/smapleMonitoring',
              meta: {
                title: '单细胞样本监控'
              },
              component: () => import('@/components/business/singleCell/smapleMonitoringManagement/index.vue')
            },
            {
              path: 'singleCell/dataDelivery',
              meta: {
                title: '单细胞数据交付'
              },
              component: () => import('@/components/business/singleCell/deliveryManagement/index.vue')
            },
            // {
            //   path: 'offlineDetectManagement',
            //   meta: {
            //     title: '线下检测管理'
            //   },
            //   component: () => import('@/components/business/sample/offlineDetectManagement.vue')
            // },
            // {
            //   path: 'sampleInfoManagement',
            //   meta: {
            //     title: '样例信息管理'
            //   },
            //   component: () => import('@/components/business/sample/sampleInfoManagement.vue')
            // },
            // {
            //   path: 'sampleArrivalConfirm',
            //   meta: {
            //     title: '样本到样确认'
            //   },
            //   component: () => import('@/components/business/sample/sampleArrivalConfirm.vue')
            // }
            {
              path: 'materialInventoryManagement',
              meta: {
                title: '物料库存管理'
              },
              component: () => import('@/components/business/materials/materialInventoryManagement.vue')
            },
            {
              path: 'publicityInventoryManagement',
              meta: {
                title: '宣传品库存管理'
              },
              component: () => import('@/components/business/materials/publicityInventoryManagement.vue')
            },
            {
              path: 'materialApplicationManagement',
              meta: {
                title: '物料申请管理'
              },
              component: () => import('@/components/business/materials/materialApplicationManagement.vue')
            },
            {
              path: 'publicityApplicationManagement',
              meta: {
                title: '宣传品申请管理'
              },
              component: () => import('@/components/business/materials/publicityApplicationManagement.vue')
            },
            {
              path: 'deliveryManagement',
              meta: {
                title: '发货管理'
              },
              component: () => import('@/components/business/materials/deliveryManagement.vue')
            },
            {
              path: 'productManagement',
              meta: {
                title: '产品管理'
              },
              component: () => import('@/components/business/productManagement/productManagement.vue')
            },
            {
              path: 'grantReport',
              meta: {
                title: '发放报表'
              },
              component: () => import('@/components/business/materials/grantReport.vue')
            },
            {
              path: 'onlineProbeManagement',
              meta: {
                title: '探针在线管理'
              },
              component: () => import('@/components/business/basicDataManagement/onlineProbeManagement.vue')
            },
            {
              path: 'nucleicGradeConfigManagement',
              meta: {
                title: '核酸等级配置'
              },
              component: () => import('@/components/business/basicDataManagement/nucleicGradeConfigManagement/index.vue')
            },
            {
              path: 'abnormalDescConfigManagement',
              meta: {
                title: '异常描述配置'
              },
              component: () => import('@/components/business/basicDataManagement/abnormalDescConfigManagement/index.vue')
            },
            {
              path: 'sinkMarketProductManagement',
              meta: {
                title: '下沉市场产品管理'
              },
              component: () => import('@/components/business/productManagement/sinkMarketProductManagement.vue')
            },
            {
              path: 'pharmaceuticalProductManagement',
              meta: {
                title: '药厂产品管理'
              },
              component: () => import('@/components/business/productManagement/pharmaceuticalProductManagement.vue')
            },
            {
              path: 'sequenceProductManagement',
              meta: {
                title: '测序工厂产品管理'
              },
              component: () => import('@/components/business/productManagement/sequenceProductManagement.vue')
            },
            {
              path: 'cnvVerifyManagement',
              meta: {
                title: '胚系CNV验证'
              },
              component: () => import('@/components/business/reportRead/cnvVerifyManagement/index.vue')
            },
            {
              path: 'samplesNotSignedFor',
              meta: {
                title: '样本未签收'
              },
              component: () => import('@/components/business/sampleReceiptManagement/samplesNotSignedFor/overview.vue')
            },
            {
              path: 'sampleHasBeenSigned',
              meta: {
                title: '签收样本信息'
              },
              component: () => import('@/components/business/sampleReceiptManagement/sampleHasBeenSigned/overview.vue')
            },
            {
              path: 'repeatClinicManagement',
              meta: {
                title: '加测订单管理'
              },
              component: () => import('@/components/business/dataMonitoringManagement/repeatClinicManagement/overview.vue')
            },
            {
              path: 'sampleMonitoring',
              meta: {
                title: '样本监控'
              },
              component: () => import('@/components/business/dataMonitoringManagement/smapleMonitoringManagement/index.vue')
            }
          ]
        },
        {
          path: 'sub',
          component: () => import('@/components/layout/subpage.vue'),
          children: [
            // 线下检测详情
            {
              path: 'offineDetectionDetails',
              component: () => import('@/components/business/offineDetectionDetails/overview.vue')
            },
            {
              path: 'containerDetail',
              meta: {
                pageTitle: '容器详情'
              },
              component: () => import('@/components/business/containerManagement/containerDetail.vue')
            },
            {
              path: 'createContainer',
              meta: {
                pageTitle: '创建容器'
              },
              component: () => import('@/components/business/containerManagement/createContainer.vue')
            },
            {
              path: 'modifyContainer',
              meta: {
                pageTitle: '修改容器'
              },
              component: () => import('@/components/business/containerManagement/createContainer.vue')
            },
            {
              path: 'enterLibraryDetail',
              meta: {
                pageTitle: '领取入库'
              },
              component: () => import('@/components/business/sampleLibraryManagement/enterLibraryDetail.vue')
            },
            {
              path: 'clinicalInfoManagementInfoDetail',
              component: () => import('@/components/business/sample/clinicalInfoManagementInfoDetail.vue')
            },
            {
              path: 'pathogenInfoManagementInfoDetail',
              component: () => import('@/components/business/pathogenSample/pathogenSampleInfo.vue')
            },
            {
              path: 'templateConfigPage',
              component: () => import('@/components/business/templateManagement/templateConfigPage.vue')
            },
            {
              path: 'orderLibraryDetail',
              component: () => import('@/components/business/orderManagement/orderReview/entryBaseInfo.vue')
            },
            {
              path: 'orderTissueDetail',
              component: () => import('@/components/business/orderManagement/orderReview/entryBaseInfo.vue')
            },
            {
              path: 'moveStorageApplication',
              meta: {
                pageTitle: '移库'
              },
              component: () => import('@/components/business/sampleLibraryManagement/moveStorageApplication.vue')
            }
          ]
        }
      ]
    },
    {
      path: '/business/unscramble',
      component: () => import('@/components/layout/subpage.vue'),
      children: [
        {
          path: 'ai',
          component: () => import('@/components/business/ai/index.vue')
        },
        {
          path: 'mutationAnalysis',
          component: () => import('@/components/business/ai/common/addForecastData.vue')
        },
        {
          path: 'wesReport',
          component: () => import('@/components/business/unscrambleReport/unscrambleReport/wesReport.vue')
        },
        {
          path: 'addForecastData',
          component: () => import('@/components/business/unscrambleReport/unscrambleReport/common/addForecastData.vue')
        },
        {
          path: 'addDXData',
          component: () => import('@/components/business/unscrambleReport/unscrambleReport/common/addDXData.vue')
        },
        {
          path: 'addGeneticMutation',
          component: () => import('@/components/business/unscrambleReport/unscrambleReport/common/addGeneticMutation.vue')
        },
        {
          path: 'pathogenicReport',
          component: () => import('@/components/business/unscrambleReport/pathogenicReport/index.vue')
        }
      ]
    },
    {
      path: '/business/subpage',
      component: () => import('@/components/layout/subpage.vue'),
      children: [
        {
          path: 'dataBoard',
          component: () => import('@/components/business/statistical/mrdMonitorManagement/dataBoard.vue')
        },
        {
          path: 'deliveryManagementSaveInfo',
          component: () => import('@/components/business/materials/deliveryManagementSaveInfo.vue')
        },
        {
          path: 'probeKanban',
          component: () => import('@/components/business/basicDataManagement/probeKanban.vue')
        },
        {
          path: 'probeOrderDetail',
          component: () => import('@/components/business/basicDataManagement/probeOrderDetail.vue')
        },
        {
          path: 'deliveryManagementDetail',
          component: () => import('@/components/business/materials/deliveryManagementDetail.vue')
        },
        {
          path: 'applicationDetail',
          component: () => import('@/components/business/materials/applicationDetail.vue')
        },
        {
          path: 'previewExcel',
          component: () => import('@/components/business/basicDataManagement/previewExcel.vue')
        },
        /**
         * 科服illumina订单详情
         * */
        {
          path: 'technologyService/entryIlluminaLibraryOrder',
          meta: {
            title: 'illumina文库订单详情'
          },
          component: () => import('@/components/business/sample/technologyService/orderReview/orderDetail/illumina/index.vue')
        },
        /**
         * 科服MGI订单详情
         * */
        {
          path: 'technologyService/entryMGILibraryOrder',
          meta: {
            title: 'MGI文库订单详情'
          },
          component: () => import('@/components/business/sample/technologyService/orderReview/orderDetail/mgi/index.vue')
        },
        /**
         * 科服组织或核酸订单详情
         * */
        {
          path: 'technologyService/entryTissueOrder',
          meta: {
            title: '组织或核酸订单详情'
          },
          component: () => import('@/components/business/sample/technologyService/orderReview/orderDetail/tissueOrNucleicAcid/index.vue')
        },
        {
          path: 'technologyService/singleCell',
          meta: {
            title: '单细胞订单详情'
          },
          component: () => import('@/components/business/sample/technologyService/orderReview/orderDetail/singleCell/index.vue')
        },
        /** ***************************************************测序一期******************************************/
        {
          path: 'sequencingManagement/sampleProcessDetail',
          meta: {
            title: '样本进度详情'
          },
          component: () => import('@/components/business/sequencingManagement/scheduleTaskManagement/sampleProcessDetail/overview.vue')
        },
        {
          path: 'sampleLogs',
          meta: {
            title: '样例日志'
          },
          component: () => import('@/components/business/sample/sampleLogs/index.vue')
        }
      ]
    }
  ]
})

routes.beforeEach((to, from, next) => {
  let title = ''
  let meta = to.meta || {}
  if (meta.title) {
    title = ' - ' + meta.title
  }
  document.title = 'lims' + title
  if (to.query.jmoz) {
    let id = to.query.jmoz.replace(/\$/g, '=')
    util.setSessionInfo('jmoz', id)
  }
  if (to.meta.pageTitle) document.title = to.meta.pageTitle
  if (Vue.prototype.$myresource === undefined) {
    Vue.prototype.$myresource = util.getSessionInfo('resource') || {
      modules: [],
      menus: [],
      buttons: []
    }
  }
  axios({
    url: '/static/config.json'
  }).then(function (result) {
    if (window.localStorage) {
      window.localStorage.setItem('IP', result.data.IP)
      window.localStorage.setItem('PORT', result.data.PORT)
      window.localStorage.setItem('PROTOCOL', result.data.PROTOCOL)
      window.localStorage.setItem('PROTOCOL_WEBSOCKET', result.data.PROTOCOL_WEBSOCKET)
      window.localStorage.setItem('PROJECT', result.data.PROJECT)
      constants.ITSM = result.data.ITSM
      constants.IFRAME_URL = result.data.iframeUrl
      constants.IS_TEST = result.data.IS_TEST
      constants.JS_CONTEXT = window.localStorage.getItem('PROTOCOL') + '://' +
        window.localStorage.getItem('IP') + ':' +
        window.localStorage.getItem('PORT') + '/' +
        window.localStorage.getItem('PROJECT')
      constants.OLD_LIMS_URL = result.data.OLD_LIMS_URL
      next()
    } else {
      alert('当前浏览器不支持localStorage，请升级版本')
    }
  }).catch(function (err) {
    alert(err)
  })
})

export default routes
