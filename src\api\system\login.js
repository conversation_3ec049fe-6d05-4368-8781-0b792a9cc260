import { myAjax } from '@/util/ajax'
// import Cookies from 'js-cookie'
/**
 * 获取登录图形验证码
 */
export function getValidateCodeImage (uuid) {
  return myAjax({
    url: '/user/get_login_code',
    data: { uuid },
    method: 'get',
    responseType: 'blob'
  })
}

/**
 * 登录
 */
export function userLogin (data) {
  return myAjax({
    url: '/user/login',
    data
  })
}

// /**
//  * 登出
//  */
// export function userLogout () {
//   return myAjax({
//     url: '/system/user/logout',
//     data: {
//       ftoken: Cookies.get('token')
//     },
//     loadingDom: 'body',
//     loadingObj: {
//       lock: true,
//       text: '正在退出',
//       spinner: 'el-icon-loading',
//       background: 'rgba(0, 0, 0, 0.7)'
//     }
//   })
// }

// /**
//  * 获取忘记密码验证码
//  */
// export function getPhoneCode (phone, type = 0) {
//   return myAjax({
//     url: '/medical/system/get_verification_code',
//     data: {
//       ftype: type,
//       fphone: phone
//     }
//   })
// }

// /**
//  * 修改密码
//  */
// export function modifyPassword (data) {
//   return myAjax({
//     url: '/system/user/change_password',
//     data
//   })
// }

// /**
//  * 修改密码
//  */
// export function forgetPassword (data) {
//   return myAjax({
//     url: '/system/user/change_password_by_fphone',
//     data
//   })
// }
