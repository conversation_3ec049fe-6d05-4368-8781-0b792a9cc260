/**
 * 统一CDN配置文件
 * 支持多环境、多CDN提供商的配置管理
 */

// 加载环境变量
try {
  require('dotenv').config()
} catch (error) {
  // dotenv不是必需的，忽略错误
}

// 基础配置
const BASE_CONFIG = {
  // 项目信息
  projectName: 'lims-frontend',
  version: 'V3.17.22.250730',

  // 支持的文件类型
  supportedExtensions: ['.js', '.css', '.png', '.jpg', '.jpeg', '.gif', '.svg', '.woff', '.woff2', '.ttf', '.eot', '.json'],

  // 内容类型映射
  contentTypes: {
    '.html': 'text/html',
    '.js': 'application/javascript',
    '.css': 'text/css',
    '.json': 'application/json',
    '.png': 'image/png',
    '.jpg': 'image/jpeg',
    '.jpeg': 'image/jpeg',
    '.gif': 'image/gif',
    '.svg': 'image/svg+xml',
    '.woff': 'font/woff',
    '.woff2': 'font/woff2',
    '.ttf': 'font/ttf',
    '.eot': 'application/vnd.ms-fontobject'
  },

  // CORS配置
  corsHeaders: {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, HEAD, OPTIONS',
    'Access-Control-Allow-Headers': 'Origin, X-Requested-With, Content-Type, Accept'
  }
}

// 环境配置
const ENVIRONMENTS = {
  // 开发环境
  development: {
    environment: 'development',
    basePath: `${BASE_CONFIG.projectName}/dev/${BASE_CONFIG.version}`,
    cacheControl: 'public, max-age=60', // 1分钟缓存
    forceOverwrite: true,
    enableGzip: false // 暂时禁用手动 Gzip，让 CDN 处理压缩
  },

  // 测试环境
  test: {
    environment: 'test',
    basePath: `${BASE_CONFIG.projectName}/test/${BASE_CONFIG.version}`,
    cacheControl: 'public, max-age=300', // 5分钟缓存
    forceOverwrite: true,
    enableGzip: false // 暂时禁用手动 Gzip，让 CDN 处理压缩
  },

  // 生产环境
  production: {
    environment: 'production',
    basePath: `${BASE_CONFIG.projectName}/prod/${BASE_CONFIG.version}`,
    cacheControl: 'public, max-age=31536000', // 1年缓存
    forceOverwrite: false,
    enableGzip: false // 暂时禁用手动 Gzip，让 CDN 处理压缩
  }
}

// CDN提供商配置
const CDN_PROVIDERS = {
  // 阿里云OSS
  aliOSS: {
    name: 'aliOSS',
    region: process.env.OSS_REGION || 'oss-cn-beijing',
    accessKeyId: process.env.OSS_ACCESS_KEY_ID,
    accessKeySecret: process.env.OSS_ACCESS_KEY_SECRET,
    bucket: process.env.OSS_BUCKET || 'genereadonly',
    domain: process.env.OSS_DOMAIN || 'cdn.geneplus.org.cn',
    endpoint: process.env.OSS_ENDPOINT,

    // 验证配置
    validate () {
      if (!this.accessKeyId || !this.accessKeySecret) {
        throw new Error('缺少阿里云OSS访问密钥配置，请设置环境变量 OSS_ACCESS_KEY_ID 和 OSS_ACCESS_KEY_SECRET')
      }
      return true
    }
  },

  // 腾讯云COS
  tencentCOS: {
    name: 'tencentCOS',
    SecretId: process.env.COS_SECRET_ID,
    SecretKey: process.env.COS_SECRET_KEY,
    Bucket: process.env.COS_BUCKET,
    Region: process.env.COS_REGION || 'ap-beijing',
    domain: process.env.COS_DOMAIN,

    // 验证配置
    validate () {
      if (!this.SecretId || !this.SecretKey || !this.Bucket) {
        throw new Error('缺少腾讯云COS配置，请设置环境变量 COS_SECRET_ID, COS_SECRET_KEY, COS_BUCKET')
      }
      return true
    }
  },

  // AWS S3
  awsS3: {
    name: 'awsS3',
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    region: process.env.AWS_REGION || 'us-east-1',
    bucket: process.env.AWS_BUCKET,
    domain: process.env.AWS_DOMAIN,

    // 验证配置
    validate () {
      if (!this.accessKeyId || !this.secretAccessKey || !this.bucket) {
        throw new Error('缺少AWS S3配置，请设置环境变量 AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY, AWS_BUCKET')
      }
      return true
    }
  },

  // 七牛云
  qiniu: {
    name: 'qiniu',
    accessKey: process.env.QINIU_ACCESS_KEY,
    secretKey: process.env.QINIU_SECRET_KEY,
    bucket: process.env.QINIU_BUCKET,
    domain: process.env.QINIU_DOMAIN,
    zone: process.env.QINIU_ZONE || 'Zone_z0',

    // 验证配置
    validate () {
      if (!this.accessKey || !this.secretKey || !this.bucket) {
        throw new Error('缺少七牛云配置，请设置环境变量 QINIU_ACCESS_KEY, QINIU_SECRET_KEY, QINIU_BUCKET')
      }
      return true
    }
  }
}

// 获取配置函数
function getConfig (environment = 'test', provider = 'aliOSS') {
  // 验证环境
  if (!ENVIRONMENTS[environment]) {
    throw new Error(`不支持的环境: ${environment}。支持的环境: ${Object.keys(ENVIRONMENTS).join(', ')}`)
  }

  // 验证CDN提供商
  if (!CDN_PROVIDERS[provider]) {
    throw new Error(`不支持的CDN提供商: ${provider}。支持的提供商: ${Object.keys(CDN_PROVIDERS).join(', ')}`)
  }

  const envConfig = ENVIRONMENTS[environment]
  const providerConfig = CDN_PROVIDERS[provider]

  // 验证提供商配置
  try {
    providerConfig.validate()
  } catch (error) {
    console.warn(`⚠️  CDN配置验证失败: ${error.message}`)
    // 不抛出错误，允许使用模拟模式
  }

  return {
    ...BASE_CONFIG,
    ...envConfig,
    provider: providerConfig,

    // 生成完整的CDN URL
    getCDNUrl () {
      const protocol = 'https://'
      const domain = providerConfig.domain
      const basePath = envConfig.basePath
      return `${protocol}${domain}/${basePath}/`
    },

    // 生成远程文件路径
    getRemotePath (localPath) {
      return `${envConfig.basePath}/${localPath}`
    },

    // 获取内容类型
    getContentType (filePath) {
      const ext = require('path').extname(filePath).toLowerCase()
      return BASE_CONFIG.contentTypes[ext] || 'application/octet-stream'
    },

    // 生成上传头信息
    getUploadHeaders (filePath) {
      return {
        'Content-Type': this.getContentType(filePath),
        'Cache-Control': envConfig.cacheControl,
        ...BASE_CONFIG.corsHeaders
      }
    }
  }
}

// 获取所有支持的环境
function getSupportedEnvironments () {
  return Object.keys(ENVIRONMENTS)
}

// 获取所有支持的CDN提供商
function getSupportedProviders () {
  return Object.keys(CDN_PROVIDERS)
}

// 显示配置信息
function displayConfig (config) {
  // 尝试加载chalk，如果没有则使用简单颜色
  let chalk
  try {
    chalk = require('chalk')
  } catch (error) {
    chalk = {
      cyan: (text) => `\x1b[36m${text}\x1b[0m`,
      gray: (text) => `\x1b[90m${text}\x1b[0m`
    }
  }

  console.log(chalk.cyan('📋 CDN部署配置信息:'))
  console.log(chalk.gray(`  项目名称: ${config.projectName}`))
  console.log(chalk.gray(`  环境: ${config.environment}`))
  console.log(chalk.gray(`  版本: ${config.version}`))
  console.log(chalk.gray(`  CDN提供商: ${config.provider.name}`))
  console.log(chalk.gray(`  基础路径: ${config.basePath}`))
  console.log(chalk.gray(`  缓存控制: ${config.cacheControl}`))
  console.log(chalk.gray(`  强制覆盖: ${config.forceOverwrite ? '是' : '否'}`))
  console.log(chalk.gray(`  启用Gzip: ${config.enableGzip ? '是' : '否'}`))
  console.log(chalk.gray(`  CDN域名: ${config.provider.domain}`))
  console.log(chalk.gray(`  完整URL: ${config.getCDNUrl()}`))
  console.log('')
}

// 导出配置
module.exports = {
  BASE_CONFIG,
  ENVIRONMENTS,
  CDN_PROVIDERS,
  getConfig,
  getSupportedEnvironments,
  getSupportedProviders,
  displayConfig
}
