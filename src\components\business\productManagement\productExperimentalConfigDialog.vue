<template>
  <div>
    <el-dialog
      :title="title"
      :visible.sync="visible"
      :close-on-click-modal="false"
      :before-close="handleClose"
      width="800px"
      @open="handleOpen">
        <!--      单产品-->
        <div v-if="isSample">
          <el-form
            ref="form"
            :model="form"
            :rules="rules"
            size="small"
            label-position="top"
            label-width="120px">
            <el-tabs v-model.trim="activeName">
              <!--       解读规则 多产品      -->
              <el-tab-pane label="基本信息" name="基本信息">
                <div class="wrapper">
                  <!-- 1.基本信息表单-->
                  <div class="basic-form">
                    <div class="basic-form-title">基本信息</div>
                    <el-row :gutter="20">
                      <el-col :span="12">
                        <el-form-item label="测序平台：" prop="seqPlat">
                          <el-select v-model.trim="form.seqPlat" filterable clearable placeholder="请选择">
                            <el-option
                              :key="index"
                              :label="item"
                              :value="item"
                              v-for="(item, index) in pequencingPlatform">
                            </el-option>
                          </el-select>
                        </el-form-item>
                      </el-col>
                      <el-col :span="12" >
                        <el-form-item label="产品TAT：" prop="tatType">
                          <el-select v-model.trim="form.tatType" clearable placeholder="请选择">
                            <el-option :value="2" label="按到样时间算TAT"></el-option>
                            <el-option :value="1" label="按送检时间算TAT"></el-option>
                            <el-option :value="3" label="探针到货计算TAT规则"></el-option>
                            <el-option :value="4" label="记录创建时间算TAT"></el-option>
                            <el-option :value="5" label="按样本类型+到样时间算TAT"></el-option>
                          </el-select>
                        </el-form-item>
                      </el-col>
                    </el-row>
                    <el-row :gutter="20">
                      <el-col :span="12">
                        <el-form-item label="配送TAT：" prop="tatTime">
                          <el-input v-model.trim="form.tatTime" style="width: 203px" clearable placeholder="必填"></el-input>
                        </el-form-item>
                      </el-col>
                      <el-col :span="12" >
                        <el-form-item label="交付周期：" prop="deliveryPeriod">
                          <el-input v-model.trim="form.deliveryPeriod" style="width: 203px" clearable placeholder="必填"></el-input>
                        </el-form-item>
                      </el-col>
                    </el-row>
                    <el-row :gutter="20">
<!--                      <el-col :span="12">-->
<!--                        <el-form-item label="是否自动生成D数据：" prop="fisAutoCreateDData">-->
<!--                          <el-select v-model.trim="form.fisAutoCreateDData" clearable placeholder="请选择">-->
<!--                            <el-option :value="1" label="是"></el-option>-->
<!--                            <el-option :value="0" label="否"></el-option>-->
<!--                          </el-select>-->
<!--                        </el-form-item>-->
<!--                      </el-col>-->
                      <el-col :span="12">
                        <el-form-item label="检测样本类型：" prop="dectType">
                          <el-select v-model.trim="form.dectType" clearable placeholder="请选择">
                            <el-option value="gDNA" label="gDNA"></el-option>
                            <el-option value="cfDNA" label="cfDNA"></el-option>
                          </el-select>
                        </el-form-item>
                      </el-col>
                    </el-row>
                    <el-row :gutter="20">
                      <el-col :span="12">
                        <el-form-item label="工序流程：" prop="fprocedureTemplateName">
                          <el-select v-model.trim="form.fprocedureTemplateId" clearable placeholder="请选择">
                            <el-option
                              :key="index"
                              :label="item.procedureTemplateName"
                              :value="item.fid"
                              v-for="(item, index) in procedureTemplate">
                            </el-option>
                          </el-select>
                        </el-form-item>
                      </el-col>
                      <el-col :span="12">
                        <el-form-item label="提取量：" prop="fextractVolumn">
                          <el-select v-model.trim="form.fextractVolumn" clearable placeholder="请选择">
                            <el-option :value="8" label="8ml"></el-option>
                            <el-option :value="4" label="4ml"></el-option>
                          </el-select>
                        </el-form-item>
                      </el-col>
                    </el-row>
                    <el-row :gutter="20">
                      <el-col :span="12">
                          <el-form-item label="是否允许单样本：" prop="fisAllowSingle">
                            <div style="display: flex; justify-content: space-between">

                            <el-select v-model.trim="form.fisAllowSingle" clearable placeholder="请选择">
                              <el-option :value="0" label="是"></el-option>
                              <el-option :value="1" label="否"></el-option>
                              <el-option :value="2" label="仅单样本"></el-option>
                            </el-select>
                            <!--                      空值、对照暂停、对照不暂停-->
                            <el-select v-model.trim="form.fis_pause" style="margin-left: 10px" size="small" clearable placeholder="请选择">
                              <el-option :value="-1" label="空值"></el-option>
                              <el-option :value="1" label="对照暂停"></el-option>
                              <el-option :value="0" label="对照不暂停"></el-option>
                            </el-select>
                            </div>
                          </el-form-item>
                      </el-col>
                      <el-col :span="12">
                        <el-form-item label="是否线下检测：" prop="fisOfflineDetect">
                          <el-select v-model.trim="form.fisOfflineDetect" clearable placeholder="请选择">
                            <el-option :value="1" label="仅线下"></el-option>
                            <el-option :value="2" label="线上+线下"></el-option>
                          </el-select>
                        </el-form-item>
                      </el-col>
                    </el-row>
                    <el-row :gutter="20">
                      <el-col :span="12">
                        <el-form-item label="线下检测类型：" prop="fofflineDetectType">
                          <el-select v-model.trim="form.fofflineDetectType" clearable placeholder="请选择">
                            <el-option
                              :key="item.id"
                              :label="item.name"
                              :value="item.id"
                              v-for="item in testType"
                            >
                            </el-option>
                          </el-select>
                        </el-form-item>
                      </el-col>
                      <el-col :span="12">
                        <el-form-item label="组织镜检：" prop="forganizationMicroscopy ">
                          <el-select v-model.trim="form.forganizationMicroscopy " clearable placeholder="请选择">
                            <el-option
                              :key="item.value"
                              :label="item.label"
                              :value="item.value"
                              v-for="item in organizationMicroscopyOptions"
                            >
                            </el-option>
                          </el-select>
                        </el-form-item>
                      </el-col>
                      <el-col :span="12">
                        <el-form-item label="芯片号：">
                          <el-input v-model.trim="form.ficNum" style="width: 203px" clearable placeholder="请输入"></el-input>
                        </el-form-item>
                      </el-col>
                      <el-col :span="12">
                        <el-form-item label="TAT速缓标签：" prop="ftatSpeedTip">
                          <el-select v-model.trim="form.ftatSpeedTip" clearable placeholder="请选择">
                            <el-option :value="1" label="加速"></el-option>
                            <el-option :value="0" label="放缓"></el-option>
                          </el-select>
                        </el-form-item>
                      </el-col>
                      <el-col :span="12">
                        <el-form-item label="海外产品：" prop="isOverseasProducts">
                          <el-select v-model.trim="form.isOverseasProducts" clearable placeholder="请选择">
                            <el-option :value="1" label="是"></el-option>
                            <el-option :value="0" label="否"></el-option>
                          </el-select>
                        </el-form-item>
                      </el-col>
                      <el-col :span="12">
                        <el-form-item label="杂交通用配置：">
                          <el-select v-model.trim="form.fcrossCommonConfig" clearable placeholder="请选择">
                            <el-option
                              :key="item.id"
                              :label="item.name"
                              :value="item.id"
                              v-for="item in crossCommonConfigs"
                            >
                            </el-option>
                          </el-select>
                        </el-form-item>
                      </el-col>
                    </el-row>
                    <el-row :gutter="20">
                      <el-col :span="12">
                        <el-form-item label="样本临床补录：">
                          <el-select v-model.trim="form.fneedMakeup" clearable placeholder="请选择">
                            <el-option
                              :key="item.value"
                              :label="item.label"
                              :value="item.value"
                              v-for="item in sampleClinical"
                            >
                            </el-option>
                          </el-select>
                        </el-form-item>
                      </el-col>
                      <el-col :span="12">
                        <el-form-item label="DNA/RNA检测：" prop="fdnaOrRna">
                          <el-select v-model.trim="form.fdnaOrRna" clearable placeholder="请选择">
                            <el-option value="DNA" label="DNA"></el-option>
                            <el-option value="RNA" label="RNA"></el-option>
                            <el-option value="DNA+RNA" label="DNA+RNA"></el-option>
                          </el-select>
                        </el-form-item>
                      </el-col>
<!--                      case预期数据量、case浮动范围、control预期数据量、control浮动范围-->
                      <el-col :span="12">
                        <el-form-item label="case预期数据量：" prop="fcaseExpectDataSize">
                          <el-input v-model.trim="form.fcaseExpectDataSize" style="width: 203px" clearable placeholder="请输入"></el-input>
                        </el-form-item>
                      </el-col>
                      <el-col :span="12">
                        <el-form-item label="case浮动范围：" prop="fcaseDataSizeFloat">
                          <el-input v-model.trim="form.fcaseDataSizeFloat" style="width: 203px" clearable placeholder="请输入"></el-input>
                        </el-form-item>
                      </el-col>
                      <el-col :span="12">
                        <el-form-item label="control预期数据量：" prop="fcontrolExpectDataSize">
                          <el-input v-model.trim="form.fcontrolExpectDataSize" style="width: 203px" clearable placeholder="请输入"></el-input>
                        </el-form-item>
                      </el-col>
                      <el-col :span="12">
                        <el-form-item label="control浮动范围：" prop="fcontrolDataSizeFloat">
                          <el-input v-model.trim="form.fcontrolDataSizeFloat" style="width: 203px" clearable placeholder="请输入"></el-input>
                        </el-form-item>
                      </el-col>
                    </el-row>
                  </div>
                  <!--: 2.样本组合信息：（这里的作用主要是用于后续一样多检时根据样本类型分配到哪个产品上的问题）-->
                  <div>
                    <el-form-item label="样本组合：" style="display: flex">
                      <el-radio-group v-model.trim="form.fisSampleCompare" @change="handleRadioChange">
                        <el-radio  :label="1" >有对照</el-radio>
                        <el-radio  :label="0" >无对照</el-radio>
                      </el-radio-group>
                    </el-form-item>
                    <el-select v-model.trim="form.fcomboTypeNames" multiple filterable  placeholder="请选择" size="small" clearable style="width: 100%; margin-bottom: 10px" >
                        <el-option v-for="(item, index) in comboTypeList" :key="index" :label="item.fcomboTypeName" :value="item.fcomboTypeName"></el-option>
                    </el-select>
                  </div>
                </div>
            </el-tab-pane>
            <el-tab-pane label="实验参数配置" name="实验参数配置">
              <div class="wrapper">
              <!--: 3.实验参数配置：根据不同的样本类型标签配置对应文库方案和探针-->
              <div v-if="errorMessage !== ''" style="color: red">{{errorMessage}}</div>
              <div class="title">
                实验参数配置
              </div>
              <el-table :data="tableData"
                        v-if="isSample"
                        border
                        style="width: 100%">
                <el-table-column prop="fsampleSign" label="样本标签">
                  <template slot-scope="scope">
                    <div v-if="!scope.row.isFix">
                      {{scope.row.fsampleSign}}
                    </div>
                    <div v-if="scope.row.isFix">
                      <el-select v-model.trim="scope.row.fsampleSign" filterable size="small" clearable placeholder="请选择" style="width: 100%">
                        <el-option
                          :key="index"
                          :label="item"
                          :value="item"
                          v-for="(item, index) in sampleSignList">
                        </el-option>
                      </el-select>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="flibType" label="文库类型">
                  <template slot-scope="scope">
                    <div v-if="!scope.row.isFix">
                      {{scope.row.flibType}}
                    </div>
                    <div v-if="scope.row.isFix">
                      <el-select v-model.trim="scope.row.flibType" filterable size="small" clearable placeholder="请选择" style="width: 100%">
                        <el-option
                          :key="index"
                          :label="item"
                          :value="item"
                          v-for="(item, index) in libTypeList">
                        </el-option>
                      </el-select>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="fprobe" label="探针">
                  <template slot-scope="scope">
                    <div v-if="!scope.row.isFix">
                      {{scope.row.fprobe}}
                    </div>
                    <div v-if="scope.row.isFix">
                      <el-select v-model.trim="scope.row.fprobe" filterable size="small" clearable placeholder="请选择" style="width: 100%">
                        <el-option
                          :key="index"
                          :label="item"
                          :value="item"
                          v-for="(item, index) in probeList">
                        </el-option>
                      </el-select>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="fdataSize" label="数据量">
                  <template slot-scope="scope">
                    <div v-if="!scope.row.isFix">
                      {{scope.row.fdataSize}}
                    </div>
                    <div v-if="scope.row.isFix">
                      <el-input v-model.trim="scope.row.fdataSize" size="small"></el-input>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="fcrossBase" label="杂交基数">
                  <template slot-scope="scope">
                    <div v-if="!scope.row.isFix">
                      {{scope.row.fcrossBase}}
                    </div>
                    <div v-if="scope.row.isFix">
                      <el-input v-model.trim="scope.row.fcrossBase" size="small"></el-input>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column  label="操作" min-width="60">
                  <template slot-scope="scope">
                    <div class="icon-wrapper">
                      <div style="cursor: pointer" @click="openFix(scope.row, scope.$index)">{{scope.row.isFix ? '保存' : '修改'}}</div> <span style="color: #EBEEF5">|</span>
                      <div style="cursor: pointer" slot="reference" @click="handleDelete(scope.$index)">删除</div>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
              <el-button style="margin-top: 5px; width: 100%" @click="addProductExperiment">+ 新增</el-button>
              </div>
            </el-tab-pane>
            <el-tab-pane label="实验标准" name="实验标准">
              <div class="wrapper">
                <el-form
                ref="form"
                :model="experConfig"
                size="small"
                label-position="top"
                label-width="120px">
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="所属事业部：" prop="affiliatedBusinessDepartment">
                      <el-input v-model.trim="experConfig.affiliatedBusinessDepartment" disabled style="width: 203px" clearable placeholder="请输入"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" >
                    <el-form-item label="工艺系列分类：" prop="craftSeriesClassification">
                      <el-cascader
                        v-model="experConfig.craftSeriesClassification"
                        :options="options"
                        clearable
                        ></el-cascader>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="血浆分离方式：" prop="plasmaSeparationMethod">
                        <el-select v-model.trim="experConfig.plasmaSeparationMethod" multiple clearable placeholder="请选择">
                          <el-option
                            :key="index"
                            :label="item"
                            :value="item"
                            v-for="(item, index) in plasmaSeparationMethods"
                            multiple>
                          </el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12" >
                      <el-form-item label="提取方式：" prop="extractionMethod">
                        <el-select v-model.trim="experConfig.extractionMethod" clearable placeholder="请选择">
                          <el-option
                            :key="index"
                            :label="item"
                            :value="item"
                            v-for="(item, index) in extractionMethods">
                          </el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="提取用量标准：" prop="extractionAmount">
                        <el-select v-model.trim="experConfig.extractionAmount" clearable placeholder="请选择">
                          <el-option
                            :key="index"
                            :label="item"
                            :value="item"
                            v-for="(item, index) in extractionAmounts">
                          </el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="建库方式：" prop="buildAlibraryWay">
                        <el-select v-model.trim="experConfig.buildAlibraryWay" clearable placeholder="请选择">
                          <el-option
                            :key="index"
                            :label="item"
                            :value="item"
                            v-for="(item, index) in buildAlibraryWays">
                          </el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="探针：" prop="probe">
                        <el-select v-model.trim="experConfig.probe" clearable placeholder="请选择">
                          <el-option
                            :key="index"
                            :label="item"
                            :value="item"
                            v-for="(item, index) in probes">
                          </el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="测序平台标准：" prop="sequencingPlatform">
                        <el-select v-model.trim="experConfig.sequencingPlatform" clearable placeholder="请选择">
                          <el-option
                            :key="index"
                            :label="item"
                            :value="item"
                            v-for="(item, index) in sequencingPlatform">
                          </el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="产出数据量下限：" prop="theLowerLimitOfTheOutputDataVolume">
                        <el-input v-model.trim="experConfig.theLowerLimitOfTheOutputDataVolume" style="width: 203px" clearable placeholder="请输入" maxlength="50"></el-input>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="特殊要求：" prop="experimentSpecialRequirements">
                        <el-select v-model.trim="experConfig.experimentSpecialRequirements" clearable placeholder="请选择">
                          <el-option
                            :key="index"
                            :label="item"
                            :value="item"
                            v-for="(item, index) in experimentSpecialRequirements">
                          </el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="镜检标准：" prop="remark">
                        <el-input v-model.trim="experConfig.microscopyStandard" style="width: 203px" clearable placeholder="请输入" maxlength="50"></el-input>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="核酸等级定级标准：" prop="fdnaLevelStandard">
                        <el-select v-model.trim="experConfig.fdnaLevelStandard" clearable placeholder="请选择">
                          <el-option
                            :key="index"
                            :label="item"
                            :value="item"
                            v-for="(item, index) in nucleicGradeConfigList">
                          </el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                  </el-row>
                   <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="异常描述标准：" prop="fexceptionDescriptionStandard">
                        <el-select v-model.trim="experConfig.fexceptionDescriptionStandard" clearable placeholder="请选择">
                          <el-option
                            :key="index"
                            :label="item"
                            :value="item"
                            v-for="(item, index) in abnormalDescConfigList">
                          </el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form>
              </div>
              </el-tab-pane>
            </el-tabs>
          </el-form>
        </div>

        <!--      组合产品：-->
        <!-- :1.不用编辑，仅为展示子产品对应的信息-->
        <div v-if="!isSample">
          <div class="tips"><i class="el-icon-warning" style="color: #608dfa"></i>
            该产品为组合产品，请选择相关产品进行配置，实验配置均由子产品来决定，子产品信息如下:
          </div>
          <el-table
            :data="productList"
            style="width: 100%">
            <el-table-column prop="productCode"  label="产品编号"></el-table-column>
            <el-table-column prop="productName" label="产品名称" show-overflow-tooltip></el-table-column>
            <el-table-column prop="seqPlat" label="测序平台"></el-table-column>
            <el-table-column prop="deliverCycle" label="交付周期"></el-table-column>
          </el-table>
        </div>

      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleClose">取 消</el-button>
        <el-button :loading="submitBtnLoading" size="mini" type="primary" @click="handleDialogConfirm">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import mixins from '../../../util/mixins'

export default {
  name: 'productExperimentalConfigDialog',
  mixins: [mixins.dialogBaseInfo],
  props: {
    title: String, // 弹窗标题
    productId: Number, // 产品id
    isSample: Boolean
  },
  data () {
    const checkDetectType = (rule, value, callback) => {
      if (this.form.fisOfflineDetect === '1' && value === '') {
        this.rules.fofflineDetectType[0].required = true
        callback(new Error('请选择线下检测类型'))
      } else {
        callback()
      }
    }
    return {
      activeName: '基本信息',
      disabled: false,
      errorMessage: '',
      experConfig: {
        experimentSpecialRequirements: '', // 特殊要求
        theLowerLimitOfTheOutputDataVolume: '', // 产出数据量下限
        microscopyStandard: '',
        fexceptionDescriptionStandard: '',
        fdnaLevelStandard: '',
        sequencingPlatform: '', // 测序平台标准
        probe: '', // 探针
        buildAlibraryWay: '', // 建库方式
        extractionAmount: '', // 提取用量标准
        extractionMethod: '', // 提取方式
        plasmaSeparationMethod: '', // 血浆分离方式
        craftSeriesClassification1: '', // 工艺系列分类一级选项
        craftSeriesClassification2: '', // 工艺系列分类二级选项
        affiliatedBusinessDepartment: '', // 附属事业部
        craftSeriesClassification: []
      },
      plasmaSeparationMethods: [
        '常规', 'PBMC', '红细胞裂解'
      ],
      extractionMethods: [
        '常规DNA', '非常规DNA(promega)', 'RNA单提取', '共提取', '非常规柱提'
      ],
      extractionAmounts: [
        '4ml', '8ml', '特定切片数'
      ],
      buildAlibraryWays: [
        '常规', 'IR-TCR', 'IR-BCR', 'AI', 'RNA', '非常规250ng(信达)', '非常规200ng（伴随诊断）', '非常规100ng'
      ],
      probes: [
        'ed2', 'ed3b', 'cd3', 'cd3v', 'cd3o', 'cd4', 'ly4', 'we6v', 'we7v', 'we7o', 'dx2', 'dx2v', 'md1', '0', '定制探针'
      ],
      sequencingPlatform: [
        '必须T7', '必须2000', 'T7/2000'
      ],
      experimentSpecialRequirements: [
        '药厂记录本', '测序结束', '锁定组织'
      ],
      realData: '',
      tableData: [],
      productList: [], // 子产品列表
      sampleSignList: [], // 样本标签列表
      libTypeList: [], // 文库类型列表
      probeList: [],
      comboTypeList: [],
      comboTypeIds: [], // 样本组合列表
      options: [
        {
          value: 'NGS',
          label: 'NGS'
        },
        {
          value: '分子病理',
          label: '分子病理',
          children: [
            {
              value: 'PD-L1',
              label: 'PD-L1'
            },
            {
              value: '包埋',
              label: '包埋'
            },
            {
              value: '镜检',
              label: '镜检'
            },
            {
              value: '包埋+镜检',
              label: '包埋+镜检'
            }
          ]
        },
        {
          value: 'PCR',
          label: 'PCR',
          children: [
            {
              value: 'Sanger',
              label: 'Sanger'
            },
            {
              value: 'EGFR',
              label: 'EGFR'
            },
            {
              value: 'KRAS',
              label: 'KRAS'
            }
          ]
        },
        {
          value: '外送',
          label: '外送',
          children: [
            {
              value: '直接外送',
              label: '直接外送'
            },
            {
              value: 'MSI外送',
              label: 'MSI外送'
            },
            {
              value: 'MGMT外送',
              label: 'MGMT外送'
            }
          ]
        }
      ],
      abnormalDescConfigList: [],
      nucleicGradeConfigList: [],
      pequencingPlatform: ['illumina', 'DNBSEQ-T7RS', 'GeneSeq2000'],
      testType: [],
      organizationMicroscopyOptions: [
        {
          label: 'A新鲜组织，石蜡包埋组织均镜检',
          value: 1
        },
        {
          label: 'B新鲜组织不镜检，石蜡包埋组织镜检',
          value: 3
        },
        {
          label: 'C新鲜组织镜检，石蜡包埋组织不镜检',
          value: 4
        },
        {
          label: 'D新鲜组织，石蜡包埋组织均不镜检',
          value: 0
        }
      ],
      crossCommonConfigs: [],
      // 样本临床补录
      sampleClinical: [
        {
          label: '补录',
          value: 1
        },
        {
          label: '不补录',
          value: 0
        }
      ],
      procedureTemplate: [], // 工序分析
      submitBtnLoading: false, // 提交loading
      isCompare: '0',
      form: {
        productId: this.productId, // 产品id
        seqPlat: '', // 测序平台
        tatType: '', // 产品TAT
        tatTime: '', // 配送TaT
        deliveryPeriod: '', // 交付周期
        fprocedureTemplateId: '', // 工序流程
        fextractVolumn: '', // 提取量
        fisAllowSingle: '', // 是否允许单样本
        fis_pause: '',
        forganizationMicroscopy: '', // 组织镜检
        isOverseasProducts: '', // 海外产品
        ftatSpeedTip: '', // 测序TAT速度提示
        fisOfflineDetect: '', // 是否线下检测 1仅线下，2线上+线下
        fofflineDetectType: '', // 线下检测类型
        fdnaOrRna: '', // DNA/RNA检测
        fcaseExpectDataSize: '',
        fcaseDataSizeFloat: '',
        fcontrolExpectDataSize: '',
        fcontrolDataSizeFloat: '',
        ficNum: '',
        fcrossCommonConfig: '',
        fcomboTypeNames: '',
        fisSampleCompare: null,
        fneedMakeup: ''
      },
      submitForm: {},
      rules: {
        tatType: [
          // { required: true, message: '请选择产品TAT', trigger: ['change'] }
        ],
        tatTime: [
          // {required: true, message: '请选择配送TAT', trigger: ['change', 'blur']},
          {pattern: /^[+]?(\d+)$|^[+]?(\d+\.\d+)$/, message: '产品TAT必须是正数', trigger: ['change', 'blur']}
        ],
        deliveryPeriod: [
          // {required: true, message: '请选择交付周期', trigger: ['change', 'blur']},
          {pattern: /^[+]?(\d+)$|^[+]?(\d+\.\d+)$/, message: '交付周期必须是正数'}
        ],
        fprocedureTemplateId: [
          {required: true, message: '请选择工序流程', trigger: ['change', 'blur']}
        ],
        fofflineDetectType: [
          {required: false, validator: checkDetectType, trigger: ['change', 'blur']}
        ],
        fcaseExpectDataSize: [
          {pattern: /^[0-9]\d*(\.\d{1,2})?$/, message: '只能输入数字，包含两位小数', trigger: 'blur'}
        ],
        fcaseDataSizeFloat: [
          {pattern: /^[0-9]\d*(\.\d{1,2})?$/, message: '只能输入数字，包含两位小数', trigger: 'blur'}
        ],
        fcontrolExpectDataSize: [
          {pattern: /^[0-9]\d*(\.\d{1,2})?$/, message: '只能输入数字，包含两位小数', trigger: 'blur'}
        ],
        fcontrolDataSizeFloat: [
          {pattern: /^[0-9]\d*(\.\d{1,2})?$/, message: '只能输入数字，包含两位小数', trigger: 'blur'}
        ]
      }
    }
  },
  methods: {
    handleOpen () {
      this.$nextTick(() => {
        this.activeName = '基本信息'
        if (this.isSample) {
          this.$refs['form'].resetFields()
          this.getFormData()
          this.getOfflineDetectList()
          this.getCrossCommonConfigs()
          this.getProcedure()
          this.getProductExperiment()
          this.getAllSampleSign()
          this.getAllLibType()
          this.getAllProbe()
          this.getComboTypeList()
          this.getExperiment()
          this.getSequencingPlatform()
          this.getNucleicGradeConfigList()
          this.getAbnormalDescConfigList()
        } else {
          this.getChildProduct()
        }
      })
    },
    getSequencingPlatform () {
      // /system/product/get_all_platforms_config
      this.$ajax({
        url: '/system/product/get_all_platforms_config',
        method: 'get',
        data: {productId: this.productId}
      }).then((result) => {
        if (result.code === this.SUCCESS_CODE) {
          this.pequencingPlatform = result.data
        }
      })
    },
    getNucleicGradeConfigList () {
      this.$ajax({
        url: '/system/dna_level_config/get_dna_level_standard_list'
      }).then((result) => {
        if (result.code === this.SUCCESS_CODE) {
          this.nucleicGradeConfigList = result.data
        }
      })
    },

    // 获取表单信息
    getFormData () {
      this.$ajax({
        url: '/system/product/get_product_detail',
        method: 'get',
        data: {productId: this.productId}
      }).then((result) => {
        if (result.code === this.SUCCESS_CODE) {
          let {
            productId, // 产品id
            seqPlat, // 测序平台
            tatType, // 产品TAT
            tatTime, // 配送TaT
            deliveryPeriod, // 交付周期
            fprocedureTemplateId,
            fextractVolumn, // 提取量
            fisAllowSingle, // 是否允许单样本
            // eslint-disable-next-line camelcase
            fis_pause,
            forganizationMicroscopy,
            isOverseasProducts,
            ftatSpeedTip,
            fisOfflineDetect, // 是否线下检测 1仅线下，2线上+线下
            fofflineDetectType, // 线下检测类型
            fdnaOrRna, // DNA/RNA检测
            fcaseExpectDataSize,
            fcaseDataSizeFloat,
            fcontrolExpectDataSize,
            fcontrolDataSizeFloat,
            fcomboTypeNames,
            fisSampleCompare,
            ficNum,
            fcrossCommonConfig,
            fneedMakeup = 1,
            dectType // 是否自动生成D数据
          } = result.data
          if (fcomboTypeNames) {
            fcomboTypeNames = fcomboTypeNames.split(',')
          }
          this.form = {
            productId,
            seqPlat,
            tatType,
            tatTime,
            deliveryPeriod,
            fprocedureTemplateId,
            fextractVolumn,
            fisAllowSingle,
            fis_pause,
            forganizationMicroscopy,
            isOverseasProducts,
            ftatSpeedTip,
            fisOfflineDetect,
            fofflineDetectType,
            fdnaOrRna,
            fcaseExpectDataSize,
            fcaseDataSizeFloat,
            fcontrolExpectDataSize,
            fcontrolDataSizeFloat,
            fcomboTypeNames,
            fisSampleCompare,
            ficNum,
            fcrossCommonConfig,
            fneedMakeup,
            dectType
          }
          this.realData = result.data
        } else {
          this.$message.error(result.message)
        }
      }).catch((e) => {
        console.log(e)
      })
    },
    getAbnormalDescConfigList () {
      this.$ajax({
        url: '/system/exception_config/get_exception_description_standard_list'
      }).then((result) => {
        if (result.code === this.SUCCESS_CODE) {
          this.abnormalDescConfigList = result.data
        }
      })
    },
    // 线下检测类型列表 /system/product/list_offline_detect
    getOfflineDetectList () {
      this.$ajax({
        url: '/system/product/list_offline_detect',
        method: 'get'
      }).then((result) => {
        if (result.code === this.SUCCESS_CODE) {
          let data = result.data || []
          this.testType = []
          data.forEach(v => {
            let item = {
              id: v.dictCode * 1,
              name: v.dictValue
            }
            this.testType.push(item)
          })
        } else {
          this.$message.error(result.message)
        }
      }).catch((e) => {
        console.log(e)
      })
    },
    getCrossCommonConfigs () {
      this.$ajax({
        url: '/system/product/list_cross_common_config',
        method: 'get'
      }).then((result) => {
        if (result.code === this.SUCCESS_CODE) {
          let data = result.data || []
          this.crossCommonConfigs = []
          data.forEach(v => {
            let item = {
              id: v.dictId,
              name: v.dictValue
            }
            this.crossCommonConfigs.push(item)
          })
        } else {
          this.$message.error(result.message)
        }
      }).catch((e) => {
        console.log(e)
      })
    },
    // 获取工序流程 /system/product/get_procedure_template_name
    getProcedure () {
      this.$ajax({
        url: '/system/product/get_procedure_template_name',
        method: 'get'
      }).then((result) => {
        if (result.code === this.SUCCESS_CODE) {
          result.data = result.data || []
          this.procedureTemplate = []
          result.data.forEach(v => {
            let item = {
              procedureTemplateName: v.fprocedureTemplateName, // 工序模版名称
              fid: v.fid // 工序模版id
            }
            this.procedureTemplate.push(item)
          })
        } else {
          this.$message.error(result.message)
        }
      }).catch((e) => {
        console.log(e)
      })
    },
    // 获取实验标准
    getExperiment () {
      this.$ajax({
        url: '/system/product/get_experimental_standard_parameter_configuration',
        method: 'get',
        data: {productId: this.productId}
      }).then((result) => {
        if (result.code === this.SUCCESS_CODE) {
          let { experimentSpecialRequirements, // 特殊要求
            theLowerLimitOfTheOutputDataVolume, // 产出数据量下限
            sequencingPlatform, // 测序平台标准
            probe, // 探针
            buildAlibraryWay, // 建库方式
            extractionAmount, // 提取用量标准
            extractionMethod, // 提取方式
            plasmaSeparationMethod, // 血浆分离方式
            craftSeriesClassification1, // 工艺系列分类一级选项
            craftSeriesClassification2, // 工艺系列分类二级选项
            affiliatedBusinessDepartment, // 附属事业部
            craftSeriesClassification,
            microscopyStandard,
            fdnaLevelStandard,
            fexceptionDescriptionStandard
          } = result.data
          this.experConfig = { experimentSpecialRequirements, // 特殊要求
            theLowerLimitOfTheOutputDataVolume, // 产出数据量下限
            sequencingPlatform, // 测序平台标准
            probe, // 探针
            buildAlibraryWay, // 建库方式
            extractionAmount, // 提取用量标准
            extractionMethod, // 提取方式
            plasmaSeparationMethod, // 血浆分离方式
            craftSeriesClassification1, // 工艺系列分类一级选项
            craftSeriesClassification2, // 工艺系列分类二级选项
            affiliatedBusinessDepartment, // 附属事业部
            craftSeriesClassification,
            microscopyStandard,
            fdnaLevelStandard,
            fexceptionDescriptionStandard
          }
          this.experConfig.craftSeriesClassification = []
          if (result.data.craftSeriesClassification1) {
            this.experConfig.craftSeriesClassification.push(result.data.craftSeriesClassification1)
          }
          if (result.data.craftSeriesClassification2) {
            this.experConfig.craftSeriesClassification.push(result.data.craftSeriesClassification2)
          }
          if (result.data.plasmaSeparationMethod === '') {
            this.experConfig.plasmaSeparationMethod = []
          } else {
            this.experConfig.plasmaSeparationMethod = result.data.plasmaSeparationMethod.split(',') || []
          }
        } else {
          this.$message.error(result.message)
        }
      })
    },
    async saveExperiment () {
      let config = JSON.parse(JSON.stringify(this.experConfig)) || {}
      config.craftSeriesClassification1 = config.craftSeriesClassification[0]
      config.craftSeriesClassification2 = config.craftSeriesClassification[1]
      let plasmaSeparationMethod = config.plasmaSeparationMethod || []
      config.plasmaSeparationMethod = plasmaSeparationMethod.join(',')
      let result = await this.$ajax({
        url: '/system/product/save_experimental_standard_parameter_configuration',
        method: 'post',
        data: {productId: this.productId, ...config}
      })
      if (result.code !== this.SUCCESS_CODE) {
        this.$message.error(result.message)
      }
      return result.code === this.SUCCESS_CODE
    },
    // 获取组合产品子产品 /system/product/get_child_product
    getChildProduct () {
      this.$ajax({
        url: '/system/product/get_child_product',
        method: 'get',
        data: {productId: this.productId}
      }).then((result) => {
        if (result.code === this.SUCCESS_CODE) {
          result.data = result.data || []
          this.productList = []
          result.data.forEach(v => {
            let item = {
              productCode: v.productCode, // 产品编号
              productName: v.productName, // 产品名称
              readProcess: v.readProcess, // 解读流程
              readTaskDistributionType: v.readTaskDistributionType, // 解读任务分配类型
              seqPlat: v.seqPlat, // 测序平台
              deliverCycle: v.deliveryPeriod // 交付日期
            }
            this.productList.push(item)
          })
        } else {
          this.$message.error(result.message)
        }
      }).catch((e) => {
        console.log(e)
      })
    },
    // 获取实验配置 /system/product/get_product_experiment_param
    getProductExperiment () {
      this.$ajax({
        url: '/system/product/get_product_experiment_param',
        method: 'get',
        data: {productId: this.productId}
      }).then((result) => {
        if (result.code === this.SUCCESS_CODE) {
          result.data = result.data || []
          this.tableData = []
          result.data.forEach(v => {
            let item = {
              fid: v.fid, // id
              fproductId: v.fproductId, // 产品id
              fsampleSign: v.fsampleSign, // 样本标签
              flibType: v.flibType, // 文库类型
              fprobe: v.fprobe, // 探针
              fdataSize: v.fdataSize, // 数据量
              fcrossBase: v.fcrossBase, // 杂交基数
              isFix: false
            }
            this.tableData.push(item)
          })
        } else {
          this.$message.error(result.message)
        }
      }).catch((e) => {
        console.log(e)
      })
    },
    // 获取所有样本标签 /system/product/get_all_sample_sign
    getAllSampleSign () {
      this.$ajax({
        url: '/system/product/get_all_sample_sign',
        method: 'get'
      }).then((result) => {
        if (result.code === this.SUCCESS_CODE) {
          this.sampleSignList = []
          this.sampleSignList = result.data
        } else {
          this.$message.error(result.message)
        }
      }).catch((e) => {
        console.log(e)
      })
    },
    // 获取文库类型 /system/product/get_all_lib_type
    getAllLibType () {
      this.$ajax({
        url: '/system/product/get_all_lib_type',
        method: 'get'
      }).then((result) => {
        if (result.code === this.SUCCESS_CODE) {
          this.libTypeList = []
          this.libTypeList = result.data || []
        } else {
          this.$message.error(result.message)
        }
      }).catch((e) => {
        console.log(e)
      })
    },
    // 获取所有探针 /system/product/get_all_probe
    getAllProbe () {
      this.$ajax({
        url: '/system/product/get_all_probe',
        method: 'get'
      }).then((result) => {
        if (result.code === this.SUCCESS_CODE) {
          this.probeList = []
          this.probeList = result.data || []
        } else {
          this.$message.error(result.message)
        }
      }).catch((e) => {
        console.log(e)
      })
    },
    // 确认提交
    handleDialogConfirm () {
      this.$refs.form.validate(async valid => {
        if (valid) {
          this.submitForm = {...this.realData, ...this.form}
          if (this.submitForm.fcomboTypeNames) this.submitForm.fcomboTypeNames = this.submitForm.fcomboTypeNames.join(',')
          this.submitBtnLoading = true
          this.$ajax({
            url: '/system/product/save_product_config',
            method: 'post',
            data: this.submitForm
          }).then(async (result) => {
            if (result.code === this.SUCCESS_CODE) {
              let res = await this.saveExperiment()
              if (res) {
                this.$message.success('提交成功')
                this.visible = false
                this.$emit('dialogConfirmEvent')
              } else {
                this.$message.error(res.message)
              }
            } else {
              this.$message.error(result.message)
            }
          }).catch((e) => {
            console.log(e)
          }).finally(() => {
            this.submitBtnLoading = false
          })
        } else {
          this.$message.error('存在未输入的必填项，请确认后重新保存')
        }
      })
    },
    // 切换修改保存
    async openFix (row, index) {
      let item = this.tableData[index]
      if (item.isFix === false) {
        item.isFix = true
      } else {
        let isCheck = this.checkRow(row)
        if (isCheck) {
          let flag = await this.fixProductExperiment(row)
          if (flag) item.isFix = false
        }
      }
    },
    // 保存或修改实验配置参数
    async fixProductExperiment (row) {
      if (row.fdataSize === '') row.fdataSize = null
      if (row.fcrossBase === '') row.fcrossBase = null
      let saveFlag = false
      let param = JSON.parse(JSON.stringify(row))
      await this.$ajax({
        url: '/system/product/save_or_update_productExperimentParam',
        method: 'post',
        data: param
      }).then((result) => {
        if (result.code === this.SUCCESS_CODE) {
          this.$message.success('保存产品实验参数配置成功')
          saveFlag = true
        } else {
          this.$message.error(result.message)
        }
      }).catch((e) => {
        console.log(e)
      })
      return saveFlag
    },
    // 校验保存数据
    checkRow (row) {
      console.log('校验中')
      // 样本标签：下拉单，必填
      if (row.fsampleSign === '') {
        this.errorMessage = '请选择样本标签'
        return false
      }
      // 文库类型：下拉单，必填，数据来源数据字典：文库类型
      if (row.flibType === '') {
        this.errorMessage = '请选择文库类型'
        return false
      }
      // 探针：下拉单，必填，数据来源数据字典：探针
      if (row.fprobe === '') {
        this.errorMessage = '请选择探针'
        return false
      }
      // 数据量：非必填，正数，可以有小数
      if (row.fdataSize !== '' && typeof row.fdataSize !== 'number' && row.fdataSize < 0) {
        this.errorMessage = '数据量必须为正数'
        return false
      }
      // 杂交基数：非必填，正数，可以有小数
      if (row.fcrossBase !== '' && typeof row.fcrossBase !== 'number' && row.fcrossBase < 0) {
        this.errorMessage = '杂交基数必须为正数'
        return false
      }
      // 2) 同一个产品里"样本标签"+"文库类型"+"探针"不能重复
      let list = this.tableData.filter(item =>
        item.fsampleSign + item.flibType + item.fprobe === row.fsampleSign + row.flibType + row.fprobe
      )
      if (list.length > 1) {
        this.errorMessage = '同一个产品里"样本标签"+"文库类型"+"探针"不能重复'
        return false
      }
      this.errorMessage = ''
      return true
    },
    // 添加一行实验配置参数
    addProductExperiment () {
      this.tableData.push(
        {
          fproductId: this.productId, // 产品id
          fsampleSign: '', // 样本标签
          flibType: '', // 文库类型
          fprobe: '', // 探针
          fdataSize: null, // 数据量
          fcrossBase: null, // 杂交基数
          isFix: true
        }
      )
    },
    // 删除一行实验配置参数
    handleDelete (index) {
      console.log(index)
      if (!this.tableData[index].fid) {
        this.tableData.splice(index, 1)
      } else {
        let self = this
        this.$ajax({
          url: '/system/product/delete_productExperimentParam',
          method: 'get',
          data: {fid: this.tableData[index].fid}
        }).then((result) => {
          if (result.code === this.SUCCESS_CODE) {
            self.tableData.splice(index, 1)
            this.$message.success('删除产品实验参数配置成功')
          } else {
            this.$message.error(result.message)
          }
        }).catch((e) => {
          console.log(e)
        })
      }
    },
    // 获取样本组合 /system/product/get_combo_type_list
    getComboTypeList () {
      this.$ajax({
        url: '/system/product/get_combo_type_list',
        method: 'get'
      }).then((result) => {
        if (result.code === this.SUCCESS_CODE) {
          result.data = result.data || []
          this.comboTypeList = []
          result.data.forEach(v => {
            let item = {
              fid: v.fid,
              fcomboTypeName: v.fcomboTypeName
            }
            this.comboTypeList.push(item)
          })
        } else {
          this.$message.error(result.message)
        }
      }).catch((e) => {
        console.log(e)
      })
    },
    handleRadioChange (label) {
      this.disabled = (label !== '0')
      this.isCompare = label
    }
  }
}
</script>

<style scoped lang="scss">
.basic-form {
  padding: 0 5px;
  .basic-form-title {
    padding-left: 5px;
    border-left: 3px solid deepskyblue;
    margin-bottom: 5px;
  }
}
.tips {
  margin: 0 70px 40px 70px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  background: #dfefff;
}
.title {
  padding-left: 5px;
  border-left: 3px solid deepskyblue;
  margin: 10px 0;
}

.icon-wrapper {
  display: flex;
  align-content: center;
  justify-content: space-between;
  color: deepskyblue;
}

.wrapper {
  height: 50vh;
  overflow-y: auto;
  overflow-x: hidden;
}

/* 优化下拉框样式 */
/deep/ .el-select {
  width: 100%;
}

/deep/ .el-select .el-input__inner {
  border-radius: 4px;
  border-color: #dcdfe6;
  transition: border-color 0.2s;
}

/deep/ .el-select .el-input__inner:hover {
  border-color: #c0c4cc;
}

/deep/ .el-select .el-input__inner:focus {
  border-color: #409eff;
}

/deep/ .el-select-dropdown__item {
  padding: 0 15px;
  line-height: 34px;
  font-size: 14px;
}

/deep/ .el-select-dropdown__item.selected {
  color: #409eff;
  font-weight: bold;
}

/deep/ .el-form-item__label {
  font-weight: 500;
}
</style>
