<template>
  <div>
    <div style="margin: 20px 0 0 0;border-bottom: 1px solid #ccc;">
      <el-form :model="form" size="mini" label-width="120px" inline>
        <div>
          <el-form-item label="Gene+编号(精确)">
            <el-input v-model="form.geneplusCodeAccurate" class="form-content" clearable></el-input>
          </el-form-item>
          <el-form-item label="Gene+编号">
            <el-input v-model="form.geneplusCode" class="form-content" clearable></el-input>
          </el-form-item>
          <el-form-item label="原始编号">
            <el-input v-model="form.originNum" class="form-content" clearable></el-input>
          </el-form-item>
          <el-form-item label="产品/项目名称">
            <el-input v-model="form.productName" class="form-content" clearable></el-input>
          </el-form-item>
        </div>
        <div>
          <el-form-item label="送检单位">
            <el-input v-model="form.unit" size="mini"  class="form-content"></el-input>
          </el-form-item>
          <el-form-item label="到样时间">
            <el-date-picker
              v-model="form.arrivalSampleDate[0]"
              type="date"
              size="mini"
              class="form-content"
              placeholder="选择日期">
            </el-date-picker>
            <span>--</span>
            <el-date-picker
              v-model="form.arrivalSampleDate[1]"
              type="date"
              size="mini"
              class="form-content"
              placeholder="选择日期">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="临床科研">
            <el-select v-model="form.clinicalResearch" size="mini" class="form-content" clearable>
              <el-option label="科研" value="0"></el-option>
              <el-option label="临床" value="1"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="样本异常">
            <el-select v-model="form.abnormalSample" size="mini" class="form-content" clearable>
              <template v-for="item in abnormalSampleLists">
                <el-option :key="item.value" :label="item.label" :value="item.value"></el-option>
              </template>
            </el-select>
          </el-form-item>
        </div>
      </el-form>
    </div>
    <div class="buttonGroup">
      <el-button type="text" size="mini">样本接受表下载</el-button>
      <el-button type="text" size="mini" @click="importDialogVisible = true">导入</el-button>
      <el-button type="text" size="mini" >到样确认</el-button>
      <el-button type="text" size="mini" >转入镜检</el-button>
      <el-button type="text" size="mini" >信息修改</el-button>
      <el-button type="text" size="mini" >登记异常</el-button>
      <el-button type="text" size="mini" >生产</el-button>
    </div>
    <div>
      <el-table
        ref="table"
        :data="tableData"
        :height="tableHeight"
        class="reservationTable"
        style="width: 100%"
        @select="handleSelectTable"
        @row-click="handleRowClick">
        <el-table-column type="selection" width="45"></el-table-column>
        <el-table-column prop="geneplusCode" label="Gene+编号" width="180" show-overflow-tooltip></el-table-column>
        <el-table-column prop="originNum" label="原始编号" width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="patientCode" label="患者节点ID" width="180" show-overflow-tooltip></el-table-column>
        <el-table-column prop="arrivalSampleConfirm" label="到样确认" width="100" show-overflow-tooltip></el-table-column>
        <el-table-column prop="sendSampleType" label="送样类型" width="180" show-overflow-tooltip></el-table-column>
        <el-table-column prop="bloodCollectionTube" label="采血管" width="180" show-overflow-tooltip></el-table-column>
        <el-table-column prop="bloodCollectionTubeNum" label="采血管批次号" width="180" show-overflow-tooltip></el-table-column>
        <el-table-column prop="transportMethod" label="运输方式" width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="arrivalSampleAccount" label="送样量" width="220" show-overflow-tooltip></el-table-column>
        <el-table-column prop="sampleCollectionDate" label="样本采集时间" width="180" show-overflow-tooltip></el-table-column>
        <el-table-column prop="arrivalSampleDate" label="到样时间" width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="notes" label="备注" width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="position" label="位置信息" width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="exceptionType" label="异常分类" width="100" show-overflow-tooltip></el-table-column>
        <el-table-column prop="exceptionRemark" label="异常描述" width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="sampleDeal" label="异常处理措施" width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="unit" label="送检单位" width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="productName" label="产品名称/项目名称" width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="productionAreaName" label="生产片区" width="140" show-overflow-tooltip></el-table-column>
        <!-- <el-table-column prop="sampleUseType" label="科研临床" width="140" show-overflow-tooltip></el-table-column> -->
        <el-table-column prop="isCreateOrder" label="是否已生产" width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="creator" label="记录建立者" width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="createTime" label="记录建立时间" width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="modifier" label="最后修改者" width="140" show-overflow-tooltip></el-table-column>
        <el-table-column prop="modifyDate" label="最后修改时间" width="140" show-overflow-tooltip></el-table-column>
      </el-table>
      <el-pagination
              :page-sizes="pageSizes"
              :page-size="pageSize"
              :current-page.sync="currentPage"
              :total="totalPage"
              layout="total, sizes, prev, pager, next, jumper, slot"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange">
        <button @click="handleRefresh"><icon-svg icon-class="icon-refresh" /></button>
      </el-pagination>
    </div>
    <import-dialog :pvisible.sync="importDialogVisible" />
  </div>
</template>

<script>
// import num from './components/cc'
import mixins from '../../../util/mixins'
import util from '../../../util/util'
import importDialog from './sampleArrivalConfirmImportDialog'
export default {
  name: 'sampleArrivalConfirm',
  mixins: [mixins.tablePaginationCommonData],
  components: {
    importDialog
  },
  beforeMount () {
    this.$_setTbHeight()
    window.addEventListener('resize', this.$_setTbHeight)
    this.$once('hook:beforeDestroy', () => {
      window.removeEventListener('resize', this.$_setTbHeight)
    })
  },
  mounted () {
    this.handleSearch()
  },
  data () {
    return {
      selectedRows: new Map(),
      importDialogVisible: false,
      tableHeight: 0,
      form: {
        geneplusCodeAccurate: '', // 吉因加编号精确
        geneplusCode: '', // 吉因加编号
        originNum: '', // 原始编号
        productName: '', // 产品/项目名称
        unit: '', // 送检单位
        arrivalSampleDate: ['', ''], // 到样时间
        abnormalSample: '', // 样本异常
        researchClinical: '' // 科研临床
      },
      formSubmit: {},
      abnormalSampleLists: [ // 样本异常选项列表
        {label: '样本类型错误', value: 1},
        {label: '采集容器错误', value: 2},
        {label: '送样量不正确', value: 3},
        {label: '采集时机不正确', value: 4},
        {label: '运输时间不当/样本超时', value: 5},
        {label: '其他', value: 6}
      ]
    }
  },
  methods: {
    $_setTbHeight () {
      let h1 = document.documentElement.clientHeight - 1
      this.tableHeight = h1 - 64 - 50 - 20 - 20 - 51 * 3 - 45 - 42 - 20
    },
    getData () {
      this.$ajax({
        url: '/sample/confirm/get_sample_confirm_list',
        data: {
          page: {
            current: this.currentPage,
            size: this.pageSize
          },
          params: {}
        },
        loadingDom: '.reservationTable'
      }).then(res => {
        if (res && res.code === this.SUCCESS_CODE) {
          this.selectedRows.clear()
          this.totalPage = res.data.total
          let rows = res.data.rows || []
          this.tableData = []
          rows.forEach(v => {
            let item = {
              id: v.sampleConfirmId,
              geneplusCode: v.geneSampleNum,
              originNum: v.originNum,
              patientCode: v.patientCode,
              arrivalSampleConfirm: v.confirmStatus,
              sendSampleType: v.sampleType,
              bloodCollectionTube: v.bloodTube,
              bloodCollectionTubeNum: v.fbloodTubeBatch,
              transportMethod: v.transportType,
              arrivalSampleAccount: v.sampleCount,
              sampleCollectionDate: v.collectionTime,
              arrivalSampleDate: v.storageTime,
              notes: v.remark,
              position: v.location,
              exceptionType: v.fexceptionType,
              exceptionRemark: v.exceptionRemark,
              sampleDeal: v.sampleDeal,
              unit: v.inspectionUnit,
              productName: v.productName,
              productionAreaName: v.projectName,
              sampleUseType: v.samleUseType,
              isCreateOrder: v.isCreateOrder,
              creator: v.creator,
              createTime: v.createTime,
              modifier: v.modifier,
              modifyDate: v.updateTime
            }
            item.realData = util.deepCopy(item)
            util.setDefaultEmptyValueForObject(item)
            this.tableData.push(item)
          })
        } else {
          this.$message.error(res.message)
        }
      })
    },
    handleSearch () {
      this.formSubmit = {...this.form}
      this.currentPage = 1
      this.getData()
    },
    handleReset () {
      this.form = {
        geneplusCodeAccurate: '', // 吉因加编号精确
        geneplusCode: '', // 吉因加编号
        originNum: '', // 原始编号
        productName: '', // 产品/项目名称
        unit: '', // 送检单位
        arrivalSampleDate: ['', ''], // 到样时间
        abnormalSample: '', // 样本异常
        researchClinical: '' // 科研临床
      }
      this.handleSearch()
    },
    // 点击行
    handleRowClick (row) {
      // if (!this.selectedRows.has(row.patientCode)) {
      //   this.$refs.table.clearSelection()
      //   this.selectedRows.clear()
      // }
      this.$refs.table.toggleRowSelection(row, !this.selectedRows.has(row.id))
      this.handleSelectTable(undefined, row)
    },
    // 选中行
    handleSelectTable (selection, row) {
      this.selectedRows.has(row.id)
        ? this.selectedRows.delete(row.id)
        : this.selectedRows.set(row.id, row)
    }
  }
}
</script>

<style scoped lang="scss">
  .form-content{
    width: 150px;
  }
  .buttonGroup{
    height: 45px;
    display: flex;
    align-items: center;
    margin: 0 20px;
  }
</style>
