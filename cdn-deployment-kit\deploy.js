#!/usr/bin/env node

/**
 * CDN部署工具包 - 主部署脚本
 * 集成构建优化、性能分析和CDN部署功能
 * 完全独立运行，不修改原始项目文件
 */

const fs = require('fs')
const path = require('path')

// 导入统一日志系统
const { logger } = require('./logger')

// 创建带上下文的日志器
const log = logger.child('deploy')

// 导入工具模块
const { ParallelBuildOptimizer } = require('./performance-tools')
const { BuildPerformanceAnalyzer } = require('./build-performance-analyzer')
// const { BuildWarningFixer } = require('./build-warning-fixer')
const { OSSUploader } = require('./oss-uploader')
const { HtmlUpdater } = require('./html-updater')
const { WebpackConfigUpdater } = require('./webpack-config-updater')

/**
 * CDN部署管理器
 */
class CDNDeploymentManager {
  constructor (options = {}) {
    // 如果在cdn-deployment-kit目录中运行，使用父目录作为项目根目录
    const currentDir = process.cwd()
    const isInCDNKit = currentDir.endsWith('cdn-deployment-kit')
    this.projectRoot = options.projectRoot || (isInCDNKit ? path.dirname(currentDir) : currentDir)

    // 设置环境和提供商
    this.environment = options.environment || 'test'
    this.provider = options.provider || 'aliOSS'

    log.debug(`项目根目录: ${this.projectRoot}`)
    log.debug(`部署环境: ${this.environment}`)
    log.debug(`CDN提供商: ${this.provider}`)

    // 加载 CDN 配置
    const { getConfig } = require('./cdn-config')
    this.cdnConfig = getConfig(this.environment, this.provider)

    this.buildOptimizer = new ParallelBuildOptimizer(this.projectRoot)
    this.performanceAnalyzer = new BuildPerformanceAnalyzer(this.projectRoot)
    // this.warningFixer = new BuildWarningFixer(this.projectRoot)
    this.ossUploader = new OSSUploader({
      projectRoot: this.projectRoot,
      environment: this.environment,
      provider: this.provider
    })
    this.htmlUpdater = new HtmlUpdater(this.projectRoot)
    this.webpackConfigUpdater = new WebpackConfigUpdater(this.projectRoot)

    this.deploymentResults = {
      timestamp: new Date().toISOString(),
      buildResult: null,
      performanceMetrics: null,
      uploadResult: null,
      success: false
    }
  }

  /**
   * 执行完整的CDN部署流程
   */
  async deploy (options = {}) {
    log.start('CDN部署工具包启动')
    log.separator('=', 50)
    log.plain('')

    const startTime = Date.now()

    try {
      // 1. 环境检查
      if (!options.skipEnvCheck) {
        await this.checkEnvironment()
      }

      // 2. 构建优化
      if (options.build !== false) {
        log.build('阶段 1: 构建优化')

        // 获取 CDN URL 并在构建时设置 publicPath
        const cdnUrl = this.cdnConfig.getCDNUrl()
        const buildOptions = {
          cdnUrl: cdnUrl
        }

        log.config(`使用 CDN URL: ${cdnUrl}`)
        log.config(`部署环境: ${this.environment}`)
        log.config(`基础路径: ${this.cdnConfig.basePath}`)

        this.deploymentResults.buildResult = await this.runOptimizedBuild(buildOptions)
        log.plain('')
      }

      // 3. 性能分析
      if (options.analyze !== false) {
        log.analyze('阶段 2: 性能分析')
        this.deploymentResults.performanceMetrics = await this.runPerformanceAnalysis()
        log.plain('')
      }
      // 5. CDN上传
      if (options.deploy !== false) {
        log.deploy('阶段 3: CDN部署')

        // 传递环境配置到上传器
        const uploadOptions = {
          ...options,
          environment: this.environment,
          provider: this.provider,
          cdnConfig: this.cdnConfig
        }

        this.deploymentResults.uploadResult = await this.uploadToCDN(uploadOptions)
        log.plain('')
      }

      // 6. 生成部署报告
      await this.generateDeploymentReport()

      const totalTime = Date.now() - startTime
      this.deploymentResults.success = true

      log.complete('CDN部署完成！')
      log.time(`总耗时: ${(totalTime / 1000).toFixed(2)}s`)
      this.displayDeploymentSummary()

      return this.deploymentResults
    } catch (error) {
      log.plain('')
      log.error('CDN部署失败:', error.message)
      this.deploymentResults.success = false
      this.deploymentResults.error = error.message
      throw error
    }
  }

  /**
   * 环境检查
   */
  async checkEnvironment () {
    log.progress('检查部署环境...')

    // 检查项目结构
    const possibleWebpackPaths = [
      'build/webpack.prod.conf.js',
      'webpack.config.js',
      'config/webpack.prod.js'
    ]

    let webpackConfigPath = null
    for (const configPath of possibleWebpackPaths) {
      const fullPath = path.join(this.projectRoot, configPath)
      if (fs.existsSync(fullPath)) {
        webpackConfigPath = configPath
        break
      }
    }

    if (!webpackConfigPath) {
      log.warn('未找到webpack配置文件，将跳过构建阶段')
      log.info('支持的配置文件路径:')
      possibleWebpackPaths.forEach(path => {
        log.debug(`     - ${path}`)
      })
    }

    // 检查package.json
    const packageJsonPath = path.join(this.projectRoot, 'package.json')
    if (!fs.existsSync(packageJsonPath)) {
      throw new Error('缺少必要文件: package.json')
    }

    // 保存找到的webpack配置路径
    this.webpackConfigPath = webpackConfigPath

    // 检查Node.js版本
    const nodeVersion = process.version
    log.debug(`Node.js版本: ${nodeVersion}`)

    // 检查可用内存
    const totalMemory = Math.round(require('os').totalmem() / 1024 / 1024 / 1024)
    log.debug(`系统内存: ${totalMemory}GB`)

    log.success('环境检查通过')
  }

  /**
   * 运行优化构建
   */
  async runOptimizedBuild (options = {}) {
    log.build('开始优化构建...')

    try {
      // 检查是否有webpack配置
      if (!this.webpackConfigPath) {
        throw new Error('未找到webpack配置文件，无法执行构建')
      }

      // 加载webpack配置
      const webpackConfigPath = path.join(this.projectRoot, this.webpackConfigPath)
      log.debug(`使用配置文件: ${this.webpackConfigPath}`)

      let originalConfig
      try {
        // 清除require缓存，确保获取最新配置
        delete require.cache[require.resolve(webpackConfigPath)]
        originalConfig = require(webpackConfigPath)

        // 验证配置是否为函数或对象
        if (typeof originalConfig === 'function') {
          log.debug('检测到函数式webpack配置，正在调用...')
          originalConfig = originalConfig()
        }

        if (!originalConfig || typeof originalConfig !== 'object') {
          throw new Error('webpack配置必须是对象或返回对象的函数')
        }

        log.debug('webpack配置加载成功')
      } catch (error) {
        throw new Error(`加载webpack配置失败: ${error.message}`)
      }

      // 生成优化配置
      let optimizedConfig = this.buildOptimizer.generateOptimizedWebpackConfig(originalConfig)

      // 如果提供了 CDN URL，动态设置 publicPath
      if (options.cdnUrl) {
        log.config('设置动态 publicPath...')

        // 使用修复后的配置更新器
        optimizedConfig = this.webpackConfigUpdater.updatePublicPath(optimizedConfig, options.cdnUrl)

        // 暂时禁用运行时插件和验证，避免兼容性问题
        // this.webpackConfigUpdater.addRuntimePublicPathPlugin(optimizedConfig, options.cdnUrl)
        // this.webpackConfigUpdater.validateConfigUpdate(optimizedConfig, options.cdnUrl)
      }

      // 执行构建
      const webpack = require('webpack')
      const startTime = Date.now()
      return new Promise((resolve, reject) => {
        webpack(optimizedConfig, (err, stats) => {
          const buildTime = Date.now() - startTime

          if (err) {
            reject(err)
            return
          }

          const warnings = stats.hasWarnings() ? stats.compilation.warnings.length : 0
          const errors = stats.hasErrors() ? stats.compilation.errors.length : 0

          const buildResult = {
            buildTime,
            buildTimeSeconds: (buildTime / 1000).toFixed(2),
            warnings,
            errors,
            assets: Object.keys(stats.compilation.assets).length,
            memory: process.memoryUsage()
          }

          if (errors > 0) {
            log.error('构建包含错误')
            stats.compilation.errors.forEach(error => {
              log.error(`   ${error.message}`)
            })
            reject(new Error('构建包含错误'))
          } else {
            log.success('优化构建完成')
            log.time(`构建时间: ${buildResult.buildTimeSeconds}s`)
            log.debug(`生成文件: ${buildResult.assets} 个`)
            log.debug(`警告: ${warnings} 个`)
            resolve(buildResult)
          }
        })
      })
    } catch (error) {
      log.error('构建失败:', error.message)
      throw error
    }
  }

  /**
   * 运行性能分析
   */
  async runPerformanceAnalysis () {
    log.analyze('开始性能分析...')

    try {
      const bundleAnalysis = this.performanceAnalyzer.analyzeBundleOutput()
      const performanceMetrics = this.performanceAnalyzer.calculatePerformanceMetrics(
        this.deploymentResults.buildResult,
        bundleAnalysis
      )

      log.success('性能分析完成')
      log.performance(`总体评分: ${performanceMetrics.overallScore}/100`)

      return performanceMetrics
    } catch (error) {
      log.error('性能分析失败:', error.message)
      throw error
    }
  }

  /**
   * 修复构建警告
   */
  async fixBuildWarnings () {
    log.config('开始修复构建警告...')

    try {
      await this.warningFixer.fixAllWarnings()
      log.success('警告修复完成')
    } catch (error) {
      log.warn('警告修复失败:', error.message)
      // 警告修复失败不应该中断整个流程
    }
  }

  /**
   * 上传到CDN
   */
  async uploadToCDN (options = {}) {
    log.upload('开始CDN上传...')

    try {
      // 使用传递的 CDN 配置或创建新的上传器
      let uploader = this.ossUploader

      if (options.environment || options.provider || options.cdnConfig) {
        // 如果有新的配置，创建新的上传器实例
        const uploaderOptions = {
          projectRoot: this.projectRoot,
          environment: options.environment || this.environment,
          provider: options.provider || this.provider,
          target: options.target || options.provider || this.provider,
          cdnConfig: options.cdnConfig || this.cdnConfig,
          ...options.ossConfig
        }

        uploader = new OSSUploader(uploaderOptions)
      }

      const uploadResult = await uploader.uploadToCDN()

      log.success('CDN上传完成')

      // 上传完成后更新 HTML 资源引用
      if (options.updateHtml !== false && uploadResult.cdnUrl) {
        await this.updateHtmlReferences(uploadResult.cdnUrl, options.htmlUpdateOptions)
      }

      return uploadResult
    } catch (error) {
      log.error('CDN上传失败:', error.message)
      throw error
    }
  }

  /**
   * 更新 HTML 资源引用
   */
  async updateHtmlReferences (cdnUrl, options = {}) {
    log.config('更新 HTML 资源引用...')

    try {
      const updateResult = this.htmlUpdater.updateHtmlReferences(cdnUrl, {
        backup: true,
        updateImages: true,
        updateFonts: true,
        ...options
      })

      log.success('HTML 资源引用更新完成')
      log.debug(`更新文件: ${updateResult.updatedFile}`)
      log.debug(`CDN URL: ${updateResult.cdnUrl}`)

      // 将更新结果添加到部署结果中
      this.deploymentResults.htmlUpdateResult = updateResult

      return updateResult
    } catch (error) {
      log.error('HTML 资源引用更新失败:', error.message)

      // 如果 HTML 更新失败，记录错误但不中断部署流程
      this.deploymentResults.htmlUpdateError = {
        message: error.message,
        timestamp: new Date().toISOString()
      }

      throw error
    }
  }

  /**
   * 生成部署报告
   */
  async generateDeploymentReport () {
    const reportPath = path.join(this.projectRoot, 'cdn-deployment-report.json')

    const report = {
      ...this.deploymentResults,
      generatedAt: new Date().toISOString(),
      version: '1.0.0'
    }

    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2))
    log.file(`部署报告已保存: ${reportPath}`)
  }

  /**
   * 显示部署摘要
   */
  displayDeploymentSummary () {
    log.plain('')
    log.analyze('CDN部署摘要')
    log.separator('=', 50)

    if (this.deploymentResults.buildResult) {
      log.time(`构建时间: ${this.deploymentResults.buildResult.buildTimeSeconds}s`)
      log.file(`生成文件: ${this.deploymentResults.buildResult.assets} 个`)
    }

    if (this.deploymentResults.performanceMetrics) {
      log.performance(`性能评分: ${this.deploymentResults.performanceMetrics.overallScore}/100`)
    }

    if (this.deploymentResults.uploadResult) {
      log.upload(`上传文件: ${this.deploymentResults.uploadResult.uploadedFiles} 个`)
      log.size(`总大小: ${(this.deploymentResults.uploadResult.totalSize / 1024 / 1024).toFixed(2)} MB`)
    }
  }
}

/**
 * 简单的命令行参数解析
 */
function parseArguments () {
  const args = process.argv.slice(2)
  const command = args[0] || 'help'

  const options = {
    environment: 'test', // 默认测试环境
    provider: 'aliOSS',
    target: 'aliOSS', // 保持向后兼容
    build: true,
    analyze: true,
    deploy: true,
    fixWarnings: true,
    skipEnvCheck: false
  }

  // 解析选项
  for (let i = 1; i < args.length; i++) {
    const arg = args[i]
    switch (arg) {
      case '--env':
      case '-e':
        options.environment = args[i + 1]
        i++
        break
      case '--provider':
      case '-p':
        options.provider = args[i + 1]
        options.target = args[i + 1] // 保持向后兼容
        i++
        break
      case '--target':
      case '-t':
        options.target = args[i + 1]
        options.provider = args[i + 1] // 同步到新的 provider 字段
        i++
        break
      case '--no-build':
        options.build = false
        break
      case '--no-analyze':
        options.analyze = false
        break
      case '--no-deploy':
        options.deploy = false
        break
      case '--no-fix-warnings':
        options.fixWarnings = false
        break
      case '--skip-env-check':
        options.skipEnvCheck = true
        break
    }
  }

  return { command, options }
}

/**
 * 显示帮助信息
 */
function showHelp () {
  log.start('CDN部署工具包 - 使用说明')
  log.plain('')
  log.info('用法:')
  log.plain('  node cdn-deployment-kit/deploy.js <command> [options]')
  log.plain('')
  log.info('命令:')
  log.plain('  deploy    执行完整的CDN部署流程')
  log.plain('  build     仅执行优化构建')
  log.plain('  analyze   仅执行性能分析')
  log.plain('  help      显示帮助信息')
  log.plain('')
  log.info('选项:')
  log.plain('  -e, --env <environment>  部署环境 (development|test|production, 默认: test)')
  log.plain('  -p, --provider <provider> CDN提供商 (aliOSS|tencentCOS|awsS3|qiniu, 默认: aliOSS)')
  log.plain('  -t, --target <target>    CDN目标 (同 --provider, 保持向后兼容)')
  log.plain('  --no-build              跳过构建阶段')
  log.plain('  --no-analyze            跳过性能分析')
  log.plain('  --no-deploy             跳过CDN部署')
  log.plain('  --skip-env-check        跳过环境检查')
  log.plain('  --no-fix-warnings       跳过警告修复')
  log.plain('')
  log.info('示例:')
  log.plain('  node cdn-deployment-kit/deploy.js deploy --env development')
  log.plain('  node cdn-deployment-kit/deploy.js deploy --env production')
  log.plain('  node cdn-deployment-kit/deploy.js deploy --env test --provider aliOSS')
  log.plain('  node cdn-deployment-kit/deploy.js build --env production')
  log.plain('  node cdn-deployment-kit/deploy.js deploy --no-build --env production')
}

/**
 * 主入口函数
 */
async function main () {
  const { command, options } = parseArguments()

  try {
    switch (command) {
      case 'deploy':
        const manager = new CDNDeploymentManager({
          environment: options.environment,
          provider: options.provider
        })
        await manager.deploy(options)
        break

      case 'build':
        const buildManager = new CDNDeploymentManager({
          environment: options.environment,
          provider: options.provider
        })
        await buildManager.deploy({
          ...options,
          deploy: false,
          analyze: false,
          fixWarnings: false,
          skipEnvCheck: false // 需要环境检查来设置 webpackConfigPath
        })
        break

      case 'analyze':
        const analyzer = new BuildPerformanceAnalyzer()
        const bundleAnalysis = analyzer.analyzeBundleOutput()
        const mockBuildResult = {
          buildTime: 0,
          buildTimeSeconds: '0.00',
          warnings: 0,
          errors: 0,
          memory: { heapUsed: 0, heapTotal: 0, external: 0 }
        }

        const performanceMetrics = analyzer.calculatePerformanceMetrics(mockBuildResult, bundleAnalysis)
        const recommendations = analyzer.generateRecommendations(performanceMetrics, bundleAnalysis)

        log.performance(`总体评分: ${performanceMetrics.overallScore}/100`)
        if (recommendations.length > 0) {
          log.info(`优化建议: ${recommendations.length} 条`)
          recommendations.forEach((rec, index) => {
            log.debug(`  ${index + 1}. [${rec.category}] ${rec.issue}`)
            log.debug(`     解决方案: ${rec.solution}`)
          })
        }
        break

      case 'help':
      default:
        showHelp()
        break
    }
  } catch (error) {
    log.plain('')
    log.error('执行失败:', error.message)
    process.exit(1)
  }
}

// 如果直接运行此文件
if (require.main === module) {
  main().catch(error => {
    log.error('程序执行失败:', error.message)
    log.debug('错误堆栈:', error.stack)
    process.exit(1)
  })
}

module.exports = {
  CDNDeploymentManager
}
