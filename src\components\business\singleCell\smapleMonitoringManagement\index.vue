<template>
  <div class="wrapper">
    <div class="search-form">
      <el-form
        ref="form"
        :model="form"
        :inline="true"
        label-width="100px"
        size="mini"
        label-suffix=":"
        @keyup.enter.native="handleSearch">
        <el-form-item label="样本原始名称">
          <el-input
            v-model.trim="form.cosSampleName"
            placeholder="批量精确查询"
            class="form-width"
            size="mini"
            clearable>
          </el-input>
        </el-form-item>

        <el-form-item label="吉因加编号">
          <el-input
            v-model.trim="form.geneNum"
            placeholder="批量精确查询"
            class="form-width"
            size="mini"
            clearable>
          </el-input>
        </el-form-item>

        <el-form-item label="项目编号">
          <el-input
            v-model.trim="form.projectCode"
            class="form-width"
            placeholder="请输入"
            size="mini"
            clearable>
          </el-input>
        </el-form-item>

        <el-form-item label="生产序号">
          <el-input
            v-model.trim="form.productionNumber"
            class="form-width"
            placeholder="请输入"
            size="mini"
            clearable>
          </el-input>
        </el-form-item>

        <el-form-item label="产品名称">
          <el-input
            v-model.trim="form.productName"
            class="form-width"
            placeholder="请输入"
            size="mini"
            clearable>
          </el-input>
        </el-form-item>

        <el-form-item label="文库编号">
          <el-input
            v-model.trim="form.libNum"
            size="mini"
            class="form-width"
            placeholder="请输入"
            clearable>
          </el-input>
        </el-form-item>
        <el-form-item label="异常状态">
          <el-select v-model="form.exceptionType" size="mini" clearable  class="form-width" placeholder="请选择">
            <el-option v-for="item in exceptionTypeList"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="到样异常">
          <el-select v-model.trim="form.fconfirmException" size="mini" class="form-width" clearable>
            <el-option v-for="item in EXCEPT_OPTION_LIST" :key="item" :label="item" :value="item" ></el-option>
          </el-select>
        </el-form-item>

      </el-form>
    </div>
    <search-params-dialog
      :pvisible.sync="searchDialogVisible"
      @reset="handleReset"
      @search="handleSearch">
      <el-form
        ref="form"
        class="params-search-form"
        :model="form"
        label-width="120px"
        label-suffix=":"
        size="small"
        label-position="top"
        inline>
        <el-form-item label="子订单编号">
          <el-input
            v-model.trim="form.subOrderNo"
            placeholder="请输入"
            size="mini"
            class="form-width"
            clearable>
          </el-input>
        </el-form-item>

        <el-form-item label="项目编号">
          <el-input
            v-model.trim="form.projectCode"
            placeholder="请输入"
            size="mini"
            class="form-width"
            clearable>
          </el-input>
        </el-form-item>

        <el-form-item label="吉因加编号">
          <el-input
            v-model.trim="form.geneNum"
            type="textarea"
            placeholder="批量精确查询"
            class="form-width"
            size="mini">
          </el-input>
        </el-form-item>

        <el-form-item label="文库编号">
          <el-input
            v-model.trim="form.libNum"
            placeholder="请输入"
            size="mini"
            class="form-width"
            clearable>
          </el-input>
        </el-form-item>

        <el-form-item label="项目名称">
          <el-input
            v-model.trim="form.projectName"
            placeholder="请输入"
            size="mini"
            class="form-width"
            clearable>
          </el-input>
        </el-form-item>

        <el-form-item label="样本原始名称">
          <el-input
            v-model.trim="form.sampleOriginName"
            type="textarea"
            placeholder="批量精确查询"
            class="form-width"
            size="mini">
          </el-input>
        </el-form-item>

        <el-form-item label="生产序号">
          <el-input
            v-model.trim="form.productionNumber"
            placeholder="请输入"
            size="mini"
            class="form-width"
            clearable>
          </el-input>
        </el-form-item>

        <el-form-item label="产品名称">
          <el-input
            v-model.trim="form.productName"
            placeholder="请输入"
            size="mini"
            class="form-width"
            clearable>
          </el-input>
        </el-form-item>

        <el-form-item label="是否已上机">
          <el-select
            v-model.trim="form.isSequenced"
            placeholder="请选择"
            size="mini"
            class="form-width"
            clearable>
            <el-option v-for="(key, value) in booleanOptions" :key="key" :label="key" :value="value"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="异常状态">
          <el-select v-model="form.exceptionType" size="mini" clearable  class="form-width" placeholder="请选择">
          <el-option v-for="item in exceptionTypeList"
                     :key="item.value"
                     :label="item.label"
                     :value="item.value">
          </el-option>
        </el-select>
        </el-form-item>

        <el-form-item label="到样异常">
          <el-select v-model.trim="form.fconfirmException" size="mini" class="form-width" clearable>
            <el-option v-for="item in EXCEPT_OPTION_LIST" :key="item" :label="item" :value="item" ></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="到样日期">
          <el-date-picker
            v-model.trim="form.time"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            size="mini"
            clearable
            :default-time="['00:00:00', '23:59:59']"
            :value-format="'yyyy-MM-dd HH:mm:ss'"
            class="form-long-width"/>
        </el-form-item>

        <el-form-item label="是否达到质控标准">
          <el-select
            v-model.trim="form.isQualityStandard"
            placeholder="请选择"
            size="mini"
            class="form-width"
            clearable>
            <el-option v-for="(key, value) in booleanOptions" :key="key" :label="key" :value="value"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="交付状态">
          <el-select
            v-model.trim="form.deliveryStatus"
            placeholder="请选择"
            size="mini"
            class="form-width"
            clearable>
            <el-option v-for="(key, value) in deliverConfig" :key="key" :label="key" :value="value"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="质控分析状态">
          <el-select
            v-model.trim="form.qcAnalysisStatus"
            placeholder="请选择"
            size="mini"
            class="form-width"
            clearable>
            <el-option v-for="(key, value) in qcAnalysisStatusConfig" :key="key" :label="key" :value="value"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="开启状态">
          <el-select
            v-model.trim="form.enableStatus"
            placeholder="请选择"
            size="mini"
            class="form-width"
            clearable>
            <el-option v-for="(key, value) in openStatus" :key="key" :label="key" :value="value"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="是否为备份">
          <el-select
            v-model.trim="form.isBackup"
            placeholder="请选择"
            size="mini"
            class="form-width"
            clearable>
            <el-option v-for="(key, value) in booleanOptions" :key="key" :label="key" :value="value"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="是否上门实验">
          <el-select
            v-model.trim="form.isDoorExperiment"
            placeholder="请选择"
            size="mini"
            class="form-width"
            clearable>
            <el-option v-for="(key, value) in booleanOptions" :key="key" :label="key" :value="value"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="是否达到交付标准">
          <el-select
            v-model.trim="form.isQualityAnalysis"
            placeholder="请选择"
            size="mini"
            class="form-width"
            clearable>
            <el-option v-for="(key, value) in booleanOptions" :key="key" :label="key" :value="value"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="生信交付时间">
          <el-date-picker
            v-model.trim="form.deliveryTime"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            size="mini"
            clearable
            :default-time="['00:00:00', '23:59:59']"
            :value-format="'yyyy-MM-dd HH:mm:ss'"
            class="form-long-width"/>
        </el-form-item>
      </el-form>
    </search-params-dialog>
    <div class="flex-wrapper">
      <div class="operate-btns-group">
        <el-button v-if="$setAuthority('024003002', 'buttons')" type="primary" size="mini" @click="handleCheck(0)">启动质控分析</el-button>
        <el-button v-if="$setAuthority('024003001', 'buttons')"  type="primary" size="mini" plain @click="handleCheck(1)">数据交付</el-button>
        <template v-if="$setAuthority('024003003', 'buttons')">
          <el-button v-if="downloadingExcelLoading" type="primary" size="small" disabled><i class="el-icon-loading"></i>
            正在导出
          </el-button>
          <el-dropdown v-else @command="handleCommand" style="margin: 0 10px;">
            <el-button type="primary" plain size="mini">执行表导出<i class="el-icon-arrow-down el-icon--right"></i></el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item :command="1">按条件导出</el-dropdown-item>
              <el-dropdown-item :command="2">按选中导出</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
<!--        <el-button type="primary" size="mini" plain @click="handleBatchModify">批量编辑</el-button>-->
        <el-button type="primary" plain size="mini" @click="handleSearch">查询</el-button>
        <el-button size="mini" @click="handleReset">重置</el-button>
        <el-badge :value="searchParamsKeyNum" :hidden="searchParamsKeyNum === 0" class="item" type="primary">
          <el-button size="mini" plain type="primary" @click="searchDialogVisible = true">更多查询</el-button>
        </el-badge>
      </div>

      <div class="setting-wrapper">
        <el-popover
          v-model.trim="visible"
          placement="bottom-start"
          title="自定义列"
          width="360"
          trigger="manual"
        >
          <div>
            <el-table
              ref="table"
              :data="tableData"
              border
              size="mini"
              height="300px"
              style="width: 100%"
              row-key="id"
              @select="handleSelectTable"
              @row-click="handleRowClick"
              @select-all="handleSelectAll"
            >
              <el-table-column type="index" width="50" show-overflow-tooltip>
                <template slot-scope="scope">
                  <icon-svg icon-class="icon-tuozhuai" class="handle"></icon-svg>
                </template>
              </el-table-column>
              <el-table-column type="selection" min-width="55" show-overflow-tooltip :selectable="checkSelectable"></el-table-column>
              <el-table-column
                prop="title"
                label="列名"
                key="title"
                min-width="200">
              </el-table-column>
            </el-table>
            <div class="operate-wrapper">
              <div class="operate-item" @click="handleResetTableConfig">恢复默认</div>
              <div class="operate-item" @click="handleCancel">关闭</div>
              <div class="operate-item" @click="handleSave">保存</div>
            </div>
          </div>
          <div slot="reference" @click="handleShowSetting">
            <el-card :body-style="{ padding: '5px'}" shadow="hover">
              <icon-svg style="font-size: 20px" icon-class="icon-shezhi"></icon-svg>
            </el-card>
          </div>
        </el-popover>
      </div>
    </div>

    <div class="content">
      <vxe-table
        ref="tableRef"
        border
        resizable
        :height="tbHeight"
        keep-source
        class="table"
        :data="tableList"
        size="mini"
        :auto-resize="true"
        :edit-rules="validRules"
        :valid-config="{msgMode: 'full'}"
        :checkbox-config="{trigger: 'row'}"
        :edit-config="{trigger: 'click', mode: 'cell',showStatus: true}"
        :scroll-y="{enabled: true}"
        @checkbox-all="handleSelectChange"
        @checkbox-change="handleSelectChange"
      >
        <vxe-column fixed="left" type="checkbox" width="50"></vxe-column>
        <vxe-table-column fixed="left" type="seq" title="序号" width="60"></vxe-table-column>
        <template v-for="(item, index) in tableConfig">
          <vxe-table-column v-if="item.isShow" :key="index" :fixed="item.fixed" :field="item.field" :formatter="item.formater"
                            :title="item.title"
                            :min-width="item.width" :edit-render="item.render">
            <template slot-scope="{ row }">
              <!-- 根据不同的字段类型渲染不同的单元格内容 -->
              <!-- 交付状态显示 -->
              <div v-if="item.field === 'deliverStatus'">
                <!-- 当状态值不存在或为0时显示占位符 -->
                <div v-if="row.deliverStatus !==0 && !row.deliverStatus">-</div>
                <!-- 根据状态值显示对应的样式类 -->
                <tooltips v-else :txt-info="row.deliverStatusText + ''" :class="getDeliverStatusClass(row.deliverStatus)"></tooltips>
              </div>

              <div v-else-if="item.field === 'warningFlag'">
                <el-tooltip :content="row.remark" :disabled="!row.realData.remark" placement="top" effect="dark">
                  <div :class="getWarningFlagClass(row.warningFlag)">{{row.warningFlagText}}</div>
                </el-tooltip>
                <!-- <tooltips :txt-info="row.warningFlagText + ''" :class="getWarningFlagClass(row.warningFlag)"></tooltips> -->
              </div>

              <!-- 质控状态显示 -->
              <div v-else-if="item.field === 'qcStatus'">
                <!-- 当状态值不存在或为0时显示占位符 -->
                <span v-if="row.qcStatus !==0 && !row.qcStatus">-</span>
                <!-- 根据质控状态设置不同的文字颜色:
                     0 - 灰色(未启动)
                     1 - 蓝色(已启动)
                     2 - 绿色(已完成) -->
                <tooltips v-else :txt-info="row.qcStatusText + ''" :class="getQcStatusClass(row.qcStatus)"></tooltips>
              </div>

              <!-- 订单编号显示(可点击) -->
              <!-- <div v-else-if="item.field === 'orderCode'">
                <span class="link" @click="handleToOrderDetail(row)">{{ row.orderCode }}</span>
              </div> -->
              <!-- 客户下单累计数据量/M 可点击弹窗 -->
              <div v-else-if="item.field === 'customerOrderDataTotal'">
                <span v-if="row.customerOrderDataTotal !==0 && !row.customerOrderDataTotal">-</span>
                <span v-else class="link" @click="handleShowDataDialog(row, 1, null, row.customerOrderDataTotal)">{{ row.customerOrderDataTotal }}</span>
              </div>
              <!-- 累积下机数据/M 可点击弹窗 -->
              <div v-else-if="item.field === 'experimentalLibraryDataTotal'">
                <span v-if="row.experimentalLibraryDataTotal !==0 && !row.experimentalLibraryDataTotal">-</span>
                <span v-else class="link" @click="handleShowDataDialog(row, 2, 0, row.experimentalLibraryDataTotal)">{{ row.experimentalLibraryDataTotal }}</span>
              </div>
              <!-- operation字段显示 -->
              <div v-else-if="item.field === 'operation'">
                <span class="link" @click="handleShowDataDialog(row, 3)">查看质控分析</span>
              </div>

              <div v-else>
                <div style="width: 100%">
                  <tooltips :txt-info="row[item.field] + ''"></tooltips>
                </div>
              </div>
            </template>
          </vxe-table-column>
        </template>
      </vxe-table>
      <div style="display: flex; align-items: center;font-size: 13px;">
        <el-pagination
          :page-sizes="pageSizes"
          :page-size="pageSize"
          :current-page.sync="currentPage"
          :total="totalPage"
          layout="total, sizes, prev, pager, next, jumper, slot"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange">
          <button @click="handleRefresh">
            <icon-svg icon-class="icon-refresh"/>
          </button>
        </el-pagination>
      </div>
    </div>
      <batch-modifying-dialog
        :pvisible.sync="batchModifyingDialogVisible"
        :ids="ids"
        @dialogConfirmEvent="getData"></batch-modifying-dialog>
      <error-dialog :pvisible.sync="errorDialogVisible"
      :error-list="errorList"
      :error-type-list="errorTypeList"
      :filter-gene-list="filterGeneList"
      @dialogConfirmEvent="getData"></error-dialog>
      <qc-error-dialog :pvisible.sync="qcErrorDialogVisible"
      :error-list="errorList"
      :error-type-list="errorTypeList"
      :filter-gene-list="filterGeneList"
      @dialogConfirmEvent="getData"></qc-error-dialog>
      <data-dialog :pvisible.sync="dataDialogVisible" :gene-num="geneNum" :total-amount="totalAmount"
      ></data-dialog>
      <sequencing-data-dialog :pvisible.sync="sequencingDataDialogVisible" :gene-num="geneNum"
      ></sequencing-data-dialog>
      <qc-analyse-dialog :pvisible.sync="qcAnalyseDialogVisible" :gene-num="geneNum" :lib-type="libType"/>
  </div>
</template>

<script>
import mixins from '../../../../util/mixins'
import {tableConfig} from './tabelConfig'
import util, {awaitWrap, deepCopy, downloadFile, readBlob} from '../../../../util/util'
import Sortable from 'sortablejs'
import IconSvg from '../../../common/iconSvg.vue'
import BatchModifyingDialog from './components/batchModifyingDialog.vue'
import errorDialog from './components/errorDialog.vue'
import QcErrorDialog from './components/qcErrorDialog.vue'
import DataDialog from './components/dataDialog.vue'
import qcAnalyseDialog from './components/qcAnalyseDialog.vue'
import {
  dataFormating,
  deliverConfig,
  qcAnalysisStatusConfig,
  openStatus,
  booleanOptions,
  exceptionTypeList
} from './dataFormate'
import {dataDelivery, checkQcAnalyse, startQcAnalysis, exportSampleMonitoring,
  getSampleMonitoringList} from '../../../../api/sequencingManagement/singleCell'
import SequencingDataDialog from './components/sequencingDataDialog.vue'
import constants from '../../../../util/constants'
export default {
  name: 'index',
  mixins: [mixins.tablePaginationCommonData],
  components: {BatchModifyingDialog, IconSvg, errorDialog, QcErrorDialog, DataDialog, SequencingDataDialog, qcAnalyseDialog},
  mounted () {
    this.$_setTbHeight(74 + 40 + 42 + 32, '.search-form')
    this.handleSearch()
  },
  data () {
    return {
      // 控制各种弹窗的显示状态
      visible: false, // 自定义列弹窗
      searchDialogVisible: false, // 搜索参数弹窗
      downloadingExcelLoading: false, // Excel导出加载状态
      errorDialogVisible: false, // 错误信息弹窗
      qcErrorDialogVisible: false, // 质控错误弹窗
      dataDialogVisible: false, // 数据详情弹窗
      sequencingDataDialogVisible: false, // 测序数据弹窗
      batchModifyingDialogVisible: false, // 批量修改弹窗
      qcAnalyseDialogVisible: false, // 质控分析弹窗

      // 彈窗參數
      errorTypeList: [],
      filterGeneList: [],
      geneNum: '',
      totalAmount: null,

      // 表格相关配置
      ids: [], // 选中行的ID集合
      libType: null, // 文库类型
      deliverStatus: null, // 交付状态
      orderCode: null, // 订单编号
      orderType: null, // 订单类型
      productName: null, // 产品名称
      errorList: [], // 错误信息列表
      deliverConfig: deliverConfig,
      qcAnalysisStatusConfig: qcAnalysisStatusConfig,
      openStatus: openStatus,
      booleanOptions: booleanOptions,
      exceptionTypeList: exceptionTypeList,
      EXCEPT_OPTION_LIST: ['无', ...constants.EXCEPT_OPTION_LIST],

      // 表单数据
      form: {}, // 当前表单数据
      formSubmit: {}, // 提交的表单数据

      // 分页配置
      pageSizes: [100, 200, 500], // 每页显示条数选项
      pageSize: 100, // 当前每页显示条数

      // 表格配置
      tableConfig: JSON.parse(localStorage.getItem('singleSampleModifyTableConfig')) || tableConfig,
      tableData: [], // 表格配置数据
      tableList: [], // 表格显示数据
      validRules: {} // 表单验证规则
    }
  },
  methods: {
    /**
     * 设置查询参数。
     * 此函数用于根据表单提交的信息，生成并返回一个包含各种查询条件的对象。
     * 这些条件包括时间范围、项目信息、样本信息、订单信息等，用于精确查询项目进度和状态。
     *
     * @returns {Object} 返回一个对象，其中包含了所有的查询参数。
     */
    setParams () {
      const formSubmit = this.formSubmit
      // 初始化时间范围数组为空，如果存在则使用之
      const timeRanges = {
        fconfirmTime: formSubmit.time || [],
        fdeliveryTime: formSubmit.deliveryTime || []
      }
      // 构建并返回查询参数对象
      return {
        fcosSampleNameList: util.setGroupData(formSubmit.cosSampleName, '、', false),
        fgeneNumList: util.setGroupData(formSubmit.geneNum, '、', false),
        fprojectCode: formSubmit.projectCode,
        fproductionNumber: formSubmit.productionNumber,
        fproductName: formSubmit.productName,
        flibNum: formSubmit.libNum,
        fcosSubCode: formSubmit.subOrderNo,
        fprojectName: formSubmit.projectName,
        fisStaging: formSubmit.enableStatus,
        fisBackUp: formSubmit.isBackup,
        fisSiteExperiment: formSubmit.isDoorExperiment,
        fisEmbarkation: formSubmit.isSequenced,
        fqualityAnalysisStatus: formSubmit.qcAnalysisStatus,
        fisQualityAnalysis: formSubmit.isQualityAnalysis,
        fisQualityStandard: formSubmit.isQualityStandard,
        fdeliverStatus: formSubmit.deliveryStatus,
        fexceptionRemark: formSubmit.exceptionType,
        fconfirmException: formSubmit.fconfirmException,
        // 处理时间范围
        ...Object.entries(timeRanges).reduce((acc, [key, value]) => {
          const [start, end] = value
          if (key === 'fconfirmTime') {
            return {...acc, fconfirmTimeStart: start, fconfirmTimeEnd: end}
          }
          if (key === 'fdeliveryTime') {
            return {...acc, fdeliverTimeStart: start, fdeliverTimeEnd: end}
          }
          return acc
        }, {})
      }
    },
    checkSelectable (row) {
      return !['交付状态', '异常标记', '质控分析状态', '样本原始名称', '生产序号', '吉因加编号', '文库编号'].includes(row.title)
    },
    // 点击行
    handleRowClick (row, c) {
      this.handleSelectTable(undefined, row)
    },
    // 选中行
    handleSelectTable (selection, row) {
      if (!this.checkSelectable(row)) return
      if (this.pin) {
        this.shiftSelect(row)
      } else {
        this.startPoint = undefined// 清空多选起点
        this.endPoint = undefined// 清空多选终点
        this.selectedRows.has(row.id)
          ? this.selectedRows.delete(row.id)
          : this.selectedRows.set(row.id, row)
      }
      this.handleEchoSelect()
    },
    // 全选
    handleSelectAll (selection) {
      this.handleDelCurrentDataMap()
      selection.forEach((row) => {
        this.selectedRows.set(row.id, row)
      })
      this.selectedRowsSize = this.selectedRows.size
    },
    // 查看 type 1编辑 2 只读
    handleToOrderDetail (row) {
      console.log(row.orderId, row.orderCode, row.orderType)
      this.$store.commit({
        type: 'old/setValue',
        category: 'libraryOperatingData',
        libraryOperatingData: {
          type: 2, // type 1编辑 2 只读
          orderId: row.orderId,
          status: 2,
          code: row.orderCode,
          name: 'lims'
        }
      })
      let path = ''
      if (row.orderType === 1) path = '/business/subpage/technologyService/entryIlluminaLibraryOrder'
      if (row.orderType === 2) path = '/business/subpage/technologyService/entryMGILibraryOrder'
      if (row.orderType === 3) path = '/business/subpage/technologyService/entryTissueOrder'
      if (row.orderType === 5) path = '/business/subpage/technologyService/singleCell'
      if (path) util.openNewPage(path)
    },
    /**
     * 处理搜索操作。
     * 重置当前页码并调用获取数据的方法，以便加载新搜索结果。
     */
    handleSearch () {
      // 深拷贝表单提交的数据，确保不直接修改原数据
      this.formSubmit = deepCopy(this.form)
      this.currentPage = 1
      this.getData()
    },
    /**
     * 处理重置操作。
     * 将表单数据重置为初始状态，并调用搜索方法以刷新列表。
     */
    handleReset () {
      this.form = this.$options.data().form
      this.handleSearch()
    },
    /**
     * 异步获取样本监控列表的数据。
     *
     * 使用设置的参数进行请求，并对请求结果进行处理，将处理后的数据赋值给列表。
     * 使用awaitWrap封装请求以处理加载状态。
     */
    async getData () {
      const param = this.setParams()
      console.log(param, '2222')

      const params = {
        ...param, // 分页信息
        pageVO: {
          currentPage: this.currentPage,
          pageSize: this.pageSize
        }
      }
      // this.tableList = dataFormating([])
      const {res} = await awaitWrap(getSampleMonitoringList(params, {
        loadingDom: '.table'
      }))
      if (res && res.code === this.SUCCESS_CODE) {
        this.totalPage = res.data.total
        this.tableList = dataFormating(res.data.records)
      }
    },
    setUpdateParams (field, cellValue, row) {
      // 使用映射代替 if-else 逻辑，提高代码可读性
      const paramMapper = {
        'deliveryProblem': {fdeliveryProblem: cellValue},
        'dataNote': {fdataNote: cellValue}
      }
      const params = {
        fidList: [row.fid]
      }
      // 使用映射函数根据 this.form.type 的值来添加特定的属性
      return Object.assign(params, {...paramMapper[field]})
    },
    /**
     * 处理质控分析启动
     * 1. 验证是否有选中的样本
     * 2. 调用质控分析接口
     * 3. 处理返回结果和错误信息
     */
    async handleCheck (type) {
      // 获取选中的样本记录
      const selectedRecords = this.$refs.tableRef.getCheckboxRecords()
      if (selectedRecords.length === 0) {
        this.$message.error('请至少选择一条样本')
        return
      }
      if (type === 0) {
        await this.$confirm(`已勾选<span style="color: red;">${selectedRecords.length}</span>条数据, 启动分析后不可撤销, 请确认继续启动吗？`, '启动质控分析', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          dangerouslyUseHTMLString: true,
          type: 'warning'
        })
      }
      if (type === 1) {
        await this.$confirm(`已勾选<span style="color: red;">${selectedRecords.length}</span>条数据, 请进行数据交付？`, '数据交付', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          dangerouslyUseHTMLString: true,
          type: 'warning'
        })
      }

      // 调用启动质控分析接口
      const params = {
        fgeneNumList: selectedRecords.map(record => record.geneCode),
        fcondition: type
      }
      // 判斷是否滿足启动分析條件
      const {res} = await awaitWrap(checkQcAnalyse(params))
      if (res.code === this.SUCCESS_CODE) {
        if (res.data.errorDataList.length > 0) {
          this.handleOpenErrorDialog(type, res.data)
        } else {
          type === 0 ? this.handleStart(params) : this.handleDataDelivery(params)
        }
      }
    },
    async handleStart (params) {
      const {res} = await awaitWrap(startQcAnalysis(params))
      if (res.code === this.SUCCESS_CODE) {
        this.$message.success('启动质控分析成功')
        await this.getData() // 刷新表格数据
      }
    },
    /**
     * 处理数据交付
     * 1. 验证是否有选中的样本
     * 2. 调用数据交付接口
     * 3. 处理返回结果和错误信息
     */
    async handleDataDelivery (params) {
      const {res} = await awaitWrap(dataDelivery(params))
      if (res.code === this.SUCCESS_CODE) {
        this.$message.success('操作成功！')
        await this.getData() // 刷新表格数据
      }
    },
    // 打开错误弹窗
    handleOpenErrorDialog (type = 1, data) {
      const errorList = data.errorDataList || []
      this.filterGeneList = data.filterGeneList || []
      type === 0 ? this.qcErrorDialogVisible = true : this.errorDialogVisible = true
      this.errorTypeList = errorList.map(v => v.errorType)
      this.errorList = errorList
    },
    handleCommand (command) {
      command === 1 ? this.handleExportAll() : this.handleExport()
    },
    async downloadExport (res) {
      if (res) {
        const {err} = await awaitWrap(readBlob(res.data))
        err ? this.$message.error(err) : downloadFile(res)
      }
      this.downloadLoading = false
    },
    // 按条件导出
    async handleExportAll () {
      const params = this.setParams()
      await this.$confirm('是否确认导出查询数据？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      this.downloadLoading = true
      const {res} = await awaitWrap(exportSampleMonitoring(params))
      await this.downloadExport(res)
    },
    // 导出所选
    async handleExport () {
      let selectRecords = this.$refs.tableRef.getCheckboxRecords()
      if (selectRecords.length === 0) {
        this.$message.error('请选择数据')
        return
      }
      let rowsId = selectRecords.map(item => item.fid)
      await this.$confirm('否确认导出选中数据？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
      this.downloadLoading = true
      const {res} = await awaitWrap(exportSampleMonitoring({
        fcosQualityMonitorIdList: rowsId
      }))
      await this.downloadExport(res)
    },
    handleBatchModify () {
      // 获取选中的样本记录
      const selectedRecords = this.$refs.tableRef.getCheckboxRecords()
      if (selectedRecords.length === 0) {
        this.$message.error('请选择需要数据交付的样本')
        return
      }
      this.ids = selectedRecords.map(item => item.fid)
      this.batchModifyingDialogVisible = true
    },
    // 开启弹窗
    handleShowDataDialog (row, type, libType, data = null) {
      // 使用对象映射定义不同类型的弹窗数据
      const visibleMap = {
        1: 'dataDialogVisible',
        2: 'sequencingDataDialogVisible',
        3: 'qcAnalyseDialogVisible'
      }
      this.geneNum = row.geneCode
      this.totalAmount = data
      this.libType = libType
      this[visibleMap[type]] = true
    },
    handleSelectChange () {
      let selectRecords = this.$refs.tableRef.getCheckboxRecords()
      this.selectedRowsSize = selectRecords.length
    },
    // 恢复默认表格配置
    handleResetTableConfig () {
      this.tableConfig = tableConfig
      localStorage.setItem('singleSampleModifyTableConfig', JSON.stringify(this.tableConfig))
      this.visible = false
    },
    // 拖拽排序
    async initSort () {
      await this.$nextTick()
      const el = document.querySelectorAll('.el-table__body-wrapper > table > tbody')[0]
      // 根据具体需求配置options配置项
      Sortable.create(el, {
        handle: '.handle', // handle's class
        onEnd: (evt) => { // 监听拖动结束事件
          try {
            // 交换元素的逻辑，避免直接使用splice，以提高性能
            const temp = this.tableData[evt.oldIndex]
            this.tableData[evt.oldIndex] = this.tableData[evt.newIndex]
            this.tableData[evt.newIndex] = temp
          } catch (error) {
            console.error('Error during sorting:', error)
            // 可以进一步处理异常，例如回滚操作、显示错误信息等
          }
        }
      })
    },
    // 保存表格配置
    handleSave () {
      this.tableConfig = this.tableData.map(item => {
        if (item.isCustomerField) {
          item.isShow = !!this.selectedRows.has(item.id)
        }
        return item
      })
      localStorage.setItem('singleSampleModifyTableConfig', JSON.stringify(this.tableConfig))
      this.visible = false
    },
    // 取消表格配置
    handleCancel () {
      this.visible = false
    },
    // 显示表格配置
    handleShowSetting () {
      this.initSort()
      this.visible = !this.visible
      // 回显选中的列
      this.tableData = this.tableConfig.filter(item => item.isCustomerField)
      this.tableData.forEach(item => {
        if (item.isShow) {
          this.selectedRows.set(item.id, item)
        }
      })
      this.handleEchoSelect()
    },
    /**
     * 获取交付状态对应的样式类名
     * @param {number} deliverStatus - 交付状态码
     * @returns {string} 对应的CSS类名
     * 0: 待交付 - 灰色
     * 1: 部分交付 - 蓝色
     * 2: 已交付 - 绿色
     */
    getDeliverStatusClass (deliverStatus) {
      const statusClassMap = {
        0: 'deliver-status--undelivered', // 待交付
        1: 'deliver-status--assigned', // 部分交付
        2: 'deliver-status--delivered' // 已交付
      }
      return statusClassMap[deliverStatus]
    },
    /**
     * 获取警告标记对应的样式类名
     * @param {number} type - 警告类型
     * @returns {string} 对应的CSS类名
     * 1: 暂停 - 橙色
     * 2-5: 各类终止状态 - 红色
     */
    getWarningFlagClass (type) {
      const warningClassMap = {
        0: 'warning-flag--pause', // 暂停
        1: 'warning-flag--terminate-pre', // 终止-解离前
        2: 'warning-flag--terminate-post', // 终止-解离后
        3: 'warning-flag--terminate-lib', // 终止-建库后
        4: 'warning-flag--terminate-seq' // 终止-上机后
      }
      return warningClassMap[type]
    },
    /**
     * 获取质控状态对应的样式类名
     * @param {number} status - 质控状态码
     * @returns {string} 对应的CSS类名
     * 0: 未启动 - 灰色
     * 1: 已启动 - 蓝色
     * 2: 已完成 - 绿色
     */
    getQcStatusClass (status) {
      const statusClassMap = {
        2: 'qc-status--not-started', // 未启动
        0: 'qc-status--in-progress', // 已启动
        1: 'qc-status--completed' // 已完成
      }
      return statusClassMap[status]
    }
  }
}
</script>

<style scoped lang="scss">
.wrapper {
  width: 100%;

  .btn-group {
    margin-bottom: 10px;
  }
}

.flex-wrapper {
  display: flex;
  justify-content: space-between;
}

.setting-wrapper {
  height: 32px;
}

.operate-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  text-align: center;
  margin-top: 12px;

  .operate-item {
    flex: 1;
    cursor: pointer;
  }

  .operate-item:hover {
    color: #409EFF;
  }
}

.time-tips-item {
  border-bottom: 1px solid #ebeef5;
  padding: 5px 0;
}

.time-tips-item:last-child {
  border-bottom: none;
}

.table-item {
  width: 100%;
  // 单行省略
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
/deep/ .vxe-body--column {
  padding: 0 !important;
}

.deliver-status {
  &--undelivered { color: #747474; }  // 待交付 - 灰色
  &--assigned { color: #409EFF; }     // 部分交付 - 蓝色
  &--delivered { color: #51C86B; }    // 已交付 - 绿色
}

// 设置表格行高
/deep/ .vxe-body--row {
  height: 32px !important;  // 设置行高为32px

  // 确保单元格内容垂直居中
  .vxe-cell {
    line-height: 32px;
  }
}

// 设置表头行高
/deep/ .vxe-header--row {
  height: 32px !important;

  .vxe-cell {
    line-height: 32px;
  }
}

// 确保单元格内容不会被截断
/deep/ .vxe-cell {
  padding: 0 8px;  // 设置单元格左右内边距
}

// 如果有特殊列需要不同的行高,可以单独设置
/deep/ .vxe-body--column.custom-column {
  .vxe-cell {
    height: auto;      // 自适应高度
    line-height: 1.5;  // 更舒适的行间距
    padding: 4px 8px;  // 上下添加内边距
  }
}

.warning-flag {
  &--pause { color: #E6A23C; }        // 暂停 - 橙色
  // 所有终止状态使用相同的红色
  &--terminate {
    &-pre,
    &-post,
    &-lib,
    &-seq { color: #F56C6C; }         // 终止状态 - 红色
  }
}

// 添加鼠标悬停效果

.qc-status {
  &--not-started { color: #747474; }  // 未启动 - 灰色
  &--in-progress { color: #409EFF; }  // 已启动 - 蓝色
  &--completed { color: #51C86B; }    // 已完成 - 绿色
}
</style>
