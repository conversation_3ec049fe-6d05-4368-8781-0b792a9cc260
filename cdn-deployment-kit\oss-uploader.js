/**
 * CDN部署工具包 - 阿里云OSS上传器
 * 实现构建产物自动上传到阿里云OSS
 */

const fs = require('fs')
const path = require('path')
const crypto = require('crypto')
const zlib = require('zlib')

// 首先尝试加载.env文件
try {
  const dotenv = require('dotenv')
  const envPath = path.join(__dirname, '.env')
  if (require('fs').existsSync(envPath)) {
    dotenv.config({ path: envPath })
    console.log('✅ 已加载.env配置文件')
  }
} catch (error) {
  // dotenv不是必需的，忽略错误
}

// 导入CDN配置
const { getConfig, displayConfig } = require('./cdn-config')

// 导入统一日志系统
const { logger } = require('./logger')

// 创建带上下文的日志器
const log = logger.child('oss-uploader')

/**
 * 阿里云OSS上传器
 */
class OSSUploader {
  constructor (options = {}) {
    // 如果在cdn-deployment-kit目录中运行，使用父目录作为项目根目录
    const currentDir = process.cwd()
    const isInCDNKit = currentDir.endsWith('cdn-deployment-kit')
    this.projectRoot = options.projectRoot || (isInCDNKit ? path.dirname(currentDir) : currentDir)
    this.distPath = path.join(this.projectRoot, 'dist')

    // 使用传递的 CDN 配置或加载新配置
    if (options.cdnConfig) {
      this.cdnConfig = options.cdnConfig
      log.config('使用传递的CDN配置:')
      displayConfig(this.cdnConfig)
    } else {
      const environment = options.environment || process.env.NODE_ENV || 'test'
      const provider = options.provider || options.target || 'aliOSS'

      try {
        this.cdnConfig = getConfig(environment, provider)
        log.config('加载CDN配置:')
        displayConfig(this.cdnConfig)
      } catch (error) {
        log.warn(`CDN配置加载失败: ${error.message}`)
        log.info('将使用模拟上传模式')
        this.cdnConfig = null
      }
    }

    // 配置合并：优先使用 CDN 配置，然后是选项，最后是环境变量
    this.config = {
      accessKeyId: this.cdnConfig?.provider?.accessKeyId || options.accessKeyId || process.env.OSS_ACCESS_KEY_ID,
      accessKeySecret: this.cdnConfig?.provider?.accessKeySecret || options.accessKeySecret || process.env.OSS_ACCESS_KEY_SECRET,
      bucket: this.cdnConfig?.provider?.bucket || options.bucket || process.env.OSS_BUCKET || 'genereadonly',
      region: this.cdnConfig?.provider?.region || options.region || process.env.OSS_REGION || 'oss-cn-beijing',
      prefix: this.cdnConfig?.basePath || options.prefix || 'static/',
      enableGzip: this.cdnConfig?.enableGzip ?? (options.enableGzip !== false),
      enableCache: this.cdnConfig?.enableCache ?? (options.enableCache !== false),
      simulateMode: process.env.OSS_SIMULATE === 'true' || options.simulate === true
    }

    log.debug(`使用配置:`)
    log.debug(`  Bucket: ${this.config.bucket}`)
    log.debug(`  Region: ${this.config.region}`)
    log.debug(`  Prefix: ${this.config.prefix}`)
    log.debug(`  模拟模式: ${this.config.simulateMode}`)
    log.debug(`  Gzip: ${this.config.enableGzip}`)
    log.debug(`  缓存: ${this.config.enableCache}`)

    this.uploadResults = {
      timestamp: new Date().toISOString(),
      totalFiles: 0,
      uploadedFiles: 0,
      failedFiles: 0,
      totalSize: 0,
      compressedSize: 0,
      files: []
    }
  }

  /**
   * 执行完整的CDN上传流程
   */
  async uploadToCDN () {
    log.upload('开始CDN上传流程...')
    log.plain('')

    try {
      // 1. 验证配置
      this.validateConfig()

      // 2. 检查构建输出
      await this.checkBuildOutput()

      // 3. 分析文件
      const files = await this.analyzeFiles()

      // 4. 上传文件
      await this.uploadFiles(files)

      // 5. 生成上传报告
      await this.generateUploadReport()

      log.plain('')
      log.complete('CDN上传完成！')
      this.displayUploadSummary()

      // 构建 CDN URL
      const cdnUrl = this.buildCdnUrl()

      return {
        ...this.uploadResults,
        cdnUrl: cdnUrl
      }
    } catch (error) {
      log.plain('')
      log.error('CDN上传失败:', error.message)
      throw error
    }
  }

  /**
   * 验证OSS配置
   */
  validateConfig () {
    log.config('验证OSS配置...')

    // 优先使用CDN配置
    if (this.cdnConfig && this.cdnConfig.provider) {
      const provider = this.cdnConfig.provider

      // 检查是否有有效的访问密钥
      if (provider.accessKeyId && provider.accessKeySecret && provider.bucket) {
        log.success('CDN配置验证通过')
        log.debug(`环境: ${this.cdnConfig.environment}`)
        log.debug(`Bucket: ${provider.bucket}`)
        log.debug(`Region: ${provider.region}`)
        log.debug(`域名: ${provider.domain}`)
        log.debug(`基础路径: ${this.cdnConfig.basePath}`)

        // 更新config以使用CDN配置
        this.config.accessKeyId = provider.accessKeyId
        this.config.accessKeySecret = provider.accessKeySecret
        this.config.bucket = provider.bucket
        this.config.region = provider.region
        this.config.prefix = this.cdnConfig.basePath + '/'
        this.config.enableGzip = this.cdnConfig.enableGzip
        this.config.simulateMode = false
        return
      }
    }

    // 回退到传统配置检查
    if (!this.config.accessKeyId || !this.config.accessKeySecret || !this.config.bucket) {
      log.warn('OSS配置缺失，将使用模拟上传模式')
      log.info('要启用真实上传，请设置以下环境变量:')
      log.debug('   export OSS_ACCESS_KEY_ID="your-access-key-id"')
      log.debug('   export OSS_ACCESS_KEY_SECRET="your-access-key-secret"')
      log.debug('   export OSS_BUCKET="your-bucket-name"')
      log.debug('   export OSS_REGION="oss-cn-beijing"')
      this.config.simulateMode = true
    } else {
      log.success('传统OSS配置验证通过')
      log.debug(`Bucket: ${this.config.bucket}`)
      log.debug(`Region: ${this.config.region}`)
      log.debug(`Prefix: ${this.config.prefix}`)
      this.config.simulateMode = false
    }
  }

  /**
   * 检查构建输出
   */
  async checkBuildOutput () {
    log.progress('检查构建输出...')

    if (!fs.existsSync(this.distPath)) {
      throw new Error(`构建输出目录不存在: ${this.distPath}`)
    }

    const staticPath = path.join(this.distPath, 'static')
    if (!fs.existsSync(staticPath)) {
      throw new Error(`静态资源目录不存在: ${staticPath}`)
    }

    log.success('构建输出检查通过')
  }

  /**
   * 分析待上传文件
   */
  async analyzeFiles () {
    log.analyze('分析待上传文件...')

    const files = []

    // 递归扫描文件
    const scanDirectory = (dirPath, relativePath = '', isStaticDir = false) => {
      const items = fs.readdirSync(dirPath)

      for (const item of items) {
        const itemPath = path.join(dirPath, item)
        const itemRelativePath = path.join(relativePath, item).replace(/\\/g, '/')
        const stats = fs.statSync(itemPath)

        if (stats.isDirectory()) {
          scanDirectory(itemPath, itemRelativePath, isStaticDir)
        } else {
          // 构建 OSS 键名
          let ossKey
          if (isStaticDir) {
            // static 目录下的文件，保持 static/ 前缀
            ossKey = this.config.prefix + 'static/' + itemRelativePath
          } else {
            // 根目录文件，直接放在根目录
            ossKey = this.config.prefix + itemRelativePath
          }

          const fileInfo = {
            localPath: itemPath,
            relativePath: itemRelativePath,
            ossKey: ossKey,
            size: stats.size,
            mtime: stats.mtime,
            contentType: this.cdnConfig ? this.cdnConfig.getContentType(itemPath) : this.getContentType(itemPath),
            needsGzip: this.shouldGzip(itemPath)
          }

          files.push(fileInfo)
          this.uploadResults.totalSize += stats.size
        }
      }
    }

    // 扫描 static 目录
    const staticPath = path.join(this.distPath, 'static')
    if (fs.existsSync(staticPath)) {
      scanDirectory(staticPath, '', true)
    }

    // 扫描根目录文件（如 config.json, icon.jpg 等）
    const distItems = fs.readdirSync(this.distPath)
    for (const item of distItems) {
      const itemPath = path.join(this.distPath, item)
      const stats = fs.statSync(itemPath)

      // 跳过目录和 index.html
      if (!stats.isDirectory() && item !== 'index.html') {
        const fileInfo = {
          localPath: itemPath,
          relativePath: item,
          ossKey: this.config.prefix + item,
          size: stats.size,
          mtime: stats.mtime,
          contentType: this.cdnConfig ? this.cdnConfig.getContentType(itemPath) : this.getContentType(itemPath),
          needsGzip: this.shouldGzip(itemPath)
        }

        files.push(fileInfo)
        this.uploadResults.totalSize += stats.size
      }
    }

    this.uploadResults.totalFiles = files.length

    log.success('文件分析完成')
    log.debug(`总文件数: ${files.length}`)
    log.size(`总大小: ${(this.uploadResults.totalSize / 1024 / 1024).toFixed(2)} MB`)

    return files
  }

  /**
   * 上传文件到OSS
   */
  async uploadFiles (files) {
    log.upload('开始上传文件...')

    // 检查是否使用模拟模式或ali-oss依赖
    if (this.config.simulateMode) {
      return this.simulateUpload(files)
    }

    let OSS
    try {
      OSS = require('ali-oss')
    } catch (error) {
      log.warn('ali-oss依赖缺失，使用模拟上传...')
      return this.simulateUpload(files)
    }

    // 创建OSS客户端
    const client = new OSS({
      accessKeyId: this.config.accessKeyId,
      accessKeySecret: this.config.accessKeySecret,
      bucket: this.config.bucket,
      region: this.config.region
    })

    // 并发上传控制
    const concurrency = 5
    const chunks = this.chunkArray(files, concurrency)

    for (let i = 0; i < chunks.length; i++) {
      const chunk = chunks[i]
      log.progress(`上传批次 ${i + 1}/${chunks.length} (${chunk.length} 文件)`)

      const uploadPromises = chunk.map(file => this.uploadSingleFile(client, file))
      await Promise.all(uploadPromises)
    }

    log.success('文件上传完成')
  }

  /**
   * 上传单个文件
   */
  async uploadSingleFile (client, fileInfo) {
    try {
      let fileBuffer = fs.readFileSync(fileInfo.localPath)
      let headers = {
        'Content-Type': fileInfo.contentType
      }

      // 启用缓存 - 优先使用CDN配置
      if (this.config.enableCache) {
        if (this.cdnConfig) {
          headers['Cache-Control'] = this.cdnConfig.cacheControl
        } else {
          headers['Cache-Control'] = this.getCacheControl(fileInfo.relativePath)
        }
      }

      // Gzip压缩
      if (this.config.enableGzip && fileInfo.needsGzip) {
        fileBuffer = zlib.gzipSync(fileBuffer)
        headers['Content-Encoding'] = 'gzip'
        this.uploadResults.compressedSize += fileBuffer.length
      } else {
        this.uploadResults.compressedSize += fileInfo.size
      }

      // 上传到OSS
      const result = await client.put(fileInfo.ossKey, fileBuffer, {
        headers
      })

      this.uploadResults.uploadedFiles++
      this.uploadResults.files.push({
        ...fileInfo,
        uploaded: true,
        url: result.url,
        etag: result.etag
      })

      log.debug(`✅ ${fileInfo.relativePath}`)
    } catch (error) {
      this.uploadResults.failedFiles++
      this.uploadResults.files.push({
        ...fileInfo,
        uploaded: false,
        error: error.message
      })

      log.error(`❌ ${fileInfo.relativePath}: ${error.message}`)
    }
  }

  /**
   * 模拟上传（用于测试）
   */
  async simulateUpload (files) {
    log.info('模拟上传模式...')

    for (const file of files) {
      // 模拟上传延迟
      await new Promise(resolve => setTimeout(resolve, 50))

      this.uploadResults.uploadedFiles++
      this.uploadResults.files.push({
        ...file,
        uploaded: true,
        url: `https://${this.config.bucket || 'demo-bucket'}.${this.config.region}.aliyuncs.com/${file.ossKey}`,
        etag: this.generateMockEtag(file.localPath)
      })

      log.debug(`✅ ${file.relativePath} (模拟)`)
    }

    this.uploadResults.compressedSize = this.uploadResults.totalSize
    log.success('模拟上传完成')
  }

  /**
   * 获取文件Content-Type
   */
  getContentType (filePath) {
    const ext = path.extname(filePath).toLowerCase()
    const contentTypes = {
      '.js': 'application/javascript',
      '.css': 'text/css',
      '.html': 'text/html',
      '.png': 'image/png',
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.gif': 'image/gif',
      '.svg': 'image/svg+xml',
      '.ico': 'image/x-icon',
      '.woff': 'font/woff',
      '.woff2': 'font/woff2',
      '.ttf': 'font/ttf',
      '.eot': 'application/vnd.ms-fontobject'
    }
    return contentTypes[ext] || 'application/octet-stream'
  }

  /**
   * 判断是否需要Gzip压缩
   */
  shouldGzip (filePath) {
    const ext = path.extname(filePath).toLowerCase()
    const gzipExtensions = ['.js', '.css', '.html', '.json', '.xml', '.svg']
    return gzipExtensions.includes(ext)
  }

  /**
   * 获取缓存控制策略
   */
  getCacheControl (relativePath) {
    // 带hash的文件长期缓存
    if (/\.[a-f0-9]{8,}\.(js|css)$/.test(relativePath)) {
      return 'public, max-age=31536000' // 1年
    }

    // 其他静态资源短期缓存
    return 'public, max-age=86400' // 1天
  }

  /**
   * 数组分块
   */
  chunkArray (array, chunkSize) {
    const chunks = []
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize))
    }
    return chunks
  }

  /**
   * 生成模拟ETag
   */
  generateMockEtag (filePath) {
    const content = fs.readFileSync(filePath)
    return crypto.createHash('md5').update(content).digest('hex')
  }

  /**
   * 构建 CDN URL
   */
  buildCdnUrl () {
    try {
      // 优先使用 CDN 配置中的 domain
      if (this.cdnConfig && this.cdnConfig.provider && this.cdnConfig.provider.domain) {
        const domain = this.cdnConfig.provider.domain
        const basePath = this.cdnConfig.basePath || ''

        // 确保 URL 格式正确
        let cdnUrl = domain.endsWith('/') ? domain : domain + '/'
        if (basePath) {
          cdnUrl += basePath.startsWith('/') ? basePath.substring(1) : basePath
          cdnUrl = cdnUrl.endsWith('/') ? cdnUrl : cdnUrl + '/'
        }

        log.debug(`构建 CDN URL: ${cdnUrl}`)
        return cdnUrl
      }

      // 回退到基于 bucket 和 region 构建 URL
      if (this.config.bucket && this.config.region) {
        const cdnUrl = `https://${this.config.bucket}.${this.config.region}.aliyuncs.com/`
        log.debug(`使用默认 CDN URL: ${cdnUrl}`)
        return cdnUrl
      }

      log.warn('无法构建 CDN URL，缺少必要配置')
      return null
    } catch (error) {
      log.error('构建 CDN URL 失败:', error.message)
      return null
    }
  }

  /**
   * 生成上传报告
   */
  async generateUploadReport () {
    const reportPath = path.join(this.projectRoot, 'cdn-upload-report.json')

    const report = {
      ...this.uploadResults,
      config: {
        bucket: this.config.bucket,
        region: this.config.region,
        prefix: this.config.prefix,
        simulateMode: this.config.simulateMode
      },
      generatedAt: new Date().toISOString()
    }

    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2))
    log.file(`上传报告已保存: ${reportPath}`)
  }

  /**
   * 显示上传摘要
   */
  displayUploadSummary () {
    log.plain('')
    log.analyze('CDN上传摘要')
    log.separator('=', 50)
    log.file(`总文件数: ${this.uploadResults.totalFiles}`)
    log.success(`成功上传: ${this.uploadResults.uploadedFiles}`)
    log.error(`上传失败: ${this.uploadResults.failedFiles}`)
    log.size(`原始大小: ${(this.uploadResults.totalSize / 1024 / 1024).toFixed(2)} MB`)
    log.size(`压缩后大小: ${(this.uploadResults.compressedSize / 1024 / 1024).toFixed(2)} MB`)

    const compressionRatio = ((this.uploadResults.totalSize - this.uploadResults.compressedSize) / this.uploadResults.totalSize * 100).toFixed(1)
    if (compressionRatio > 0) {
      log.performance(`压缩率: ${compressionRatio}%`)
    }

    // 显示CDN地址
    if (this.cdnConfig && this.cdnConfig.provider.domain) {
      log.deploy(`CDN地址: ${this.cdnConfig.getCDNUrl()}`)
    } else {
      const bucketName = this.config.bucket || 'demo-bucket'
      log.deploy(`CDN地址: https://${bucketName}.${this.config.region}.aliyuncs.com/${this.config.prefix}`)
    }

    if (this.config.simulateMode) {
      log.plain('')
      log.info('提示: 当前为模拟上传模式')
      log.info('要启用真实上传，请配置以下环境变量:')
      log.debug('   export OSS_ACCESS_KEY_ID="your-access-key-id"')
      log.debug('   export OSS_ACCESS_KEY_SECRET="your-access-key-secret"')
      log.debug('   export OSS_BUCKET="your-bucket-name"')
      log.debug('   export OSS_REGION="oss-cn-beijing"')
    } else if (this.cdnConfig) {
      log.plain('')
      log.success('使用真实CDN上传')
      log.debug(`环境: ${this.cdnConfig.environment}`)
      log.debug(`项目: ${this.cdnConfig.projectName}`)
      log.debug(`版本: ${this.cdnConfig.version}`)
    }
  }
}

module.exports = {
  OSSUploader
}
