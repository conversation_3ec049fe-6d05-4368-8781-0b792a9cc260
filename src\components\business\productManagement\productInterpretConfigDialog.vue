<template>
  <div>
    <el-dialog :title="title" :visible.sync="visible" :close-on-click-modal="false" :modal-append-to-body='false'
      :before-close="handleClose" width="800px" append-to-body @opened="handleOpen">
      <el-form ref="form" :model="form" :rules="rules" size="mini" class="form" label-position="top"
        label-width="120px">
        <el-tabs v-model.trim="activeName">
          <!--       解读规则 多产品      -->
          <el-tab-pane v-if="!isSample" label="解读规则" name="解读规则">
            <div class="wrapper">
              <div class="tips"><i class="el-icon-warning" style="color: #608dfa"></i>
                该产品为组合产品，请选择相关产品进行配置，实验配置均由子产品来决定，子产品信息如下:
              </div>
              <el-form-item label="组合产品：">
                <el-table :data="tableData">
                  <el-table-column prop="productCode" label="产品编号"></el-table-column>
                  <el-table-column prop="productName" label="产品名称"></el-table-column>
                  <el-table-column prop="readProcess" label="解读流程"></el-table-column>
                  <el-table-column prop="readTaskDistributionType" label="解读任务分类类型"></el-table-column>
                </el-table>
              </el-form-item>
              <el-row :gutter="40">
                <el-col :span="8">
                  <el-form-item label="组合报告类型：">
                    <!--待定-->
                    <!--                  <el-input  placeholder="文档待定"></el-input>-->
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="解读任务分类类型：" prop="readTaskDistributionType">
                    <el-select v-model.trim="form.readTaskDistributionType" clearable placeholder="请选择"
                      style="width: 100%">
                      <el-option :key="index" :label="item.dictValue" :value="item.dictCode"
                        v-for="(item, index) in taskType">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="16">
                  <el-form-item label="报告模版：" prop="reportCode">
                    <el-select v-model.trim="form.reportCode" clearable filterable placeholder="请选择"
                      style="width: 100%">
                      <el-option :key="index" :label="item.reportName" :value="item.reportCode"
                        v-for="(item, index) in templates">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="40">
                <el-col :span="8">
                  <el-form-item label="模板送检单位：" prop="isHideHospital">
                    <el-select v-model.trim="form.isHideHospital" clearable placeholder="请选择" style="width: 100%">
                      <el-option :key="item" :label="item" :value="index" v-for="(item, index) in ['显示', '不显示']">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="模板送检电话：" prop="isHidePhoneno">
                    <el-select v-model.trim="form.isHidePhoneno" clearable placeholder="请选择" style="width: 100%">
                      <el-option :key="item" :label="item" :value="index" v-for="(item, index) in ['显示', '不显示']">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="8">
                  <div style="color: deepskyblue;margin-top: 46px; cursor: pointer" @click="openCustomizeTemplates">
                    客户定制化模版配置
                    <i class="el-icon-s-tools"></i>
                  </div>
                </el-col>
              </el-row>
            </div>
          </el-tab-pane>
          <!--      解读规则  单产品      -->
          <el-tab-pane v-if="isSample" label="解读规则" name="解读规则">
            <div class="wrapper">

              <el-row :gutter="40">
                <el-col :span="24">
                  <el-form-item label="报告质控分类：" prop="reportQc">
                    <el-select v-model.trim="form.reportQc" clearable multiple placeholder="请选择" style="width: 100%">
                      <el-option :key="index" :label="item.reportQc" :value="item.id" v-for="(item, index) in reportQc">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="40">
                <el-col :span="8">
                  <el-form-item label="解读流程：" prop="readProcess">
                    <el-select v-model.trim="form.readProcess" clearable placeholder="请选择" style="width: 100%">
                      <el-option :key="index" :label="item" :value="item" v-for="(item, index) in readProcessList">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="信息分析流程：" prop="infoAnalysisProcess">
                    <el-select v-model.trim="form.infoAnalysisProcess" clearable placeholder="请选择" style="width: 100%">
                      <el-option :key="index" :label="item.fname" :value="item.fname"
                        v-for="(item, index) in infoAnalysisProcess">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="数据类型：" prop="fdataType">
                    <el-select v-model.trim="form.fdataType" clearable placeholder="请选择" style="width: 100%">
                      <el-option :key="index" :label="item" :value="item" v-for="(item, index) in dateTypes">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="解读任务分类类型：" prop="readTaskDistributionType">
                    <el-select v-model.trim="form.readTaskDistributionType" clearable placeholder="请选择"
                      style="width: 100%">
                      <el-option :key="index" :label="item.dictValue" :value="item.dictCode"
                        v-for="(item, index) in taskType">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="良性展示：" prop="fbenignShow">
                    <el-select v-model.trim="form.fbenignShow" clearable placeholder="请选择" style="width: 100%">
                      <el-option :key="index" :label="item" :value="index" v-for="(item, index) in ['否', '是']">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="脑胶质瘤指标：" prop="gliomaTarget">
                    <el-select v-model.trim="form.gliomaTarget" clearable placeholder="请选择" style="width: 100%">
                      <el-option :key="index" :label="item" :value="item" v-for="(item, index) in ['报出', '不报出']">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="40">
                <el-col :span="8">
                  <el-form-item label="T+B：" prop="tAndBConfig">
                    <el-select v-model.trim="form.tAndBConfig" clearable placeholder="请选择" style="width: 100%">
                      <el-option :key="index" :label="item" :value="index" v-for="(item, index) in ['否', '是']">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="外显子CNV报出：" prop="fisReportExonCnv">
                    <el-select v-model.trim="form.fisReportExonCnv" clearable placeholder="请选择" style="width: 100%">
                      <el-option :key="index" :label="item" :value="index" v-for="(item, index) in ['否', '是']">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="产品关注点：" prop="fproductFocusPoint">
                    <el-input v-model.trim="form.fproductFocusPoint" clearable placeholder="请输入" style="width: 100%">
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="40">
                <el-col :span="8">
                  <el-form-item label="TOP版本：" prop="topVersion">
                    <el-select v-model.trim="form.topVersion" clearable placeholder="请选择" style="width: 100%">
                      <el-option :key="index" :label="item" :value="index + 1" v-for="(item, index) in ['注检', '临检']">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="核心配置：" prop="coreConfiguration">
                    <el-select v-model.trim="form.coreConfiguration" clearable placeholder="请选择" style="width: 100%">
                      <el-option v-for="(item, index) in coreConfigs" :key="index" :label="item" :value="item">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="病原版本：" prop="fpathogenVersion">
                    <el-select v-model.trim="form.fpathogenVersion" clearable placeholder="请选择" style="width: 100%">
                      <el-option v-for="(item, index) in ['tNGS', 'mNGS']" :key="index" :label="item" :value="item">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="40">
                <el-col :span="8">
                  <el-form-item label="组织展示：" prop="ftissueShow">
                    <el-select v-model.trim="form.ftissueShow" clearable placeholder="请选择" style="width: 100%">
                      <el-option v-for="(item, index) in ['全部', '监测位点']" :key="index" :label="item" :value="index">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="40">
                <el-col :span="8">
                  <el-form-item label="TMB规则：" prop="tmbCalculationRule">
                    <el-select v-model.trim="form.tmbCalculationRule" clearable placeholder="请选择" style="width: 100%">
                      <el-option v-for="(value, key) in TMBRules" :key="value" :label="key" :value="value">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="TMB1泛癌种阈值：" prop="tmbUniversalCancerThreshold">
                    <el-input v-model.trim="form.tmbUniversalCancerThreshold"
                      oninput="value=value.replace(/^\.+|[^\d.]/g,'')" clearable style="width: 203px"
                      @blur="tmbUniversalCancerThresholdChange" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="TMB1结直肠癌阈值：" prop="tmbColorectalCancerThreshold">
                    <el-input v-model.trim="form.tmbColorectalCancerThreshold"
                      oninput="value=value.replace(/^\.+|[^\d.]/g,'')" clearable style="width: 203px"
                      @blur="tmbColorectalCancerThresholdChange" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="40">
                <el-col :span="8">
                  <el-form-item label="TMB人群数据库版本：" prop="tmbEditionNum">
                    <el-select v-model.trim="form.tmbEditionNum" clearable placeholder="请选择" style="width: 100%">
                      <el-option :key="index" :label="item" :value="item" v-for="(item, index) in TMBSqlVersion">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="TMB阈值类型：" prop="tmbThresholdType">
                    <el-select v-model.trim="form.tmbThresholdType" clearable placeholder="请选择" style="width: 100%">
                      <el-option :key="index" :label="item" :value="item" v-for="(item, index) in TMBType">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="免疫指标：" prop="fimmuneOut">
                    <el-select v-model.trim="form.fimmuneOut" clearable placeholder="请选择" style="width: 100%">
                      <el-option :key="index" :label="item" :value="item" v-for="(item, index) in ['报出', '不报出']">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="40">
                <el-col :span="8">
                  <el-form-item label="围手术期药物：" prop="operateDrug">
                    <el-select v-model.trim="form.operateDrug" clearable placeholder="请选择" style="width: 100%">
                      <el-option :key="index" :label="item.name" :value="item.value"
                        v-for="(item, index) in [{ name: '是', value: '0' }, { name: '否', value: '1' }]">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="WES比对：" prop="fwesComparison">
                    <el-select v-model.trim="form.fwesComparison" clearable placeholder="请选择" style="width: 100%">
                      <el-option :key="index" :label="item" :value="index + 1"
                        v-for="(item, index) in ['用药&遗传', '仅用药']">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="防伪二维码：" prop="isNeedQrcode">
                    <el-select v-model.trim="form.isNeedQrcode" clearable placeholder="请选择" style="width: 100%">
                      <el-option :key="index" :label="item" :value="`${index}`" v-for="(item, index) in ['不显示', '显示']">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="40">
                <el-col :span="16">
                  <el-form-item label="报告模版：" prop="reportCode">
                    <el-select v-model.trim="form.reportCode" clearable filterable placeholder="请选择"
                      style="width: 100%">
                      <el-option :key="index" :label="item.reportName" :value="item.reportCode"
                        v-for="(item, index) in templates">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <div style="color: deepskyblue;margin-top: 46px; cursor:pointer;" @click="openCustomizeTemplates">
                    客户定制化模版配置<i class="el-icon-s-tools"></i></div>
                </el-col>
              </el-row>
              <el-row :gutter="40">
                <el-col :span="16">
                  <el-form-item label="PI模版：" prop="patientReportCode">
                    <el-select-v2 v-model.trim="form.patientReportCode" :options="templates" value-key="reportCode"
                      label-key="reportName" size="mini" clearable collapse-tags filterable placeholder="请选择"
                      style="width: 100%" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="HRD配置：" prop="fisHrdConfig">
                    <el-select v-model.trim="form.fisHrdConfig" clearable filterable placeholder="请选择"
                      style="width: 100%">
                      <el-option :key="index" :label="item" :value="index" v-for="(item, index) in ['否', '是']">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="40">
                <el-col :span="8">
                  <el-form-item label="模板送检单位：" prop="isHideHospital">
                    <el-select v-model.trim="form.isHideHospital" clearable placeholder="请选择" style="width: 100%">
                      <el-option :key="index" :label="item" :value="index" v-for="(item, index) in ['显示', '不显示']">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="模板送检电话：" prop="isHidePhoneno">
                    <el-select v-model.trim="form.isHidePhoneno" clearable placeholder="请选择" style="width: 100%">
                      <el-option :key="index" :label="item" :value="index" v-for="(item, index) in ['显示', '不显示']">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="对照标准：" prop="fcontrolStandardId">
                    <el-select v-model.trim="form.fcontrolStandardId" clearable placeholder="请选择" style="width: 100%">
                      <el-option :key="index" :label="item.name" :value="item.id"
                        v-for="(item, index) in controlStandardList">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="40">
                <el-col :span="8">
                  <el-form-item label="SNV是否验证：" prop="fisSnvCheck">
                    <el-select v-model.trim="form.fisSnvCheck" clearable placeholder="请选择" style="width: 100%">
                      <el-option :key="index" :label="item" :value="index" v-for="(item, index) in ['否', '是']">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="HRD矫正：" prop="fisHrdAdjust">
                    <el-select v-model.trim="form.fisHrdAdjust" clearable placeholder="请选择" style="width: 100%">
                      <el-option :key="index" :label="item" :value="index" v-for="(item, index) in ['否', '是']">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="生信脚本是否调用：" prop="shengxinScript">
                    <template #label>
                      生信脚本是否调用：
                      <div style="display: inline-block;">
                        <el-tooltip class="item" effect="dark" placement="top-start">
                          <div slot="content">
                            每个生信脚本选项都有特殊规则，请谨慎选择。
                          </div>
                          <i class="el-icon-info"></i>
                        </el-tooltip>
                      </div>
                    </template>
                    <el-select v-model.trim="form.shengxinScript" clearable placeholder="请选择" style="width: 100%">
                      <el-option :key="index" :label="item" :value="item" v-for="(item, index) in ['北肿项目生信脚本']">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="40">
                <el-col :span="8">
                  <el-form-item label="WES SelectMuts：" prop="fwesSelectMuts">
                    <el-select v-model.trim="form.fwesSelectMuts" clearable placeholder="请选择" style="width: 100%">
                      <el-option :key="index" :label="item" :value="index" v-for="(item, index) in ['否', '是']">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="是否自动生成D数据：" prop="fisAutoCreateDData">
                    <el-select v-model.trim="form.fisAutoCreateDData" clearable placeholder="请选择" style="width: 100%">
                      <el-option :key="index" :label="item" :value="index" v-for="(item, index) in ['否', '是']">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="报告先后放发：" prop="isPiReportIssued">
                    <el-select v-model.trim="form.isPiReportIssued" :disabled="!form.patientReportCode" clearable
                      placeholder="请选择" style="width: 100%">
                      <el-option :key="index" :label="item" :value="index" v-for="(item, index) in ['否', '是']">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-tab-pane>
          <!--          检测范围         -->
          <el-tab-pane v-if="isSample" label="检测范围" name="second">
            <div class="wrapper">

              <el-form-item label="体系-SNV范围：" prop="fsomaVariationCheckRange">
                <el-input v-model.trim="form.fsomaVariationCheckRange" :rows="5" type="textarea" size="medium"
                  placeholder="多个基因用 '，' 隔开或换行隔开"></el-input>
              </el-form-item>
              <el-form-item label="体系-CNV范围：" prop="fsomaCnvCheckRange">
                <el-input v-model.trim="form.fsomaCnvCheckRange" :rows="5" type="textarea" size="medium"
                  placeholder="多个基因用 '，' 隔开或换行隔开"></el-input>
              </el-form-item>
              <el-form-item label="体系-SV范围：" prop="fsomaSvCheckRange">
                <el-input v-model.trim="form.fsomaSvCheckRange" :rows="5" type="textarea" size="medium"
                  placeholder="多个基因用 '，' 隔开或换行隔开"></el-input>
              </el-form-item>
              <el-form-item label="胚系变异检测范围：" prop="checkRange">
                <el-input v-model.trim="form.checkRange" :rows="5" type="textarea" size="medium"
                  placeholder="多个基因用 '，' 隔开或换行隔开">
                </el-input>
              </el-form-item>
              <el-form-item label="外显子CNV检测范围：" prop="fexonCnvCheckRange">
                <el-input v-model.trim="form.fexonCnvCheckRange" :rows="5" type="textarea" size="medium"
                  placeholder="多个基因用 '，' 隔开或换行隔开">
                </el-input>
              </el-form-item>
              <el-form-item label="新增DX点范围：" prop="fcreateDxRange">
                <el-input v-model.trim="form.fcreateDxRange" :rows="5" type="textarea" size="medium"
                  placeholder="多个基因用 '，' 隔开或换行隔开">
                </el-input>
              </el-form-item>
              <el-form-item label="癌种范围：" prop="fcancerRange">
                <el-input v-model.trim="form.fcancerRange" :rows="5" type="textarea" size="medium"
                  placeholder="多个癌种用 '，' 隔开或换行隔开">
                </el-input>
              </el-form-item>
              <el-form-item label="SNV验证基因范围：" prop="fsnvCheckGeneRange">
                <el-input v-model.trim="form.fsnvCheckGeneRange" :rows="5" type="textarea" size="medium"
                  placeholder="多个基因用 '，' 隔开或换行隔开">
                </el-input>
              </el-form-item>
              <el-form-item label="遗传解读范围：" prop="geneticVariationDetectionRang">
                <el-input v-model.trim="form.geneticVariationDetectionRang" :rows="5" type="textarea" size="medium"
                  placeholder="多个基因用 '，' 隔开或换行隔开">
                </el-input>
              </el-form-item>
            </div>
          </el-tab-pane>
          <!--          报告发放         -->
          <el-tab-pane label="报告发放" name="third">
            <div class="wrapper">
              <el-row :gutter="40">
                <el-col :span="8">
                  <el-form-item label="PDF加密：" prop="isPdfEncrypt">
                    <el-select v-model.trim="form.isPdfEncrypt" clearable placeholder="请选择">
                      <el-option :key="index" :label="item" :value="index" v-for="(item, index) in ['不加密', '加密']">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="是否增加修订标识：" prop="isReportRevise">
                    <el-select v-model.trim="form.isReportRevise" clearable placeholder="请选择">
                      <el-option :key="index" :label="item" :value="index" v-for="(item, index) in ['否', '是']">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="电子签章：" prop="isESeal">
                    <el-select v-model.trim="form.isESeal" clearable placeholder="请选择">
                      <el-option :key="index" :label="item" :value="index" v-for="(item, index) in ['不加签章', '加签章']">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="40">
                <el-col :span="8">
                  <el-form-item label="报告文件名：" prop="freportFileNameRule">
                    <el-select v-model.trim="form.freportFileNameRule" clearable placeholder="请选择">
                      <el-option :key="index" :label="item" :value="index" v-for="(item, index) in ['安诺报告文件名']">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="邮件主题规则：" prop="femailTitleRule">
                    <el-select v-model.trim="form.femailTitleRule" clearable placeholder="请选择">
                      <el-option :key="index" :label="item" :value="index" v-for="(item, index) in ['安诺邮件主题']">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="邮件样例编号：" prop="femailSampleNum">
                    <el-select v-model.trim="form.femailSampleNum" clearable placeholder="请选择">
                      <el-option :key="index" :label="item" :value="index" v-for="(item, index) in sampleTypes">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="40">
                <el-col :span="8">
                  <el-form-item label="word报告：" prop="docRequire">
                    <el-select v-model.trim="form.docRequire" clearable placeholder="请选择">
                      <el-option :key="index" :label="item" :value="index" v-for="(item, index) in ['不发送', '随邮件发送']">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="是否生成json文件：" prop="fisCreateJson">
                    <el-select v-model.trim="form.fisCreateJson" clearable placeholder="请选择">
                      <el-option :key="index" :label="item" :value="index" v-for="(item, index) in ['否', '是']">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="临床更新自动撤回：" prop="fupdateAutoRollback">
                    <el-select v-model.trim="form.fupdateAutoRollback" clearable placeholder="请选择">
                      <el-option :key="index" :label="item" :value="index" v-for="(item, index) in ['否', '是']">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="40">
                <el-col :span="8">
                  <el-form-item label="报告邮件收件人：" prop="reportMailrc">
                    <el-input v-model.trim="form.reportMailrc" style="width: 203px" clearable></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="报告邮件抄送人：" prop="reportMailcc">
                    <el-input v-model.trim="form.reportMailcc" style="width: 203px" clearable></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="报告收件人姓名：" prop="reportMailrcName">
                    <el-input v-model.trim="form.reportMailrcName" style="width: 203px" clearable></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="40">
                <el-col :span="8">
                  <el-form-item label="补充报告输出配置：" prop="supplementaryExcelTemplatePath">
                    <el-select v-model.trim="form.supplementaryWordTemplatePath" clearable placeholder="请选择">
                      <el-option :key="index" :label="item.name" :value="item.path"
                        v-for="(item, index) in wordTemplates">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="补充报告Excel输出配置：" prop="supplementaryExcelTemplatePath">
                    <el-select v-model.trim="form.supplementaryExcelTemplatePath" clearable placeholder="请选择">
                      <el-option :key="index" :label="item.name" :value="item.path"
                        v-for="(item, index) in excelTemplates">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </el-tab-pane>
          <el-tab-pane label="“更”标签参数配置" name="updateConfig">
            <el-table :data="updateTagConfigList" height="400px" :span-method="handleSpanMethod">
              <el-table-column prop="fmodel" label="所属模块" align="center"></el-table-column>
              <el-table-column prop="ffieldName" label="对应字段" align="center"></el-table-column>
              <el-table-column label="配置项" align="center">
                <template slot-scope="scope">
                  <el-select v-model.trim="scope.row.ffieldConfig" size="mini" clearable placeholder="请选择">
                    <el-option :key="index" :label="item" :value="index" v-for="(item, index) in ['否', '是']" />
                  </el-select>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
        </el-tabs>
      </el-form>
      <product-customer-template-dialog :title="customerTemplateDialogInfo.title" :product-id="productId"
        :pvisible.sync="customerTemplateDialogInfo.visible" />
      <div slot="footer" class="dialog-footer">
        <el-button size="mini" @click="handleClose">取 消</el-button>
        <el-button :loading="submitBtnLoading" size="mini" type="primary" @click="handleDialogConfirm">确认
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import mixins from '../../../util/mixins'
import ProductCustomerTemplateDialog from './productCustomerTemplateDialog'
import util from '../../../util/util'

export default {
  name: 'productInterpretConfigDialog',
  mixins: [mixins.dialogBaseInfo],
  components: { ProductCustomerTemplateDialog },
  props: {
    title: String, // 弹窗标题
    productId: Number, // 产品id
    isSample: Boolean // 是否是单产品
  },
  data () {
    return {
      submitBtnLoading: false,
      activeName: '', // 选中的tab页
      reportQc: [], // 质控报告分类
      taskType: [], // 解读任务分类类型
      controlStandardList: [], // 对照标准列表
      TMBRules: {
        '不报出': null,
        'TMB规则1': 'tmbCalc1',
        'TMB规则2': 'tmbCalc2'
      }, // TMB计算规则
      TMBSqlVersion: ['V1.1', 'V1.2'], // TMB人群数据库版本
      TMBType: ['tmb-OncoD-v1', 'tmb-OncoG-v1'], // TMB阈值类型
      sampleTypes: ['吉因加编号', '送检样本编号'], // 样本编号
      dateTypes: ['OncoD', 'TOP_clin', 'TOP_XC'],
      coreConfigs: ['K', 'M'],
      templates: [], // 模版列表
      updateTagConfigList: [],
      tableData: [],
      realData: {},
      readProcessList: [
        'OncoD', 'OncoH', 'OncoIR', 'MRD-B', 'OncoLym',
        'OncoWES', 'OncoWES2', 'OncoS-B', 'OncoFusion', 'Pathogeny'
      ],
      customerTemplateDialogInfo: {
        title: '客户定制化模版配置',
        visible: false
      },
      form: {
        productId: 0, // 产品id
        reportQc: '', // 报告质控分类
        readProcess: '', // 解读流程
        infoAnalysisProcess: '', // 信息分析流程
        readTaskDistributionType: '', // 解读任务分配类型
        fbenignShow: 0, // 良性展示
        gliomaTarget: '', // 脑胶质瘤指标
        tAndBConfig: '',
        fisReportExonCnv: '',
        fproductFocusPoint: '', // 产品关注点
        tmbCalculationRule: '', // TMB计算规则
        tmbUniversalCancerThreshold: '', // TMB1泛癌种阈值
        tmbColorectalCancerThreshold: '', // TMB1结直肠癌阈值
        tmbEditionNum: '', // TMB人群数据库版本
        tmbThresholdType: '', // TMB阈值类型
        fimmuneOut: '', // 免疫指标是否报出
        operateDrug: '', // 围手术期药物
        fwesComparison: '', // WES比对
        isNeedQrcode: '', // 防伪二维码
        reportCode: '', // 报告模板
        patientReportCode: '', // PI模板
        fisHrdConfig: null, // HRD配置
        isHideHospital: '', // 报告是否隐藏送检单位 0不隐藏 1隐藏
        isHidePhoneno: '', // 报告是否隐藏联系电话 0不隐藏 1隐藏
        fsomaVariationCheckRange: '', // 体系-SNV范围
        fsomaCnvCheckRange: '', // 体系-CNV范围
        fsomaSvCheckRange: '', // 体系-SV范围
        fexonCnvCheckRange: '',
        checkRange: '', // 胚系变异检测范围
        fcreateDxRange: '', // 新增DX点范围
        isPdfEncrypt: '', // PDF加密
        isReportRevise: '', // 报告是否修订 0否 1是
        isESeal: '', // 是否电子签章 0否 1是
        freportFileNameRule: '', // 报告文件名规则
        femailTitleRule: '', // 邮件主题规则
        femailSampleNum: '', // 邮件样例编号
        docRequire: '', // 是否发送word报告 0不发送 1发送
        fisCreateJson: '', // 是否生成json文件
        fupdateAutoRollback: '', // 临床更新自动撤回
        reportMailrc: '', // 报告邮件收件人
        reportMailcc: '', // 报告邮件抄送人
        reportMailrcName: '', // 报告收件人姓名
        supplementaryExcelTemplatePath: '', // 补充报告模版路径
        supplementaryWordTemplatePath: '', //  补充报告Excel模板
        shengxinScript: '',
        fpathogenVersion: '', // 病原类型
        ftissueShow: '', // 组织展示
        fisSnvCheck: '',
        fwesSelectMuts: '',
        fisAutoCreateDData: '',
        isPiReportIssued: '',
        fisHrdAdjust: '',
        fcancerRange: '',
        fsnvCheckGeneRange: '',
        geneticVariationDetectionRang: ''
      },
      wordTemplates: [],
      excelTemplates: [],
      submitForm: {},
      submitReportForm: {},
      rules: {
        readTaskDistributionType: [
          { required: true, message: '请选择解读任务分类类型', trigger: ['change', 'blur'] }
        ],
        reportMailrc: [
          // {required: true, message: '请输入邮箱', trigger: ['change', 'blur']},
          { required: false, validator: util.validateElementEmail, trigger: ['change', 'blur'] }
        ],
        reportMailcc: [
          // {required: true, message: '请输入邮箱', trigger: ['change', 'blur']},
          { required: false, validator: util.validateElementEmail, trigger: ['change', 'blur'] }
        ],
        reportMailrcName: [
          // {required: true, message: '请输入邮箱', trigger: ['change', 'blur']},
          { required: false, validator: util.validateElementEmail, trigger: ['change', 'blur'] }
        ]
      }
    }
  },
  methods: {
    handleOpen () {
      this.$nextTick(() => {
        this.activeName = '解读规则'
        this.$refs.form.resetFields()
        this.getFormData()
        this.getControlList()
        this.getSupplementaryWordTemplates()
        this.getSupplementaryExcelTemplates()
        this.getAnalysisProcess()
        // 获取产品的更标签详情配置
        this.getUpdateTagDetail()
        if (this.isSample) {
          this.getReportQc()
          this.getTaskType()
          this.getAllTemplates()
        } else {
          this.getTaskType()
          this.getChildProduct()
          this.getAllTemplates()
        }
      })
    },
    nameGroup (name) {
      // 获取相同名称的长度
      return this.getData.filter((o) => o.fmodel === name).length
    },
    nameLen (name) {
      const tmp = Array.from(this.groupNum)
      let index = tmp.indexOf(name)
      let len = 0
      for (let i = 0; i < index; i++) {
        len += this.nameGroup(tmp[i])
      }
      return len
    },
    // 获取信息分析流程 /system/product/get_analysis_process
    getAnalysisProcess () {
      this.$ajax({
        url: '/system/product/get_analysis_process',
        method: 'get'
      }).then((result) => {
        if (result.code === this.SUCCESS_CODE) {
          this.infoAnalysisProcess = result.data || []
        } else {
          this.$message.error(result.message)
        }
      }).catch((e) => {
        console.log(e)
      })
    },
    handleSpanMethod ({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        // 根据重名的数据长度，计算合并的单元格长度
        return {
          rowspan: row.total, // 根据实际重复的名称合并，而不是像官网例子中写死合并2行
          colspan: 1
        }
      } else {
        return {
          rowspan: 1,
          colspan: 1
        }
      }
    },
    // 获取产品的更标签详情配置
    getUpdateTagDetail () {
      this.$ajax({
        url: '/system/product/get_product_update_sign_config',
        method: 'get',
        data: { productId: this.productId }
      }).then((result) => {
        if (result.code === this.SUCCESS_CODE) {
          const data = result.data || []
          let total = 0
          this.updateTagConfigList = data.map((v, i) => {
            if (i === 0 || v.fmodel !== data[i - 1].fmodel) {
              total = data.filter((o) => o.fmodel === v.fmodel).length
            } else {
              total = 0
            }
            return {
              total: total,
              ...v
            }
          })
        }
      })
    },
    // 获取表单信息
    getFormData () {
      this.$ajax({
        url: '/system/product/get_product_detail',
        method: 'get',
        loadingDom: '.form',
        data: { productId: this.productId }
      }).then((result) => {
        if (result.code === this.SUCCESS_CODE) {
          let {
            productId, // 产品id
            reportQc, // 报告质控分类
            readProcess, // 解读流程
            infoAnalysisProcess, // 信息分析流程
            readTaskDistributionType, // 解读任务分配类型
            fbenignShow,
            gliomaTarget, // 脑胶质瘤指标
            tAndBConfig,
            fisReportExonCnv,
            fproductFocusPoint,
            topVersion,
            coreConfiguration,
            tmbCalculationRule, // TMB计算规则
            tmbUniversalCancerThreshold, // TMB1泛癌种阈值
            tmbColorectalCancerThreshold, // TMB1结直肠癌阈值
            tmbEditionNum, // TMB人群数据库版本
            tmbThresholdType, // TMB阈值类型
            fimmuneOut, // 免疫指标是否报出
            operateDrug, // 围手术期药物
            fwesComparison, // WES比对
            isNeedQrcode, // 防伪二维码
            reportCode, // 报告模板
            patientReportCode, // PI模板
            fisHrdConfig, // 是否配置HRD
            isHideHospital, // 报告是否隐藏送检单位 0不隐藏 1隐藏
            isHidePhoneno, // 报告是否隐藏联系电话 0不隐藏 1隐藏
            fsomaVariationCheckRange, // 体系-SNV范围
            fsomaCnvCheckRange, // 体系-CNV范围
            fsomaSvCheckRange, // 体系-SV范围
            fexonCnvCheckRange,
            checkRange, // 胚系变异检测范围
            fcreateDxRange, // 新增DX点范围
            fsnvCheckGeneRange,
            geneticVariationDetectionRang,
            fcancerRange,
            isPdfEncrypt, // PDF加密
            isReportRevise, // 报告是否修订 0否 1是
            isESeal, // 是否电子签章 0否 1是
            freportFileNameRule, // 报告文件名规则
            femailTitleRule, // 邮件主题规则
            femailSampleNum, // 邮件样例编号
            docRequire, // 是否发送word报告 0不发送 1发送
            fisCreateJson,
            fupdateAutoRollback,
            reportMailrc, // 报告邮件收件人
            reportMailcc, // 报告邮件抄送人
            fcontrolStandardId, // 对照标准
            fisSnvCheck,
            fwesSelectMuts,
            fisAutoCreateDData,
            isPiReportIssued,
            fisHrdAdjust,
            reportMailrcName, // 报告收件人姓名
            supplementaryExcelTemplatePath, // 补充报告模版路径
            supplementaryWordTemplatePath, //  补充报告Excel模板
            shengxinScript, // 生信脚本
            fdataType, // 数据类型
            fpathogenVersion, // 病原类型
            ftissueShow
          } = result.data
          if (reportQc) {
            reportQc = reportQc.split(',') || []
            reportQc = reportQc.map(item => parseInt(item))
          }
          fbenignShow = fbenignShow || 0
          this.form = {
            productId, // 产品id
            reportQc: reportQc, // 报告质控分类
            readProcess, // 解读流程
            infoAnalysisProcess, // 信息分析流程
            readTaskDistributionType, // 解读任务分配类型
            fbenignShow,
            gliomaTarget, // 脑胶质瘤指标
            tAndBConfig,
            fisReportExonCnv,
            fproductFocusPoint,
            topVersion,
            coreConfiguration,
            tmbCalculationRule, // TMB计算规则
            tmbUniversalCancerThreshold, // TMB1泛癌种阈值
            tmbColorectalCancerThreshold, // TMB1结直肠癌阈值
            tmbEditionNum, // TMB人群数据库版本
            tmbThresholdType, // TMB阈值类型
            fimmuneOut, // 免疫指标是否报出
            operateDrug, // 围手术期药物
            fwesComparison, // WES比对
            isNeedQrcode, // 防伪二维码
            reportCode, // 报告模板
            patientReportCode, // PI模板
            fisHrdConfig,
            isHideHospital, // 报告是否隐藏送检单位 0不隐藏 1隐藏
            isHidePhoneno, // 报告是否隐藏联系电话 0不隐藏 1隐藏
            fsomaVariationCheckRange, // 体系-SNV范围
            fsomaCnvCheckRange, // 体系-CNV范围
            fsomaSvCheckRange, // 体系-SV范围
            fexonCnvCheckRange,
            checkRange, // 胚系变异检测范围
            fcreateDxRange, // 新增DX点范围
            fsnvCheckGeneRange,
            geneticVariationDetectionRang,
            fcancerRange,
            isPdfEncrypt, // PDF加密
            isReportRevise, // 报告是否修订 0否 1是
            isESeal, // 是否电子签章 0否 1是
            freportFileNameRule, // 报告文件名规则
            femailTitleRule, // 邮件主题规则
            femailSampleNum, // 邮件样例编号
            docRequire, // 是否发送word报告 0不发送 1发送
            fisCreateJson,
            fupdateAutoRollback,
            reportMailrc, // 报告邮件收件人
            reportMailcc,
            fdataType,
            fcontrolStandardId,
            fisSnvCheck,
            fwesSelectMuts,
            fisAutoCreateDData,
            isPiReportIssued,
            fisHrdAdjust,
            reportMailrcName, // 报告收件人姓
            supplementaryExcelTemplatePath, // 补充报告模版路径
            supplementaryWordTemplatePath, //  补充报告Excel模板
            shengxinScript,
            fpathogenVersion,
            ftissueShow
          }
          this.realData = result.data
        } else {
          this.$message.error(result.message)
        }
      }).catch((e) => {
        console.log(e)
      })
    },
    // 获取对照标准
    getControlList () {
      this.$ajax({
        url: '/system/control_standard/list_control_standard',
        method: 'get'
      }).then((result) => {
        if (result.code === this.SUCCESS_CODE) {
          this.controlStandardList = []
          result.data = result.data || []
          result.data.forEach(v => {
            let item = {
              name: v.fname, // 质控分类名
              id: v.fid // 质控分类id
            }
            this.controlStandardList.push(item)
          })
        } else {
          this.$message.error(result.message)
        }
      })
    },
    // 获取对照标准
    getSupplementaryWordTemplates () {
      this.$ajax({
        url: '/system/template/get_supplementary_word_templates',
        method: 'get'
      }).then((result) => {
        if (result.code === this.SUCCESS_CODE) {
          this.wordTemplates = []
          result.data = result.data || []
          result.data.forEach(v => {
            let item = {
              name: v.reportName, // 质控分类名
              path: v.reportPath // 质控分类id
            }
            this.wordTemplates.push(item)
          })
        } else {
          this.$message.error(result.message)
        }
      })
    },
    // 获取对照标准
    getSupplementaryExcelTemplates () {
      this.$ajax({
        url: '/system/template/get_supplementary_excel_templates',
        method: 'get'
      }).then((result) => {
        if (result.code === this.SUCCESS_CODE) {
          this.excelTemplates = []
          result.data = result.data || []
          result.data.forEach(v => {
            let item = {
              name: v.reportName, // 质控分类名
              path: v.reportPath // 质控分类id
            }
            this.excelTemplates.push(item)
          })
        } else {
          this.$message.error(result.message)
        }
      })
    },
    // 获取质控分类 /system/product/get_report_qc
    getReportQc () {
      this.$ajax({
        url: '/system/product/get_report_qc',
        method: 'get'
      }).then((result) => {
        if (result.code === this.SUCCESS_CODE) {
          this.reportQc = []
          result.data = result.data || []
          result.data.forEach(v => {
            let item = {
              reportQc: v.qc_type, // 质控分类名
              id: v.id // 质控分类id
            }
            this.reportQc.push(item)
          })
        } else {
          this.$message.error(result.message)
        }
      })
    },
    // 获取解读任务分配类型 /system/product/get_read_task_type
    getTaskType () {
      this.$ajax({
        url: '/system/product/get_read_task_type',
        method: 'get'
      }).then((result) => {
        if (result.code === this.SUCCESS_CODE) {
          result.data = result.data || []
          this.taskType = []
          result.data.forEach(v => {
            let item = {
              dictValue: v.dictValue, // 解读任务类型名
              dictCode: v.dictCode // 解读任务类型id
            }
            this.taskType.push(item)
          })
        } else {
          this.$message.error(result.message)
        }
      }).catch((e) => {
        console.log(e)
      })
    },
    // 获取模版 /system/product/get_all_template
    getAllTemplates () {
      this.$ajax({
        url: '/system/product/get_all_template',
        method: 'get'
      }).then((result) => {
        if (result.code === this.SUCCESS_CODE) {
          result.data = result.data || []
          this.templates = []
          result.data.forEach(v => {
            let item = {
              reportTemplateId: v.reportTemplateId, // 报告模版id
              reportName: v.reportName,
              reportCode: v.reportCode
            }
            this.templates.push(item)
          })
        } else {
          this.$message.error(result.message)
        }
      }).catch((e) => {
        console.log(e)
      })
    },
    formateStr (str = '') {
      return str.replace(/\n/g, ',')
        .replace(/\s/g, ',')
        .replace(/，/g, ',')
        .replace(/、/g, ',')
        .replace(/;/g, ',')
        .replace(/；/g, ',')
    },
    // 确认提交解读配置
    handleDialogConfirm () {
      this.submitForm = { ...this.realData, ...this.form }
      if (this.submitForm.fsomaVariationCheckRange) {
        this.submitForm.fsomaVariationCheckRange = this.formateStr(this.submitForm.fsomaVariationCheckRange)
      } // 体系-SNV范围
      if (this.submitForm.fsomaCnvCheckRange) {
        this.submitForm.fsomaCnvCheckRange = this.formateStr(this.submitForm.fsomaCnvCheckRange) // 体系-CNV范围
      }
      if (this.submitForm.fsomaSvCheckRange) {
        this.submitForm.fsomaSvCheckRange = this.formateStr(this.submitForm.fsomaSvCheckRange) // 体系-CNV范围// 体系-SV范围
      }
      if (this.submitForm.fexonCnvCheckRange) {
        this.submitForm.fexonCnvCheckRange = this.formateStr(this.submitForm.fexonCnvCheckRange) // 体系-CNV范围// 体系-SV范围// 胚系变异检测范围
      }
      if (this.submitForm.checkRange) {
        this.submitForm.checkRange = this.formateStr(this.submitForm.checkRange) // 体系-CNV范围// 体系-SV范围// 胚系变异检测范围
      }
      if (this.submitForm.fcreateDxRange) {
        this.submitForm.fcreateDxRange = this.formateStr(this.submitForm.fcreateDxRange) // 体系-CNV范围// 体系-SV范围 // 新增DX点范围
      }
      if (this.submitForm.fsnvCheckGeneRange) {
        this.submitForm.fsnvCheckGeneRange = this.formateStr(this.submitForm.fsnvCheckGeneRange) // 体系-CNV范围// 体系-SV范围 // 新增DX点范围
      }
      if (this.submitForm.geneticVariationDetectionRang) {
        this.submitForm.geneticVariationDetectionRang = this.formateStr(this.submitForm.geneticVariationDetectionRang) // 体系-CNV范围// 体系-SV范围 // 新增DX点范围
      }
      if (this.submitForm.fcancerRange) {
        this.submitForm.fcancerRange = this.formateStr(this.submitForm.fcancerRange) // 体系-CNV范围// 体系-SV范围 // 新增DX点范围
      }

      if (Array.isArray(this.submitForm.reportQc)) this.submitForm.reportQc = this.submitForm.reportQc.join(',')
      this.$refs.form.validate(valid => {
        if (valid) {
          this.submitBtnLoading = true
          // 保存更标签配置项
          this.handleSaveTagConfig()
          this.$ajax({
            url: '/system/product/save_product_config',
            method: 'post',
            data: this.submitForm
          }).then((result) => {
            if (result.code === this.SUCCESS_CODE) {
              this.$message.success('提交成功')
              this.visible = false
              this.$emit('dialogConfirmEvent')
            } else {
              this.$message.error(result.message)
            }
          }).catch((e) => {
            console.log(e)
          }).finally(() => {
            this.submitBtnLoading = false
          })
        }
      })
    },
    // 保存更标签配置项
    handleSaveTagConfig () {
      this.$ajax({
        url: '/system/product/save_product_update_sign_config',
        method: 'post',
        data: {
          fproductId: this.productId,
          configList: this.updateTagConfigList
        }
      }).then((result) => {
        if (result.code !== this.SUCCESS_CODE) {
          this.$message.error(result.message)
        }
      })
    },
    openCustomizeTemplates () {
      this.customerTemplateDialogInfo.visible = true
    },
    // 获取组合产品子产品 /system/product/get_child_product
    getChildProduct () {
      this.$ajax({
        url: '/system/product/get_child_product',
        method: 'get',
        data: { productId: this.productId }
      }).then((result) => {
        if (result.code === this.SUCCESS_CODE) {
          result.data = result.data || []
          this.tableData = []
          result.data.forEach(v => {
            let item = {
              productCode: v.productCode, // 产品编号
              productName: v.productName, // 产品名称
              readProcess: v.readProcess, // 解读流程
              readTaskDistributionType: v.readTaskDistributionType, // 解读任务分配类型
              seqPlat: v.seqPlat, // 测序平台
              deliverCycle: v.deliverCycle // 交付日期
            }
            this.tableData.push(item)
          })
        } else {
          this.$message.error(result.message)
        }
      }).catch((e) => {
        console.log(e)
      })
    },
    // 确保输入数字
    tmbUniversalCancerThresholdChange (e) {
      this.tmbUniversalCancerThreshold = e.target.value
    },
    tmbColorectalCancerThresholdChange (e) {
      this.tmbColorectalCancerThreshold = e.target.value
    }
  }
}
</script>

<style scoped lang="scss">
.tips {
  margin: 10px 70px 20px 70px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  background: #dfefff;
}

.wrapper {
  height: 50vh;
  overflow-y: auto;
  overflow-x: hidden;
}
</style>
