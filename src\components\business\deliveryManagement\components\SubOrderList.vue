<template>
  <div class="sub-order-wrapper">
    <div class="table-wrapper">
      <el-table
        ref="table"
        :data="tableData"
        :cell-style="handleRowStyle"
        class="sub-table"
        size="mini"
        border
        :row-class-name="handleClassName"
        @select="handleSelectTable"
        @row-click="handleRowClick"
        @select-all="handleSelectAll">
        <el-table-column type="selection" width="55" :selectable="checkSelectable"></el-table-column>
        <el-table-column prop="subDeliverStatus" label="交付状态" width="140" show-overflow-tooltip>
          <template slot-scope="scope">
<!--            <el-tooltip v-if="+scope.row.subDeliverStatus === 9 && scope.row.fnote" placement="top" effect="dark" :content="scope.row.fnote">-->
<!--              <span :class="scope.row.className">{{ scope.row.subDeliverStatusText }}</span>-->
<!--            </el-tooltip>-->
            <span :class="scope.row.className">{{ scope.row.subDeliverStatusText }}</span>
            <el-tooltip
              v-if="scope.row.fnote"
              placement="top"
              effect="dark"
              :content="scope.row.fnote"
            >
              <i class="el-icon-info"></i>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="deliverTime" label="交付日期" min-width="120" show-overflow-tooltip></el-table-column>
        <!--    交付批次-->
        <el-table-column prop="cosDeliverBatchCode" label="交付批次" min-width="120" show-overflow-tooltip></el-table-column>
        <!--    上机批次-->
        <el-table-column prop="seqBatch" label="上机批次" min-width="80" show-overflow-tooltip></el-table-column>
        <!--    芯片号-->
        <el-table-column prop="fcNum" label="芯片号" min-width="80" show-overflow-tooltip></el-table-column>
        <!--    原始样本名称-->
        <el-table-column prop="oriSampleName" label="原始样本名称" min-width="120" show-overflow-tooltip></el-table-column>
        <!--    原始子文库名称-->
        <el-table-column prop="oriSampleLibName" label="原始子文库名称" min-width="150" show-overflow-tooltip></el-table-column>
        <!--    吉因加编号-->
        <el-table-column prop="geneNum" label="吉因加编号" min-width="100" show-overflow-tooltip></el-table-column>
        <!--    杂交子文库-->
        <el-table-column prop="libNum" label="杂交子文库" min-width="230" show-overflow-tooltip>
          <template slot-scope="scope">
            <span v-if="scope.row.libNum" :style="`${scope.row.fisDataPollute  === 1 ? 'color: red;' : ''}`">{{scope.row.libNum}}</span>
          </template>
        </el-table-column>
        <!--    下单数据量（G）-->
        <el-table-column prop="orderDataSize" label="下单数据量（G）" min-width="120" show-overflow-tooltip></el-table-column>
        <!--    下机产量（G）-->
        <el-table-column prop="afterFilterDataSize" label="过滤后产量（G）" width="120" show-overflow-tooltip></el-table-column>
        <!--    质控结果-->
        <el-table-column prop="qcResult" label="数据质控" min-width="80" show-overflow-tooltip></el-table-column>
        <el-table-column prop="libLevel" label="样本质控" min-width="80" show-overflow-tooltip></el-table-column>
        <!--    数据情况-->
        <el-table-column prop="dataSituation" label="数据情况" min-width="80" show-overflow-tooltip></el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import mixins from '../../../../util/mixins'
import util, {awaitWrap} from '../../../../util/util'
import {getSubDeliverOrderList} from '../../../../api/deliveryManagement'

export default {
  name: 'SubOrderList',
  mixins: [mixins.tablePaginationCommonData],
  props: {
    id: {
      type: Number,
      default: null
    },
    params: {
      type: Object,
      default: () => ({})
    }
  },
  watch: {
    selectedRowsSize () {
      const selectedData = [...this.selectedRows.values()]
      console.log(`子订单组件 ${this.id} 选择变化:`, {
        selectedRowsSize: this.selectedRowsSize,
        selectedData: selectedData.length,
        selectedIds: selectedData.map(d => d.id)
      })
      this.$emit('subOrderSelectedChange', {key: this.id, data: selectedData})
    }
  },
  data () {
    return {
      tableData: [],
      subLibStatus: {
        8: '未上机',
        0: '未投递',
        1: '等待投递',
        2: '投递失败',
        3: '待交付',
        110: '已交付',
        5: '已关闭',
        6: '已停止',
        7: '不交付',
        4: '已有交付',
        9: '失败（吉云）'
      },
      result: {
        0: '不合格',
        1: '合格'
      },
      subLibColors: {
        0: 'blue',
        1: 'blue',
        2: 'red',
        3: 'green',
        9: 'red'
      }
    }
  },
  methods: {
    async getData (id) {
      const { res } = await awaitWrap(getSubDeliverOrderList({
        fcosDeliverOrderId: id,
        ...this.params
      }, {loadingDom: '.sub-table'}))
      if (res && res.code === this.SUCCESS_CODE) {
        const data = res.data || []
        data.forEach(v => {
          const item = {
            id: v.fcosDeliverBatchDetailId,
            subDeliverStatus: v.fsubDeliverStatus,
            subDeliverStatusText: this.subLibStatus[v.fsubDeliverStatus],
            className: this.subLibColors[v.fsubDeliverStatus],
            cosDeliverBatchCode: v.fcosDeliverBatchCode,
            seqBatch: v.fseqBatch,
            fcNum: v.ffcNum,
            oriSampleName: v.foriSampleName,
            oriSampleLibName: v.foriSampleLibName,
            geneNum: v.fgeneNum,
            libNum: v.flibNum,
            orderDataSize: v.forderDataSize,
            dataSize: v.fdataSize,
            walkthroughDateSize: v.fwalkthroughDateSize,
            preFilterDataSize: v.fpreFilterDataSize,
            afterFilterDataSize: v.fafterFilterDataSize,
            dataRemark: v.fdataRemark,
            dataSituation: v.fdataSituation,
            qcResult: this.result[v.fqcResult],
            fisDataPollute: v.fisDataPollute * 1,
            libLevel: v.flibLevel,
            sampleStatus: v.fsampleStatus,
            deliverTime: v.fdeliverTime,
            deliveryOrderId: v.fcosDeliverOrderId,
            fnote: v.fnote
          }
          item.realData = JSON.parse(JSON.stringify(item))
          util.setDefaultEmptyValueForObject(item)
          this.tableData.push(item)
          this.$nextTick(() => {
            if (this.$refs.table) {
              if (this.$refs.table) this.$refs.table.doLayout()
            }
          })
        })
      }
    },
    checkSelectable (row) {
      return row.realData.cosDeliverBatchCode
    },
    // 点击行
    handleRowClick (row, c) {
      if (!this.checkSelectable(row)) return
      this.handleSelectTable(undefined, row)
    }
  }
}
</script>

<style scoped lang="scss">
.sub-order-wrapper {
  //max-height: 20vh;
  padding-left: 30px;
}
.table-wrapper {
}
.blue {
  color: $color
}
.green {
  color: $success-color
}
.red {
  color: $fail-color
}
</style>
