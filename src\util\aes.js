import CryptoJS from 'crypto-js'

let keyIv = 'lims#@!123qwezxc' // 必须为16位
let options = {
  iv: CryptoJS.enc.Utf8.parse(keyIv),
  mode: CryptoJS.mode.CBC,
  padding: CryptoJS.pad.ZeroPadding
}
export let encryptAES = function (word) {
  let key = CryptoJS.enc.Utf8.parse(keyIv)
  let srcs = CryptoJS.enc.Utf8.parse(word)
  let encrypted = CryptoJS.AES.encrypt(srcs, key, options)
  return encrypted.toString()
}
export let decryptAES = function (word) {
  let key = CryptoJS.enc.Utf8.parse(keyIv)
  let decrypt = CryptoJS.AES.decrypt(word, key, options)
  return CryptoJS.enc.Utf8.stringify(decrypt).toString()
}
