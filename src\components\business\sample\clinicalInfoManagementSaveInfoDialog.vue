<template>
  <div>
    <el-dialog
      :title="title"
      :visible.sync="visible"
      :close-on-click-modal="false"
      :before-close="handleClose" width="98%"
      top="20px"
      @open="handleOpen">
      <div class="infoContent">
        <div class="left">
          <el-form ref="form" :model="form"  :rules="rules" size="mini" label-width="130px" label-suffix=":">
            <el-collapse v-model="activeNames" @change="handleChange">
              <el-collapse-item :class="{isOpen: activeNames.indexOf('1') !== -1}" title="基本信息" name="1">
                <template slot="title">
                  <div>基本信息</div>
                  <el-checkbox v-model.trim="isAudit[0]" v-if="type == 2" class="title-checkbox" @change="handleAuditStatusChange">已审</el-checkbox>
                </template>
                <el-row :gutter="10">
                  <el-col :span="8" class="colHeight">
                    <el-form-item label="样例编号">{{form.sampleNum}}</el-form-item>
                  </el-col>
<!--                  <el-col :span="12" class="colHeight">-->
<!--                    <el-form-item label="产品/项目名称">-->
<!--                      <el-tooltip effect="dark" :content="form.productName" placement="top">-->
<!--                        <div class="productName">{{form.productName}}</div>-->
<!--                      </el-tooltip>-->
<!--                    </el-form-item>-->
<!--                  </el-col>-->
<!--                  <el-col :span="8" class="colHeight redStyle">-->
<!--                    <el-form-item label="样本类型">-->
<!--                      <el-select v-model.trim="form.sampleType" allow-create filterable :disabled="disabled" placeholder="请选择" class="selectItem">-->
<!--                        <el-option-->
<!--                          v-for="(item, index) in sampleTypes"-->
<!--                          :key="index"-->
<!--                          :label="item.label"-->
<!--                          :value="item.value"/>-->
<!--                      </el-select>-->
<!--                    </el-form-item>-->
<!--                  </el-col>-->
                  <el-col :span="8" class="colHeight">
                    <el-form-item label="基线送检次数">
                      <el-input v-model.trim="form.baselineSendTimes" placeholder="请输入" disabled></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item>
                      <el-table
                        :data="productList"
                        size="mini"
                        :span-method="spanMethod"
                        border>
                        <el-table-column label="产品" prop="productName"></el-table-column>
                        <el-table-column label="子产品" prop="childProName"></el-table-column>
                        <el-table-column label="样本类型" prop="sampleType">
                          <template slot-scope="scope">
                            <el-select v-model.trim="scope.row.sampleType" :disabled="disabled" allow-create filterable placeholder="请选择" class="selectItem">
                              <el-option
                                :key="index"
                                :label="item.label"
                                :value="item.value"
                                v-for="(item, index) in sampleTypes"/>
                            </el-select>
                          </template>
                        </el-table-column>
                      </el-table>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" class="colHeight">
                    <el-form-item label="送检单位">
                      <el-select v-model="form.inspectionUnitName" disabled placeholder="请选择" class="selectItem">
                        <el-option
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                          v-for="item in optionsList">
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" class="colHeight">
                    <el-form-item label="科室">
                      <el-select v-model="form.departments" filterable allow-create disabled clearable placeholder="请选择" style="width: 100%;">
                        <el-option
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                          v-for="item in departmentList">
                        </el-option>
                      </el-select>
                      <!--<el-input v-model.trim="form.departments" maxlength="20" :disabled="disabled" placeholder="请输入"></el-input>-->
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" class="colHeight">
                    <el-form-item label="样本癌种类型">
                      <el-select v-model="form.sampleCancerType" :disabled="disabled" placeholder="请选择" class="selectItem">
                        <el-option
                          :key="item"
                          :label="item"
                          :value="item"
                          v-for="item in ['乳腺癌', '肠癌', '肺癌', '泛癌', '胰腺癌', '小细胞肺癌', '黑色素瘤']">
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" class="colHeight">
                    <el-form-item label="ctDNA类型">
                      <el-select v-model="form.ctDnaType" :disabled="disabled" clearable placeholder="请选择" style="width: 100%;">
                        <el-option
                          :key="item"
                          :label="item"
                          :value="item"
                          v-for="item in ['术后血', '术前血', '其他']">
                        </el-option>
                      </el-select>
                      <!--<el-input v-model.trim="form.departments" maxlength="20" :disabled="disabled" placeholder="请输入"></el-input>-->
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" class="colHeight">
                    <el-form-item label="送检医生">
                      <el-select v-model="form.doctor" disabled placeholder="请选择" class="selectItem">
                        <el-option
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                          v-for="item in optionsList">
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" class="colHeight">
                    <el-form-item label="送检日期">
                      <el-date-picker v-model="form.inspectionTime" :disabled="disabled" class="selectItem" type="datetime" value-format="yyyy-MM-dd HH:mm:ss" placeholder="选择日期时间"></el-date-picker>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" class="colHeight">
                    <el-form-item label="原始编号">
                      <el-input v-model.trim="form.originNum" disabled  placeholder="请输入"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" class="colHeight">
                    <el-form-item label="送检方编号">
                      <el-input v-model.trim="form.thirdpartySampleNum" :disabled="disabled" placeholder="请输入"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" class="colHeight redStyle">
                    <el-form-item label="姓名">
                      <el-input v-model.trim="form.name" :disabled="disabled" maxlength="30" placeholder="请输入"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" class="colHeight redStyle">
                    <el-form-item label="性别">
                      <el-radio-group v-model="form.sex" :disabled="disabled">
                        <el-radio :label="0">男</el-radio>
                        <el-radio :label="1">女</el-radio>
                        <!--<el-radio :label="2">不详</el-radio>-->
                      </el-radio-group>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" class="colHeight">
                    <el-form-item label="证件类型" prop="cardType">
                      <el-select v-model.trim="form.cardType" :disabled="disabled" filterable allow-create clearable placeholder="请选择" style="width: 100%;" @change="handleCardTypeChange">
                        <el-option
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                          v-for="item in cardTypeList">
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" class="colHeight">
                    <el-form-item label="证件号" prop="idcard">
                      <el-input v-model.trim="form.idcard" :disabled="disabled" placeholder="请输入" @change="handleIdCardBlur"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" class="colHeight">
                    <el-form-item label="民族">
                      <el-select v-model="form.nation" :disabled="disabled" filterable allow-create clearable placeholder="请选择" style="width: 100%;">
                        <el-option
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                          v-for="item in nationList">
                        </el-option>
                      </el-select>
                      <!--<el-input v-model.trim="form.nation" maxlength="10" :disabled="disabled" placeholder="请输入"></el-input>-->
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" class="colHeight">
                    <el-form-item label="出生年月">
                      <!--<el-date-picker v-model="form.birthday" type="month" value-format="yyyy-MM" placeholder="请选择年月" class="selectItem"></el-date-picker>-->
                      <my-date-picker v-model="form.birthday" :is-disabled="disabled" @change="handleBirthdayChange"></my-date-picker>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" class="colHeight">
                    <el-form-item label="电子邮箱">
                      <el-input v-model.trim="form.email" :disabled="disabled" placeholder="请输入"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" class="colHeight">
                    <el-form-item label="联系电话1" prop="contactTel">
                      <el-input v-model.trim="form.contactTel" :disabled="disabled" placeholder="请输入"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" class="colHeight">
                    <el-form-item label="联系电话2" prop="familyContactType">
                      <el-input v-model.trim="form.familyContactType" :disabled="disabled" placeholder="请输入"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" class="colHeight">
                    <el-form-item label="邮寄地址">
                      <el-cascader :disabled="disabled"
                                   :options="regionList"
                                   v-model="form.detailAddr"
                                   :props ="{checkStrictly: true, value: 'code', label: 'name'}" class="selectItem"
                                   style="width: 100%"
                                   clearable
                                   placeholder="请选择">
                      </el-cascader>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" class="colHeight">
                    <el-form-item label="详细地址">
                      <el-input v-model.trim="form.contactAddr" :disabled="disabled" placeholder="请输入"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" class="colHeight">
                    <el-form-item label="收件人">
                      <el-input v-model.trim="form.recipient" :disabled="disabled" placeholder="请输入"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" class="colHeight">
                    <el-form-item label="收件联系方式">
                      <el-input v-model.trim="form.recipientTel" :disabled="disabled" placeholder="请输入"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" class="colHeight">
                    <el-form-item label="医院医生">
                      <el-input v-model.trim="form.inspectionDoctor" :disabled="disabled" placeholder="请输入"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" class="colHeight">
                    <el-form-item label="住院号">
                      <el-input v-model.trim="form.admissionNum" :disabled="disabled" placeholder="请输入" :maxlength="50"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" class="colHeight">
                    <el-form-item label="收费条码号">
                      <el-input v-model.trim="form.chargeCode" :disabled="disabled" placeholder="请输入" :maxlength="50"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" class="colHeight">
                    <el-form-item label="门诊号">
                      <el-input v-model.trim="form.outpatientService" :disabled="disabled" placeholder="请输入" :maxlength="50"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" class="colHeight">
                    <el-form-item label="送检医院">
                      <el-input v-model.trim="form.finspectionHospital" :disabled="disabled" placeholder="请输入" :maxlength="50"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" class="colHeight">
                    <el-form-item label="年龄">
                      <el-input v-model.trim="form.age" disabled placeholder="请输入" :maxlength="50"></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-collapse-item>
              <el-collapse-item :class="{isOpen: activeNames.indexOf('11') !== -1}" title="样本信息" name="11">
                <template slot="title">
                  <div>样本信息</div>
                  <el-checkbox  v-model.trim="isAudit[1]" v-if="type == 2" class="title-checkbox" @change="handleAuditStatusChange">已审</el-checkbox>
                </template>
                <div style="padding: 0 10px;">
                  <el-collapse v-model="sampleInfoActiveNames">
                    <el-collapse-item :class="{isSecOpen: sampleInfoActiveNames.indexOf('111') !== -1}" title="血液样本类型" name="111">
                      <el-row :gutter="10">
                        <el-col :span="12" class="colHeight">
                          <el-form-item label="血液样本类型">
                            <el-select v-model="form.bloodSampleType" :disabled="disabled" clearable placeholder="请选择" class="selectItem">
                              <el-option
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                                v-for="item in bloodSampleTypeList">
                              </el-option>
                            </el-select>
                          </el-form-item>
                        </el-col>
                        <el-col :span="12" v-if="form.bloodSampleType === '其他'" class="colHeight">
                          <el-form-item label="其他血液样本类型">
                            <el-input v-model.trim="form.bloodOtherType" :disabled="disabled" placeholder="请输入"></el-input>
                          </el-form-item>
                        </el-col>
                        <el-col :span="12" class="colHeight">
                          <el-form-item label="样本采集时间">
                            <el-date-picker v-model="form.bloodCollectTime" :disabled="disabled" class="selectItem" clearable type="datetime"  value-format="yyyy-MM-dd HH:mm:ss" placeholder="选择日期时间"></el-date-picker>
                          </el-form-item>
                        </el-col>
                        <el-col :span="12" class="colHeight">
                          <el-form-item label="血液样本量">
                            <el-input v-model.trim="form.bloodMl" :disabled="disabled" placeholder="请输入" style="width: 110px;"><template slot="append">ml</template></el-input>
                            <el-input v-model.trim="form.bloodTube" :disabled="disabled" placeholder="请输入" style="width: 110px;"><template slot="append">管</template></el-input>
                          </el-form-item>
                        </el-col>
                        <el-col :span="12" class="colHeight">
                          <el-form-item label="特殊采血部位">
                            <el-input v-model.trim="form.fspecialTakeBloodPosition" :disabled="disabled" placeholder="请输入"></el-input>
                          </el-form-item>
                        </el-col>
                      </el-row>
                    </el-collapse-item>
                    <el-collapse-item :class="{isSecOpen: sampleInfoActiveNames.indexOf('112') !== -1}" title="体液样本类型" name="112">
                      <el-row :gutter="10">
                        <el-col :span="12" class="colHeight">
                          <el-form-item label="体液样本类型">
                            <el-select v-model="form.humorSampleType" :disabled="disabled" clearable placeholder="请选择" class="selectItem">
                              <el-option
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                                v-for="item in humorSampleTypeList">
                              </el-option>
                            </el-select>
                          </el-form-item>
                        </el-col>
                        <el-col :span="12" v-if="form.humorSampleType === '其他'" class="colHeight">
                          <el-form-item label="其他体液样本类型">
                            <el-input v-model.trim="form.humorOtherType" :disabled="disabled" placeholder="请输入"></el-input>
                          </el-form-item>
                        </el-col>
                        <el-col :span="12" class="colHeight">
                          <el-form-item label="样本采集时间">
                            <el-date-picker v-model="form.humorCollectTime" :disabled="disabled"  clearable type="datetime" value-format="yyyy-MM-dd HH:mm:ss" placeholder="选择日期时间" class="selectItem"></el-date-picker>
                          </el-form-item>
                        </el-col>
                        <el-col :span="12" class="colHeight">
                          <el-form-item label="体液样本量">
                            <el-input v-model.trim="form.humorMl" :disabled="disabled" placeholder="请输入" style="width: 110px;"><template slot="append">ml</template></el-input>
                            <el-input v-model.trim="form.humorTube" :disabled="disabled"  placeholder="请输入" style="width: 110px;"><template slot="append">管</template></el-input>
                          </el-form-item>
                        </el-col>
                      </el-row>
                    </el-collapse-item>
                    <el-collapse-item :class="{isSecOpen: sampleInfoActiveNames.indexOf('113') !== -1}" title="输血史" name="113">
                      <el-row :gutter="10">
                        <el-col :span="12" class="colHeight">
                          <el-form-item label="输血史">
                            <el-select v-model="form.bloodTransHistory" :disabled="disabled" clearable placeholder="请选择" class="selectItem">
                              <el-option
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                                v-for="item in bloodTransHistoryBeanList">
                              </el-option>
                            </el-select>
                          </el-form-item>
                        </el-col>
                        <el-col :span="12" class="colHeight">
                          <el-form-item label="输血时间">
                            <el-date-picker v-model="form.bloodTransDate" :disabled="disabled" clearable type="datetime" value-format="yyyy-MM-dd HH:mm:ss" placeholder="选择日期时间" class="selectItem"></el-date-picker>
                          </el-form-item>
                        </el-col>
                        <el-col :span="12" class="colHeight">
                          <el-form-item label="输血类型">
                            <el-checkbox-group v-model="form.bloodTransType" :disabled="disabled" style="height: 32px;">
                              <el-checkbox label="红细胞"></el-checkbox>
                              <el-checkbox label="血浆"></el-checkbox>
                            </el-checkbox-group>
                          </el-form-item>
                        </el-col>
                        <el-col :span="12" class="colHeight">
                          <el-form-item label="其他">
                            <el-input v-model="form.otherBloodTransType" :disabled="disabled" placeholder="请输入，多个以“,”相隔"></el-input>
                          </el-form-item>
                        </el-col>
                      </el-row>
                    </el-collapse-item>
                    <el-collapse-item :class="{isSecOpen: sampleInfoActiveNames.indexOf('117') !== -1}" title="移植史" name="117">
                      <el-row :gutter="10">
                        <el-col :span="12" class="colHeight">
                          <el-form-item label="移植史">
                            <el-select v-model="form.transplantHistory " :disabled="disabled" clearable placeholder="请选择" class="selectItem">
<!--                              选项和血液史一致故借用-->
                              <el-option
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                                v-for="item in bloodTransHistoryBeanList">
                              </el-option>
                            </el-select>
                          </el-form-item>
                        </el-col>
                        <el-col :span="12" class="colHeight">
                          <el-form-item label="移植时间">
                            <el-date-picker v-model="form.transplantTime" :disabled="disabled" clearable type="datetime" value-format="yyyy-MM-dd HH:mm:ss" placeholder="选择日期时间" class="selectItem"></el-date-picker>
                          </el-form-item>
                        </el-col>
                      </el-row>
                    </el-collapse-item>
                    <el-collapse-item :class="{isSecOpen: sampleInfoActiveNames.indexOf('114') !== -1}" title="组织样本类型" name="114">
                      <el-row :gutter="10">
                        <el-col :span="12" class="colHeight">
                          <el-form-item label="组织样本类型">
                            <el-select v-model="form.tissueSampleType" :disabled="disabled" clearable placeholder="请选择" style="width: calc(100% - 70px)">
                              <el-option
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                                v-for="item in tissueSampleTypeList">
                              </el-option>
                            </el-select>
                            <el-checkbox-group v-model="form.fcryopreservation" :disabled="disabled" style="width: 60px;display: inline-block;height: 32px;">
                              <el-checkbox :label="1">冻存</el-checkbox>
                            </el-checkbox-group>
                          </el-form-item>
                        </el-col>
                        <el-col :span="12" v-if="form.tissueSampleType === '其他'" class="colHeight">
                          <el-form-item label="其他组织样本类型">
                            <el-input v-model.trim="form.tissueOtherType" :disabled="disabled" placeholder="请输入"></el-input>
                          </el-form-item>
                        </el-col>
                        <el-col :span="12" class="colHeight">
                          <el-form-item label="样本采集时间">
                            <el-date-picker v-model="form.tissueCollectTime" :disabled="disabled" clearable type="datetime" value-format="yyyy-MM-dd HH:mm:ss" placeholder="选择日期时间" class="selectItem"></el-date-picker>
                          </el-form-item>
                        </el-col>
                        <el-col :span="24">
                          <el-form-item label="组织样本量">
                            <el-input v-model.trim="form.tissueTube" :disabled="disabled"  placeholder="请输入" style="width: 180px;"><template slot="append">张/块/针/管</template></el-input>
                            <el-input v-model.trim="form.tissueMm" :disabled="disabled"  placeholder="请输入" style="width: 180px;"><template slot="append">厚度(μm)</template></el-input>
                          </el-form-item>
                        </el-col>
                        <el-col :span="12" class="colHeight">
                          <el-form-item label="采集部位">
                            <el-input v-model.trim="form.samplingArea" :disabled="disabled" placeholder="请输入"></el-input>
                          </el-form-item>
                        </el-col>
                        <el-col :span="12" class="colHeight">
                          <el-form-item label="取样方式">
                            <el-select v-model="form.samplingMode" :disabled="disabled" clearable placeholder="请选择" class="selectItem">
                              <el-option
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                                v-for="item in samplingModeList">
                              </el-option>
                            </el-select>
                          </el-form-item>
                        </el-col>
                        <el-col :span="12" v-if="form.samplingMode === '其他'" class="colHeight">
                          <el-form-item label="其他取样方式">
                            <el-input v-model.trim="form.otherSamplingMode" :disabled="disabled" placeholder="请输入"></el-input>
                          </el-form-item>
                        </el-col>
                      </el-row>
                    </el-collapse-item>
                    <el-collapse-item :class="{isSecOpen: sampleInfoActiveNames.indexOf('115') !== -1}" title="细胞样本类型" name="115">
                      <el-row :gutter="10">
                        <el-col :span="12" class="colHeight">
                          <el-form-item label="细胞样本类型">
                            <el-select v-model="form.cellSampleType" :disabled="disabled" clearable placeholder="请选择" class="selectItem">
                              <el-option
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                                v-for="item in cellSampleTypeList">
                              </el-option>
                            </el-select>
                          </el-form-item>
                        </el-col>
                        <el-col :span="12" v-if="form.cellSampleType === '其他'" class="colHeight">
                          <el-form-item label="其他细胞样本类型">
                            <el-input v-model.trim="form.cellOtherType" :disabled="disabled"  placeholder="请输入"></el-input>
                          </el-form-item>
                        </el-col>
                        <el-col :span="12" class="colHeight">
                          <el-form-item label="样本采集时间">
                            <el-date-picker v-model="form.cellCollectTime" :disabled="disabled" clearable type="datetime" value-format="yyyy-MM-dd HH:mm:ss" placeholder="选择日期时间" class="selectItem"></el-date-picker>
                          </el-form-item>
                        </el-col>
                        <el-col :span="12" class="colHeight">
                          <el-form-item label="细胞样本量">
                            <el-input v-model.trim="form.cellMl" :disabled="disabled" placeholder="请输入" style="width: 110px;"><template slot="append">ml</template></el-input>
                            <el-input v-model.trim="form.cellTube" :disabled="disabled"  placeholder="请输入" style="width: 110px;"><template slot="append">管</template></el-input>
                          </el-form-item>
                        </el-col>
                      </el-row>
                    </el-collapse-item>
                    <el-collapse-item :class="{isSecOpen: sampleInfoActiveNames.indexOf('116') !== -1}" title="核酸样本类型" name="116">
                      <el-row :gutter="10">
                        <el-col :span="12" class="colHeight">
                          <el-form-item label="核酸样本类型">
                            <el-select v-model="form.nucleicAcidSampleType" :disabled="disabled" clearable placeholder="请选择" class="selectItem">
                              <el-option
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                                v-for="item in nucleicAcidSampleTypeList">
                              </el-option>
                            </el-select>
                          </el-form-item>
                        </el-col>
                        <el-col :span="12" v-if="form.nucleicAcidSampleType === '其他'" class="colHeight">
                          <el-form-item label="其他核酸样本类型">
                            <el-input v-model.trim="form.nucleicAcidOtherType" :disabled="disabled" placeholder="请输入"></el-input>
                          </el-form-item>
                        </el-col>
                        <el-col :span="12" class="colHeight">
                          <el-form-item label="样本采集时间">
                            <el-date-picker v-model="form.nucleicAcidCollectTime" :disabled="disabled" clearable value-format="yyyy-MM-dd HH:mm:ss" type="datetime" placeholder="选择日期时间" class="selectItem"></el-date-picker>
                          </el-form-item>
                        </el-col>
                        <el-col :span="12" class="colHeight">
                          <el-form-item label="核酸样本量">
                            <el-input v-model.trim="form.nucleicAcidMl" :disabled="disabled" placeholder="请输入" style="width: 110px;"><template slot="append">ml</template></el-input>
                            <el-input v-model.trim="form.nucleicAcidTube" :disabled="disabled" placeholder="请输入" style="width: 110px;"><template slot="append">管</template></el-input>
                          </el-form-item>
                        </el-col>
                      </el-row>
                    </el-collapse-item>
                  </el-collapse>
                </div>
                <el-row :gutter="10" style="padding-top: 20px;">
                  <el-col :span="12" class="colHeight">
                    <el-form-item label="病理号">
                      <el-input v-model.trim="form.fpathologyNumber" :disabled="disabled" placeholder="请输入"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" class="colHeight">
                    <el-form-item label="病历号">
                      <el-input v-model.trim="form.fmedicalRecordNum" :disabled="disabled" placeholder="请输入"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" class="colHeight">
                    <el-form-item label="患者来源">
                      <el-input v-model.trim="form.fpatientSource" :disabled="disabled" placeholder="请输入"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" class="colHeight">
                    <el-form-item label="床号">
                      <el-input v-model.trim="form.fbedNum" :disabled="disabled" placeholder="请输入"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" class="colHeight">
                    <el-form-item label="送检次数">
                      <el-input v-model.trim="form.detectionTimes" :disabled="disabled" placeholder="请输入"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" class="colHeight">
                    <el-form-item label="上次送检类型">
                      <el-input v-model.trim="form.lastSampleType" :disabled="disabled" placeholder="请输入"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" class="colHeight">
                    <el-form-item label="上次送检编号">
                      <el-input v-model.trim="form.lastSampleNum" :disabled="disabled" placeholder="请输入"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" class="colHeight">
                    <el-form-item label="样本采集人">
                      <el-input v-model.trim="form.sampleCollectMan" disabled  placeholder="请输入"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24" class="colHeight">
                    <el-form-item label="备注">
                      <el-input v-model.trim="form.remark" disabled  placeholder="请输入"></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-collapse-item>
              <el-collapse-item :class="{isOpen: activeNames.indexOf('10') !== -1}" v-if="isShowSanger(form.productName)" title="Sanger验证位点" name="10">
                <template slot="title">
                  <div>Sanger验证位点</div>
                  <el-checkbox  v-model.trim="isAudit[2]" v-if="type == 2" class="title-checkbox" @change="handleAuditStatusChange">已审</el-checkbox>
                </template>
                <div v-if="form.productName === 'OncoH-Sanger验证' || form.productName === 'OncoH-CNV验证'" class="item">
                  <el-form ref="sangerForm" :model="sangerForm" :inline="true" label-width="100px" size="mini" label-suffix=":">
                    <el-form-item v-if="form.productName === 'OncoH-Sanger验证'" label="先证者编号">
                      <el-input v-model.trim="sangerForm.sampleNum" :disabled="disabled" placeholder="请输入"></el-input>
                    </el-form-item>
                    <el-form-item>
                      <template v-if="form.productName === 'OncoH-Sanger验证'">
                        <el-button :disabled="disabled" type="primary" @click="handleSearchSanger">查询</el-button>
                        <el-button :disabled="disabled" type="primary" @click="handleReset">重置</el-button>
                        <el-button :disabled="disabled" type="primary" @click="handleCopy">复制位点</el-button>
                      </template>
                      <el-button :disabled="disabled" type="primary" @click="handleAddSanger">添加</el-button>
                      <el-button :disabled="disabled" type="primary" @click="handleDeleteSanger">删除</el-button>
                    </el-form-item>
                  </el-form>
                </div>
                <div>
                  <el-table
                    :data="sangerTableData"
                    ref="sangerTable" border
                    class="sangerTable" size="mini"
                    height="400"
                    style="width: 100%"
                    @row-click="(row, column, event) => handleRowClick(row, column, event, 'sanger')"
                    @select="(selection, row) => handleSelectTable(selection, row, 'sanger')"
                    @select-all="selection => handleSelectAll(selection, 'sanger')">
                    <el-table-column type="selection"></el-table-column>
                    <template v-if="form.productName === 'OncoH-CNV验证'">
                      <el-table-column key="H-report" prop="report" label="标记检测" width="100">
                        <template slot-scope="scope">
                          <el-button :disabled="disabled" type="text" @click="handleChangeReport(scope.row)">{{scope.row.report}}</el-button>
                        </template>
                      </el-table-column>
                      <el-table-column key="cnv-gene" prop="gene" label="基因" min-width="180" show-overflow-tooltip></el-table-column>
                      <el-table-column key="cnv-nucleotideMutation" prop="nucleotideMutation" label="碱基改变" min-width="180" show-overflow-tooltip></el-table-column>
                      <el-table-column key="cnv-aminoAcidMutation" prop="aminoAcidMutation" label="氨基酸改变" min-width="200" show-overflow-tooltip></el-table-column>
                      <el-table-column key="cnv-referenceSequence" prop="referenceSequence" label="转录本" min-width="180" show-overflow-tooltip></el-table-column>
                      <el-table-column key="cnv-exon" prop="exon" label="功能区域" min-width="180" show-overflow-tooltip></el-table-column>
                      <el-table-column key="cnv-chr" prop="chr" label="Chr" min-width="180" show-overflow-tooltip></el-table-column>
                      <el-table-column key="cnv-start" prop="start" label="Start" min-width="180" show-overflow-tooltip></el-table-column>
                      <el-table-column key="cnv-end" prop="end" label="End" min-width="180" show-overflow-tooltip></el-table-column>
                      <el-table-column key="cnv-ref" prop="ref" label="Ref" min-width="180" show-overflow-tooltip></el-table-column>
                      <el-table-column key="cnv-alt" prop="alt" label="Alt" min-width="180" show-overflow-tooltip></el-table-column>
                      <el-table-column key="cnv-status" prop="status" label="status" min-width="180" show-overflow-tooltip></el-table-column>
                      <el-table-column key="cnv-statusRatio" prop="statusRatio" label="statusRatio" min-width="180" show-overflow-tooltip></el-table-column>
                    </template>
                    <template v-if="form.productName === 'OncoH-Sanger验证'">
                      <el-table-column key="H-report" prop="report" label="标记检测" width="100">
                        <template slot-scope="scope">
                          <el-button :disabled="disabled" type="text" @click="handleChangeReport(scope.row)">{{scope.row.report}}</el-button>
                        </template>
                      </el-table-column>
                      <el-table-column key="H-gene" prop="gene" label="基因" min-width="180" show-overflow-tooltip></el-table-column>
                      <el-table-column key="H-nucleotideMutation" prop="nucleotideMutation" label="碱基改变" min-width="180" show-overflow-tooltip></el-table-column>
                      <el-table-column key="H-aminoAcidMutation" prop="aminoAcidMutation" label="氨基酸改变" min-width="200" show-overflow-tooltip></el-table-column>
                      <el-table-column key="H-referenceSequence" prop="referenceSequence" label="转录本" min-width="180" show-overflow-tooltip></el-table-column>
                      <el-table-column key="H-exon" prop="exon" label="功能区域" min-width="180" show-overflow-tooltip></el-table-column>
                    </template>
                    <template v-else>
                      <el-table-column key="other-report" prop="report" label="标记检测" width="100"></el-table-column>
                      <el-table-column key="other-gene" prop="gene" label="基因" min-width="180" show-overflow-tooltip></el-table-column>
                      <el-table-column key="other-nucleotideMutation" prop="nucleotideMutation" label="碱基改变" min-width="180" show-overflow-tooltip></el-table-column>
                      <el-table-column key="other-aminoAcidMutation" prop="aminoAcidMutation" label="氨基酸改变" min-width="200" show-overflow-tooltip></el-table-column>
                      <el-table-column key="other-referenceSequence" prop="referenceSequence" label="转录本" min-width="180" show-overflow-tooltip></el-table-column>
                      <el-table-column key="other-exon" prop="exon" label="功能区域" min-width="180" show-overflow-tooltip></el-table-column>
                      <el-table-column key="other-chr" prop="chr" label="Chr" min-width="180" show-overflow-tooltip></el-table-column>
                      <el-table-column key="other-start" prop="start" label="Start" min-width="180" show-overflow-tooltip></el-table-column>
                      <el-table-column key="other-end" prop="end" label="End" min-width="180" show-overflow-tooltip></el-table-column>
                      <el-table-column key="other-ref" prop="ref" label="Ref" min-width="180" show-overflow-tooltip></el-table-column>
                      <el-table-column key="other-alt" prop="alt" label="Alt" min-width="180" show-overflow-tooltip></el-table-column>
                    </template>
                  </el-table>
                </div>
              </el-collapse-item>
              <el-collapse-item :class="{isOpen: activeNames.indexOf('2') !== -1}" title="病历小结" name="2">
                <template slot="title">
                  <div>病历小结</div>
                  <el-checkbox  v-model.trim="isAudit[3]" v-if="type == 2" class="title-checkbox" @change="handleAuditStatusChange">已审</el-checkbox>
                </template>
                <div style="text-align: right;margin: 10px 0;">
                  <el-button :disabled="disabled" size="mini"  type="danger" @click="handleAutoGetData">自动获取</el-button>
                </div>
                <div style="margin: 0 20px;">
                  <table class="resultTable">
                    <tr>
                      <th>受检者信息</th>
                      <th>样本信息</th>
                      <th>送检信息</th>
                    </tr>
                    <tr>
                      <td>姓名：{{form.name}}</td>
                      <td>样本编号：{{form.sampleNum}}</td>
                      <td>送检单位：{{form.inspectionUnitName}}</td>
                    </tr>
                    <tr>
                      <td>性别：{{form.sex === 0 ? '男' : form.sex === 1 ? '女' : ''}}</td>
                      <td>样本类型：{{sampleTotalType}}</td>
                      <td>送检医生：{{form.doctor}}</td>
                    </tr>
                    <tr>
                      <td>出生年月：{{form.birthday}}</td>
                      <td>样本采集日期：{{sampleCollectTime}}</td>
                      <td>送检项目：{{form.productName}}</td>
                    </tr>
                    <tr>
                      <td>证件号：{{form.idcard}}</td>
                      <td>样本接收日期：</td>
                      <td></td>
                    </tr>
                    <tr>
                      <td>联系电话：{{form.contactTel}}  {{form.familyContactType}}</td>
                      <td>报告日期：</td>
                      <td></td>
                    </tr>
                    <tr>
                      <td colspan="3">临床诊断：{{form.clinicalDiagnose}}</td>
                    </tr>
                    <tr>
                      <td colspan="3">治疗史：
                        <div v-if="form.operationHistory" style="padding: 0 10px;display: flex">
                          <div style="flex-shrink: 0;">手术史:</div>
                          <div v-html="form.operationHistory"></div>
                        </div>
                        <div v-if="form.medicationHistory" style="padding: 0 10px;display: flex">
                          <div style="flex-shrink: 0;">用药史:</div>
                          <div v-html="form.medicationHistory"></div>
                        </div>
                        <div v-if="form.irrationalHistory" style="padding: 0 10px;display: flex">
                          <div style="flex-shrink: 0;">放疗史:</div>
                          <div v-html="form.irrationalHistory"></div>
                        </div>
                      </td>
                    </tr>
                  </table>
                </div>
              </el-collapse-item>
              <el-collapse-item :class="{isOpen: activeNames.indexOf('3') !== -1}" title="个人病情" name="3">
                <template slot="title">
                  <div>个人病情</div>
                  <el-checkbox  v-model.trim="isAudit[4]" v-if="type == 2" class="title-checkbox" @change="handleAuditStatusChange">已审</el-checkbox>
                </template>
                <el-form-item label="临床信息统计">
                  <el-radio-group v-model="form.isAll" :disabled="disabled">
                    <el-radio label="完整">完整</el-radio>
                    <el-radio label="不完整">不完整</el-radio>
                  </el-radio-group>
                  <el-checkbox-group v-model="form.clinDetail" :disabled="disabled" v-if="form.isAll === '不完整'">
                    <el-checkbox label="癌种(病理)"></el-checkbox>
                    <el-checkbox label="确诊时间(年龄或日期)"></el-checkbox>
                    <el-checkbox label="临床分期"></el-checkbox>
                    <el-checkbox label="转移部位"></el-checkbox>
                    <el-checkbox label="治疗史"></el-checkbox>
                    <el-checkbox label="MRD手术方式"></el-checkbox>
                    <el-checkbox label="MRD手术分期"></el-checkbox>
                    <el-checkbox label="乳腺癌分子分型"></el-checkbox>
                    <el-checkbox label="家族史"></el-checkbox>
                    <el-checkbox label="吸烟史"></el-checkbox>
                    <el-checkbox label="临床信息缺失"></el-checkbox>
                  </el-checkbox-group>
                </el-form-item>
                <el-form-item label="主诉">
                  <el-input v-model.trim="form.mainSuit" type="textarea" rows="5" resize="none" placeholder="请输入主诉" disabled></el-input>
                </el-form-item>
                <el-form-item label="拍照内容">
                  <el-checkbox-group v-model="form.photoContent" :disabled="disabled">
                    <el-checkbox label="出院记录"></el-checkbox>
                    <el-checkbox label="入院记录"></el-checkbox>
                    <el-checkbox label="病理诊断报告"></el-checkbox>
                    <el-checkbox label="基因检测结果"></el-checkbox>
                    <el-checkbox label="影像检查"></el-checkbox>
                    <el-checkbox label="肿瘤标记物报告"></el-checkbox>
                    <el-checkbox label="其他"></el-checkbox>
                  </el-checkbox-group>
                  <el-input v-model="form.otherPhotoContent" :disabled="disabled"
                            v-if="form.photoContent.indexOf('其他') !== -1"
                            maxlength="20"
                            placeholder="请输入其他"
                            style="width: 180px;"></el-input>
                </el-form-item>
                <el-row :gutter="10">
                  <el-col :span="10">
                    <el-form-item label="病案号">
                      <el-input v-model.trim="form.medicalRecord" :disabled="disabled"  placeholder="请输入" maxlength="20"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="14">
                    <el-form-item label="记录日期">
                      <my-date-picker v-model="form.recordDate" :is-disabled="disabled"></my-date-picker>
                      <!--<el-date-picker v-model="form.time" type="date" value-format="yyyy-MM-dd" placeholder="请选择日期"></el-date-picker>-->
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-form-item label="临床症状">
                  <el-input v-model.trim="form.clinicalDiagnoseMedical" :disabled="disabled" type="textarea" rows="5" resize="none" placeholder="请输入临床症状"></el-input>
                </el-form-item>
                <el-row :gutter="10">
                  <el-col :span="10">
                    <el-form-item label="体力状况">
                      <el-select v-model="form.performanceStatus" :disabled="disabled" placeholder="请选择">
                        <el-option
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                          v-for="item in performanceStatusList">
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="14">
                    <el-form-item label="评估日期">
                      <my-date-picker v-model="form.evaluationDate" :is-disabled="disabled"></my-date-picker>
                      <!--<el-date-picker v-model="form.time" type="date" value-format="yyyy-MM-dd" placeholder="请选择日期"></el-date-picker>-->
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-form-item label="备注">
                  <el-input v-model.trim="form.evaluationMark" :disabled="disabled" type="textarea" rows="5" resize="none" placeholder="请输入备注"></el-input>
                </el-form-item>
              </el-collapse-item>
              <el-collapse-item :class="{isOpen: activeNames.indexOf('4') !== -1}" title="癌种信息" name="4">
                <template slot="title">
                  <div>癌种信息</div>
                  <el-checkbox  v-model.trim="isAudit[5]" v-if="type == 2" class="title-checkbox" @change="handleAuditStatusChange">已审</el-checkbox>
                </template>
                <el-row :gutter="10">
                 <el-col :span="10" class="redStyle">
                   <el-form-item label="癌种标签" label-width="80px">
                     <el-select v-model="form.cancerLabel" :disabled="disabled" clearable placeholder="请选择" class="selectItem">
                       <el-option
                         :key="item.value"
                         :label="item.label"
                         :value="item.value"
                         v-for="item in cancerLabelList">
                       </el-option>
                     </el-select>
                   </el-form-item>
                 </el-col>
                 <el-col :span="14" class="redStyle">
                   <el-form-item label="解读匹配癌种" label-width="120px" prop="readCancerId">
                     <el-select v-model="form.readCancerId" :disabled="disabled" clearable multiple filterable placeholder="请选择" class="selectItem">
                       <el-option
                         :key="item.value"
                         :label="item.label"
                         :value="item.value"
                         v-for="item in cancerList">
                       </el-option>
                     </el-select>
                   </el-form-item>
                 </el-col>
                </el-row>
                <div style="display: flex; justify-content: space-between;padding: 0 10px;border-bottom: 1px solid #DCDFE6; height: 40px; align-items: center;">
                  <span style="color: #F56C6C;">诊断癌种</span>
                  <el-button :disabled="disabled" type="primary" size="mini" @click="handleAddCancerInfo">+添加</el-button>
                </div>
                <template v-for="(item, index) in cancerInfoList">
                  <div :key="index" style="padding:  10px 10px 0;">
                    <el-row :gutter="10">
                      <el-col :span="8" class="item">
                        诊断：{{item.diagnosCancer}}
                      </el-col>
                      <el-col :span="8" class="item">
                        病理诊断：{{item.pathologicDiagnosis }}
                      </el-col>
                      <el-col :span="8" style="text-align: right;" class="item">
                        <el-button :disabled="disabled" type="primary" size="mini" @click="handleCancerEdit(item)">编辑</el-button>
                        <el-button :disabled="disabled" type="danger" size="mini" @click="handleCancerDelete(item)">删除</el-button>
                      </el-col>
                      <el-col :span="24" class="item">
                        癌种：{{item.cancerTypeAllName}}
                      </el-col>
                      <el-col :span="8" class="item">
                        确诊年龄：{{item.diagnosAge}}
                      </el-col>
                      <el-col :span="8" class="item">
                        确诊日期：{{item.diagnosTime}}
                      </el-col>
                      <el-col :span="8" class="item">
                        复发日期：{{item.recrudescenceTime}}
                      </el-col>
                      <el-col :span="8" class="item">
                        TNM分期：T{{item.tnmTstatges}};N{{item.tnmNstatges}};M{{item.tnmMstatges}}
                      </el-col>
                      <el-col :span="8" class="item">
                        目前临床分期：{{item.clinicalStages}}
                      </el-col>
                      <el-col :span="24" class="item">
                        转移部位：
                        <el-checkbox-group v-model="item.cancerMetastasisSites" disabled>
                          <el-checkbox label="骨"></el-checkbox>
                          <el-checkbox label="脑"></el-checkbox>
                          <el-checkbox label="肝"></el-checkbox>
                          <el-checkbox label="肺"></el-checkbox>
                          <el-checkbox label="淋巴结"></el-checkbox>
                          <el-checkbox label="无"></el-checkbox>
                          <el-checkbox label="不详"></el-checkbox>
                          <el-checkbox label="其他">
                            其他 <el-input v-model.trim="item.otherCancerMetastasisSites" v-show="item.cancerMetastasisSites.indexOf('其他') !== -1" disabled size="mini" placeholder="请输入"></el-input>
                          </el-checkbox>
                        </el-checkbox-group>
                      </el-col>
                      <el-col :span="12" class="item">
                        有无病理：<span :style="`${item.pathology === 1 ? 'color: red;' : ''}`">{{item.pathology === 0 ? '无' : item.pathology === 1 ? '有' : ''}}</span>
                      </el-col>
                      <el-col :span="12" v-if="item.pathology === 1" class="item"  style="text-align: right;">
                        <el-button :disabled="disabled" type="primary" size="mini" @click="handleClinicalPathologyEdit(null, item.sampleClinicalCancerInfoId)">+添加</el-button>
                      </el-col>
                      <el-col :span="24">
                        <template v-for="(items, indexs) in item.clinicalPathologyList">
                          <div :key="'clinicalPathology' + indexs" style="padding: 10px 10px 0;">
                            <el-row :gutter="10">
                              <el-col :span="12" class="item">
                                病灶部位：{{items.checkPosition}}
                              </el-col>
                              <el-col :span="12" class="item" style="text-align: right;">
                                <el-button :disabled="disabled" size="mini" type="primary" @click="handleClinicalPathologyEdit(items, item.sampleClinicalCancerInfoId)">编辑</el-button>
                                <el-button :disabled="disabled" size="mini" type="danger" @click="handleClinicalPathologyDelete(items)">删除</el-button>
                              </el-col>
                              <el-col :span="12" class="item">
                                检测日期：{{items.checkTime}}
                              </el-col>
                              <el-col :span="12" class="item">
                                病理结果：{{items.checkResult}}
                              </el-col>
                              <el-col :span="12" class="item">
                                淋巴结转移：{{items.lymphTransfer}}
                              </el-col>
                              <el-col :span="12" class="item">
                                淋巴结内容：{{items.lymphNum1}}/{{items.lymphNum2}};{{items.lymphRange}}
                              </el-col>
                              <el-col :span="12" class="item">
                                乳腺癌分子亚型：{{items.breastCancerMoleculeSubtype}}
                              </el-col>
                              <el-col :span="24" class="item">
                                信息来源：{{items.sourceImgs}}
                              </el-col>
                            </el-row>
                          </div>
                        </template>
                      </el-col>
                    </el-row>
                  </div>
                </template>
              </el-collapse-item>
              <el-collapse-item :class="{isOpen: activeNames.indexOf('5') !== -1}" title="检测情况" name="5">
                <template slot="title">
                  <div>检测情况</div>
                  <el-checkbox  v-model.trim="isAudit[6]" v-if="type == 2" class="title-checkbox" @change="handleAuditStatusChange">已审</el-checkbox>
                </template>
                <div style="padding: 0 10px;">
                  <el-collapse v-model="detectInfoActiveNames" @change="handleChange">
                    <el-collapse-item title="免疫组化" name="51">
                      <div style="display: flex;" class="item">
                        <div>免疫组化：</div>
                        <div style="flex: 1;">
                          <!--<el-radio-group v-model="form.immune" @change="value => handleRadioChange(value, 'immune')">-->
                          <el-radio-group v-model="form.immune" :disabled="disabled">
                            <el-radio :label="1">有</el-radio>
                            <el-radio :label="0">无</el-radio>
                            <el-radio :label="2">不详</el-radio>
                          </el-radio-group>
                        </div>
                        <div v-if="form.immune === 1">
                          <el-button :disabled="disabled" type="primary" size="mini" @click="handleDetectInfoEdit(null, 'immuneHistos')">+添加</el-button>
                        </div>
                      </div>
                      <template v-if="form.immune === 1">
                        <template v-for="(item, index) in immuneHistos">
                          <div :key="'immuneHistos' + index">
                            <el-row :gutter="10">
                              <el-col :span="24" class="item" style="text-align: right;">
                                <el-button :disabled="disabled" size="mini" type="primary" @click="handleDetectInfoEdit(item, 'immuneHistos')">编辑</el-button>
                                <el-button :disabled="disabled" size="mini" type="danger" @click="handleDetectDelete(item, 'immuneHistos')">删除</el-button>
                              </el-col>
                              <el-col :span="8" class="item">
                                检测日期：{{item.detectTime}}
                              </el-col>
                              <el-col :span="8" class="item">
                                病灶部位：{{item.focusPosition}}
                              </el-col>
                              <template v-for="(items, indexs) in item.projects">
                                <div :key="'testProject' + indexs">
                                  <el-col :span="8" class="item"></el-col>
                                  <el-col :span="8" class="item">
                                    检测项目：{{items.project}}
                                  </el-col>
                                  <el-col :span="8" class="item">
                                    检测结果：{{items.result}}
                                  </el-col>
                                </div>
                              </template>
                            </el-row>
                          </div>
                        </template>
                      </template>
                    </el-collapse-item>
                    <el-collapse-item title="基因检测" name="52">
                      <div style="display: flex;" class="item">
                        <div>基因检测：</div>
                        <div style="flex: 1;">
                          <!--<el-radio-group v-model="form.geneTest" @change="value => handleRadioChange(value, 'geneTest')">-->
                          <el-radio-group v-model="form.geneTest" :disabled="disabled">
                            <el-radio :label="1">有</el-radio>
                            <el-radio :label="0">无</el-radio>
                            <el-radio :label="2">不详</el-radio>
                          </el-radio-group>
                        </div>
                        <div v-if="form.geneTest === 1">
                          <el-button :disabled="disabled" type="primary" size="mini" @click="handleDetectInfoEdit(null, 'geneTest')">+添加</el-button>
                        </div>
                      </div>
                      <template v-if="form.geneTest === 1">
                        <template v-for="(item, index) in geneTest">
                          <div :key="'geneTest' + index">
                            <el-row :gutter="10">
                              <el-col :span="24" class="item" style="text-align: right;">
                                <el-button :disabled="disabled" size="mini" type="primary" @click="handleDetectInfoEdit(item, 'geneTest')">编辑</el-button>
                                <el-button :disabled="disabled" size="mini" type="danger" @click="handleDetectDelete(item, 'geneTest')">删除</el-button>
                              </el-col>
                              <el-col :span="8" class="item">
                                检测日期：{{item.detectTime}}
                              </el-col>
                              <el-col :span="8" class="item">
                                检测方法：{{item.detectMethod}}
                              </el-col>
                              <template v-for="(items, indexs) in item.detail">
                                <div :key="'detail' + indexs">
                                  <el-col :span="8" class="item"></el-col>
                                  <el-col :span="8" class="item">
                                    检测基因：{{items.gene}}
                                  </el-col>
                                  <el-col :span="8" class="item">
                                    检测结果：{{items.result}}
                                  </el-col>
                                </div>
                              </template>
                            </el-row>
                          </div>
                        </template>
                      </template>
                    </el-collapse-item>
                    <el-collapse-item title="影像检查" name="53">
                      <div style="display: flex;" class="item">
                        <div>影像检查：</div>
                        <div style="flex: 1;">
                          <!--<el-radio-group v-model="form.iconography" @change="value => handleRadioChange(value, 'iconography')">-->
                          <el-radio-group v-model="form.iconography" :disabled="disabled">
                            <el-radio :label="1">有</el-radio>
                            <el-radio :label="0">无</el-radio>
                            <el-radio :label="2">不详</el-radio>
                          </el-radio-group>
                        </div>
                        <div v-if="form.iconography === 1">
                          <el-button :disabled="disabled" type="primary" size="mini" @click="handleDetectInfoEdit(null, 'iconography')">+添加</el-button>
                        </div>
                      </div>
                      <template v-if="form.iconography === 1">
                        <template v-for="(item, index) in iconography">
                          <div :key="'iconography' + index">
                            <el-row :gutter="10">
                              <el-col :span="24" class="item" style="text-align: right;">
                                <el-button :disabled="disabled" size="mini" type="primary" @click="handleDetectInfoEdit(item, 'iconography')">编辑</el-button>
                                <el-button :disabled="disabled" size="mini" type="danger" @click="handleDetectDelete(item, 'iconography')">删除</el-button>
                              </el-col>
                              <template v-if="item.targetSpot === 0">
                                <el-col :span="8" class="item">
                                  检测日期：{{item.checkTime}}
                                </el-col>
                                <el-col :span="8" class="item">
                                  检测方式：{{item.checkProject || item.otherProject}}
                                </el-col>
                                <el-col :span="8" class="item">
                                  靶病灶所在器官：{{item.checkPostion || item.checkPostion1}}
                                </el-col>
                                <el-col :span="8" class="item">
                                  靶病灶最长直径(LD)：{{item.targetSpotLength}}
                                </el-col>
                              </template>
                              <template v-else-if="item.targetSpot === 1">
                                <el-col :span="8" class="item">
                                  检测日期：{{item.checkTime}}
                                </el-col>
                                <el-col :span="8" class="item">
                                  检测方式：{{item.checkProject || item.otherProject}}
                                </el-col>
                                <el-col :span="8" class="item">
                                  靶病灶所在器官：{{item.checkPostion || item.checkPostion1}}
                                </el-col>
                                <el-col :span="8" class="item">
                                  描述：{{item.targetSpotDes}}
                                </el-col>
                              </template>
                              <template v-else>
                                <el-col :span="8" class="item">
                                  检测部位：{{item.checkArea}}
                                </el-col>
                                <el-col :span="16" class="item">
                                  检测时间：{{item.checkTime}}
                                </el-col>
                                <el-col :span="8" class="item">
                                  检查项目：{{item.checkProject}}
                                </el-col>
                                <el-col :span="16" class="item">
                                  检查结果：{{item.checkResult}}
                                </el-col>
                                <el-col :span="8" v-show="item.otherProject" class="item">
                                  检查项目：{{item.otherProject}}
                                </el-col>
                                <el-col :span="8" v-show="item.checkOtherResult" class="item">
                                  检查结果：{{item.checkOtherResult}}
                                </el-col>
                                <el-col :span="8" v-show="item.diseaseTail" class="item">
                                  检查结果：{{item.diseaseTail}}
                                </el-col>
                                <template v-if="item.checkArea === '肺部' && item.checkResult === '结节'">
                                  <el-col :span="24" class="item">外部特征</el-col>
                                  <el-col :span="8" class="item" style="padding: 0 15px;">
                                    个数：{{item.num}}
                                  </el-col>
                                  <el-col :span="8" class="item" style="padding: 0 15px;">
                                    突出病灶：{{item.protrudingLesions}}
                                  </el-col>
                                  <el-col :span="8" class="item" style="padding: 0 15px;">
                                    大小：{{item.size}}mm
                                  </el-col>
                                  <el-col :span="8" class="item" style="padding: 0 15px;">
                                    位置：{{item.location}}
                                  </el-col>
                                  <el-col :span="8" class="item" style="padding: 0 15px;">
                                    形态：{{item.form}}
                                  </el-col>
                                  <el-col :span="8" class="item" style="padding: 0 15px;">
                                    边缘：{{item.margin}}
                                  </el-col>
                                  <el-col :span="8" class="item" style="padding: 0 15px;">
                                    结节-肺界面：{{item.nodeLung}}
                                  </el-col>
                                  <el-col :span="24" class="item">内部特征</el-col>
                                  <el-col :span="8" class="item" style="padding: 0 15px;">
                                    密度：{{item.density}}
                                  </el-col>
                                  <el-col :span="8" v-if="item.density === '亚实性'" class="item" style="padding: 0 15px;">
                                    {{item.densityValue}}
                                  </el-col>
                                  <el-col :span="8" v-if="item.density === '亚实性'" class="item" style="padding: 0 15px;">
                                    实性成分{{item.element}}
                                  </el-col>
                                </template>
                              </template>
                            </el-row>
                          </div>
                        </template>
                      </template>
                    </el-collapse-item>
                    <el-collapse-item title="肿瘤标记物" name="54">
                      <div style="display: flex;" class="item">
                        <div>肿瘤标记物：</div>
                        <div style="flex: 1;">
                          <!--<el-radio-group v-model="form.cancerTag" @change="value => handleRadioChange(value, 'cancerTag')">-->
                          <el-radio-group v-model="form.cancerTag" :disabled="disabled">
                            <el-radio :label="1">有</el-radio>
                            <el-radio :label="0">无</el-radio>
                            <el-radio :label="2">不详</el-radio>
                          </el-radio-group>
                        </div>
                        <div v-if="form.cancerTag === 1">
                          <el-button :disabled="disabled" type="primary" size="mini" @click="handleDetectInfoEdit(null, 'cancerTag')">+添加</el-button>
                        </div>
                      </div>
                      <template v-if="form.cancerTag === 1">
                        <template v-for="(item, index) in cancerTag">
                          <div :key="'cancerTag' + index" style="padding: 10px;">
                            <el-row :gutter="10">
                              <el-col :span="12" class="item">
                                检测时间：{{item.checkTime}}
                              </el-col>
                              <el-col :span="12" class="item" style="text-align: right;">
                                <el-button :disabled="disabled" size="mini" type="primary" @click="handleDetectInfoEdit(item, 'cancerTag')">编辑</el-button>
                                <el-button :disabled="disabled" size="mini" type="danger" @click="handleDetectDelete(item, 'cancerTag')">删除</el-button>
                              </el-col>
                              <template v-for="(items, indexs) in item.details">
                                <div :key="'details' + indexs">
                                  <el-col :span="16" class="item">
                                    {{items.cancerIndex}}：{{items.detectIndexValueSymbol}}{{items.cancerIndexValue}}{{items.unitV}}({{items.cancerIndexValueScope}})
                                  </el-col>
                                  <el-col :span="8" class="item">
                                    临床判断：{{items.clinicalJudgement === 1 ? '异常无临床意义' : items.clinicalJudgement === 2 ? '异常且有临床意义' : items.clinicalJudgement === 3 ? '正常' : ''}}
                                  </el-col>
                                </div>
                              </template>
                            </el-row>
                          </div>
                        </template>
                      </template>
                    </el-collapse-item>
                    <el-collapse-item title="生化检查" name="55">
                      <div style="display: flex;" class="item">
                        <div>生化检查：</div>
                        <div style="flex: 1;">
                          <!--<el-radio-group v-model="form.biocheExamTag" @change="value => handleRadioChange(value, 'biocheExamTag')">-->
                          <el-radio-group v-model="form.biocheExamTag" :disabled="disabled">
                            <el-radio :label="1">有</el-radio>
                            <el-radio :label="0">无</el-radio>
                            <el-radio :label="2">不详</el-radio>
                          </el-radio-group>
                        </div>
                        <div v-if="form.biocheExamTag === 1">
                          <el-button :disabled="disabled" type="primary" size="mini" @click="handleDetectInfoEdit(null, 'biocheExamTag')">+添加</el-button>
                        </div>
                      </div>
                      <template v-if="form.biocheExamTag === 1">
                        <template v-for="(item, index) in biocheExamTag">
                          <div :key="'biocheExamTag' + index">
                            <el-row :gutter="10">
                              <el-col :span="24" class="item" style="text-align: right;">
                                <el-button :disabled="disabled" size="mini" type="primary" @click="handleDetectInfoEdit(item, 'biocheExamTag')">编辑</el-button>
                                <el-button :disabled="disabled" size="mini" type="danger" @click="handleDetectDelete(item, 'biocheExamTag')">删除</el-button>
                              </el-col>
                              <el-col :span="8" class="item">
                                检测日期{{item.detectTime}}
                              </el-col>
                              <el-col :span="8" class="item">
                                检测方法：{{item.detectMethod}}
                              </el-col>
                              <el-col :span="8" class="item"></el-col>
                              <template v-for="(items, indexs) in item.biocheDtl">
                                <div :key="'biocheDtl' + indexs">
                                  <el-col :span="8" class="item">
                                    指标：{{items.detectIndex}}
                                  </el-col>
                                  <el-col :span="8" class="item">
                                    指标值：{{items.detectIndexValue}}
                                  </el-col>
                                  <el-col :span="8" class="item">
                                    单位：{{items.detectUnit}}
                                  </el-col>
                                </div>
                              </template>
                            </el-row>
                          </div>
                        </template>
                      </template>
                    </el-collapse-item>
                    <el-collapse-item title="性激素" name="56">
                      <div style="display: flex;" class="item">
                        <div>性激素：</div>
                        <div style="flex: 1;">
                          <!--<el-radio-group v-model="form.sexhormoneTag" @change="value => handleRadioChange(value, 'sexhormoneTag')">-->
                          <el-radio-group v-model="form.sexhormoneTag" :disabled="disabled">
                            <el-radio :label="1">有</el-radio>
                            <el-radio :label="0">无</el-radio>
                            <el-radio :label="2">不详</el-radio>
                          </el-radio-group>
                        </div>
                        <div v-if="form.sexhormoneTag === 1">
                          <el-button :disabled="disabled" type="primary" size="mini" @click="handleDetectInfoEdit(null, 'sexhormoneTag')">+添加</el-button>
                        </div>
                      </div>
                      <template v-if="form.sexhormoneTag === 1">
                        <template v-for="(item, index) in sexhormoneTag">
                          <div :key="'sexhormoneTag' + index">
                            <el-row :gutter="10">
                              <el-col :span="24" class="item" style="text-align: right;">
                                <el-button :disabled="disabled" size="mini" type="primary" @click="handleDetectInfoEdit(item, 'sexhormoneTag')">编辑</el-button>
                                <el-button :disabled="disabled" size="mini" type="danger" @click="handleDetectDelete(item, 'sexhormoneTag')">删除</el-button>
                              </el-col>
                              <el-col :span="8" class="item">
                                检测时间：{{item.detectTime}}
                              </el-col>
                              <el-col :span="8" class="item">
                                检测方法：{{item.detectMethod}}
                              </el-col>
                              <el-col :span="8" class="item">
                                检测结果：{{item.detectResult}}
                              </el-col>
                              <template v-for="(items, indexs) in item.sexhorDtl">
                                <div :key="'sexhorDtl' + indexs">
                                  <el-col :span="8" class="item">
                                    指标：{{items.detectIndex}}
                                  </el-col>
                                  <el-col :span="8" class="item">
                                    指标值：{{items.detectIndexValue}}
                                  </el-col>
                                  <el-col :span="8" class="item">
                                    单位：{{items.detectUnit}}
                                  </el-col>
                                </div>
                              </template>
                            </el-row>
                          </div>
                        </template>
                      </template>
                    </el-collapse-item>
                    <el-collapse-item title="病原体感染" name="57">
                      <div style="display: flex;" class="item">
                        <div>HPV：</div>
                        <div style="flex: 1;">
                          <el-radio-group v-model="HPV.bytgrFlag" :disabled="disabled" @change="(val) => {handlePathogenRadioChange(val, 'HPV')}">
                            <el-radio :label="1">感染</el-radio>
                            <el-radio :label="0">未感染</el-radio>
                            <el-radio :label="2">不详</el-radio>
                          </el-radio-group>
                          <span v-if="HPV.unknownReason" style="padding-left: 20px;">
                            不详原因：{{HPV.unknownReason}}
                          </span>
                        </div>
                        <div v-if="HPV.bytgrFlag === 1">
                          <el-button :disabled="disabled" type="primary" size="mini" @click="handleTgrEdit(null, 'HPV')">+添加</el-button>
                        </div>
                      </div>
                      <template v-if="HPV.bytgrFlag === 1 && HPV.data.length > 0">
                        <template v-for="(item, index) in HPV.data">
                          <div :key="'HPV' + index" style="padding: 10px;">
                            <el-row :gutter="10">
                              <el-col :span="6" class="item">
                                检测时间：{{item.detectTime}}
                              </el-col>
                              <el-col :span="6" class="item">
                                检测方法：{{item.detectMethod}}
                              </el-col>
                              <el-col :span="6" class="item">
                                检测结果：{{item.testResult === 0 ? '阴性' : item.testResult === 1 ? '阳性' : item.testResult === 2 ? '不详' : ''}}
                              </el-col>
                              <el-col :span="6" class="item" style="text-align: right;">
                                <el-button :disabled="disabled" size="mini" type="primary" @click="handleTgrEdit(item, 'HPV')">编辑</el-button>
                                <el-button :disabled="disabled" size="mini" type="danger" @click="handleTgrDelete(item, 'HPV')">删除</el-button>
                              </el-col>
                              <el-col :span="24" class="item">
                                结果描述：{{item.resultDesc}}
                              </el-col>
                            </el-row>
                          </div>
                        </template>
                      </template>
                      <div style="display: flex;" class="item">
                        <div>HBV：</div>
                        <div style="flex: 1;">
                          <el-radio-group v-model="HBV.bytgrFlag" :disabled="disabled" @change="(val) => {handlePathogenRadioChange(val, 'HBV')}">
                            <el-radio :label="1">感染</el-radio>
                            <el-radio :label="0">未感染</el-radio>
                            <el-radio :label="2">不详</el-radio>
                          </el-radio-group>
                          <span v-if="HBV.unknownReason" style="padding-left: 20px;">
                            不详原因：{{HBV.unknownReason}}
                          </span>
                        </div>
                        <div v-if="HBV.bytgrFlag === 1">
                          <el-button :disabled="disabled" type="primary" size="mini" @click="handleTgrEdit(null, 'HBV')">+添加</el-button>
                        </div>
                      </div>
                      <template v-if="HBV.bytgrFlag === 1 && HBV.data.length > 0">
                        <template v-for="(item, index) in HBV.data">
                          <div :key="'HBV' + index" style="padding: 10px;">
                            <el-row :gutter="10">
                              <el-col :span="8" class="item">
                                检测时间：{{item.detectTime}}
                              </el-col>
                              <el-col :span="8" class="item">
                                检测结果：{{item.testResult === 0 ? '阴性' : item.testResult === 1 ? '阳性' : item.testResult === 2 ? '不详' : ''}}
                              </el-col>
                              <el-col :span="8" class="item" style="text-align: right;">
                                <el-button :disabled="disabled" size="mini" type="primary" @click="handleTgrEdit(item, 'HBV')">编辑</el-button>
                                <el-button :disabled="disabled" size="mini" type="danger" @click="handleTgrDelete(item, 'HBV')">删除</el-button>
                              </el-col>
                              <template v-for="(items, indexs) in item.bytgrDtl">
                                <div :key="'HBVbytgrDtl' + indexs">
                                  <el-col :span="8" class="item">
                                    指标：{{items.detectIndex}}
                                  </el-col>
                                  <el-col :span="8" class="item">
                                    指标值：{{items.detectIndexValue}}
                                  </el-col>
                                  <el-col :span="8" class="item">
                                    单位：{{items.detectUnit}}
                                  </el-col>
                                </div>
                              </template>
                            </el-row>
                          </div>
                        </template>
                      </template>
                      <div style="display: flex;" class="item">
                        <div>HCV：</div>
                        <div style="flex: 1;">
                          <el-radio-group v-model="HCV.bytgrFlag" :disabled="disabled" @change="(val) => {handlePathogenRadioChange(val, 'HCV')}">
                            <el-radio :label="1">感染</el-radio>
                            <el-radio :label="0">未感染</el-radio>
                            <el-radio :label="2">不详</el-radio>
                          </el-radio-group>
                          <span v-if="HCV.unknownReason" style="padding-left: 20px;">
                            不详原因：{{HCV.unknownReason}}
                          </span>
                        </div>
                        <div v-if="HCV.bytgrFlag === 1">
                          <el-button :disabled="disabled" type="primary" size="mini" @click="handleTgrEdit(null, 'HCV')">+添加</el-button>
                        </div>
                      </div>
                      <template v-if="HCV.bytgrFlag === 1 && HCV.data.length > 0">
                        <template v-for="(item, index) in HCV.data" style="padding: 10px;">
                          <div :key="'HCV' + index">
                            <el-row :gutter="10">
                              <el-col :span="8" class="item">
                                检测时间：{{item.detectTime}}
                              </el-col>
                              <el-col :span="8" class="item">
                                检测结果：{{item.testResult === 0 ? '阴性' : item.testResult === 1 ? '阳性' : item.testResult === 2 ? '不详' : ''}}
                              </el-col>
                              <el-col :span="8" class="item" style="text-align: right;">
                                <el-button :disabled="disabled" size="mini" type="primary" @click="handleTgrEdit(item, 'HCV')">编辑</el-button>
                                <el-button :disabled="disabled" size="mini" type="danger" @click="handleTgrDelete(item, 'HCV')">删除</el-button>
                              </el-col>
                              <template v-for="(items, indexs) in item.bytgrDtl">
                                <div :key="'HCVbytgrDtl' + indexs">
                                  <el-col :span="8" class="item">
                                    指标：{{items.detectIndex}}
                                  </el-col>
                                  <el-col :span="8" class="item">
                                    指标值：{{items.detectIndexValue}}
                                  </el-col>
                                  <el-col :span="8" class="item">
                                    单位：{{items.detectUnit}}
                                  </el-col>
                                </div>
                              </template>
                            </el-row>
                          </div>
                        </template>
                      </template>
                      <div style="display: flex;" class="item">
                        <div>EBV：</div>
                        <div style="flex: 1;">
                          <el-radio-group v-model="EBV.bytgrFlag" :disabled="disabled" @change="(val) => {handlePathogenRadioChange(val, 'EBV')}">
                            <el-radio :label="1">感染</el-radio>
                            <el-radio :label="0">未感染</el-radio>
                            <el-radio :label="2">不详</el-radio>
                          </el-radio-group>
                          <span v-if="EBV.unknownReason" style="padding-left: 20px;">
                            不详原因：{{EBV.unknownReason}}
                          </span>
                        </div>
                        <div v-if="EBV.bytgrFlag === 1">
                          <el-button :disabled="disabled" type="primary" size="mini" @click="handleTgrEdit(null, 'EBV')">+添加</el-button>
                        </div>
                      </div>
                      <template v-if="EBV.bytgrFlag === 1 && EBV.data.length > 0">
                        <template v-for="(item, index) in EBV.data">
                          <div :key="'EBV' + index" style="padding: 10px;">
                            <el-row :gutter="10">

                              <el-col :span="8" class="item">
                                检测时间：{{item.detectTime}}
                              </el-col>
                              <el-col :span="8" class="item">
                                检测结果：{{item.testResult === 0 ? '阴性' : item.testResult === 1 ? '阳性' : item.testResult === 2 ? '不详' : ''}}
                              </el-col>
                              <el-col :span="8" class="item" style="text-align: right;">
                                <el-button :disabled="disabled" size="mini" type="primary" @click="handleTgrEdit(item, 'EBV')">编辑</el-button>
                                <el-button :disabled="disabled" size="mini" type="danger" @click="handleTgrDelete(item, 'EBV')">删除</el-button>
                              </el-col>
                              <template v-for="(items, indexs) in item.bytgrDtl">
                                <div :key="'EBVbytgrDtl' + indexs">
                                  <el-col :span="8" class="item">
                                    指标：{{items.detectIndex}}
                                  </el-col>
                                  <el-col :span="8" class="item">
                                    指标值：{{items.detectIndexValue}}
                                  </el-col>
                                  <el-col :span="8" class="item">
                                    单位：{{items.detectUnit}}
                                  </el-col>
                                </div>
                              </template>
                            </el-row>
                          </div>
                        </template>
                      </template>
                      <div style="display: flex;" class="item">
                        <div>HIV：</div>
                        <div style="flex: 1;">
                          <el-radio-group v-model="HIV.bytgrFlag" :disabled="disabled">
                            <el-radio :label="1">感染</el-radio>
                            <el-radio :label="0">未感染</el-radio>
                            <el-radio :label="2">不详</el-radio>
                          </el-radio-group>
                        </div>
                      </div>
                      <div style="display: flex;" class="item">
                        <div>幽门螺旋杆菌：</div>
                        <div style="flex: 1;">
                          <el-radio-group v-model="HP.bytgrFlag" :disabled="disabled">
                            <el-radio :label="1">感染</el-radio>
                            <el-radio :label="0">未感染</el-radio>
                            <el-radio :label="2">不详</el-radio>
                          </el-radio-group>
                        </div>
                      </div>
                    </el-collapse-item>
                  </el-collapse>
                </div>
              </el-collapse-item>
              <el-collapse-item :class="{isOpen: activeNames.indexOf('6') !== -1}" title="治疗情况" name="6">
                <template slot="title">
                  <div>治疗情况</div>
                  <el-checkbox  v-model.trim="isAudit[7]" v-if="type == 2" class="title-checkbox" @change="handleAuditStatusChange">已审</el-checkbox>
                </template>
                <div style="padding: 0 10px;">
                  <el-collapse v-model="treatmentActiveNames" @change="handleChange">
                    <el-collapse-item title="手术史" name="61">
                      <div style="display: flex;" class="item">
                        <div>手术史：</div>
                        <div style="flex: 1;">
                          <!--<el-radio-group v-model="form.operateHis" @change="value => handleRadioChange(value, 'operateHis')">-->
                          <el-radio-group v-model="form.operateHis" :disabled="disabled">
                            <el-radio :label="1">有</el-radio>
                            <el-radio :label="0">无</el-radio>
                            <el-radio :label="2">不详</el-radio>
                          </el-radio-group>
                        </div>
                        <div v-if="form.operateHis === 1">
                          <el-button :disabled="disabled" type="primary" size="mini" @click="handleTreatmentSituationEdit(null, 'operateHis')">+添加</el-button>
                        </div>
                      </div>
                      <template v-if="form.operateHis === 1">
                        <template v-for="(item, index) in operateHis">
                          <div :key="'operateHis' + index">
                            <el-row :gutter="10">
                              <el-col :span="24" class="item" style="text-align: right;">
                                <el-button :disabled="disabled" size="mini" type="primary" @click="handleTreatmentSituationEdit(item, 'operateHis')">编辑</el-button>
                                <el-button :disabled="disabled" size="mini" type="danger" @click="handleTreatmentSituationDelete(item, 'operateHis')">删除</el-button>
                              </el-col>
                              <el-col :span="8" class="item">
                                第几次手术：{{item.times}}
                              </el-col>
                              <el-col :span="8" class="item">
                                手术日期：{{item.operateTime}}
                              </el-col>
                              <el-col :span="8" class="item">
                                手术名称：{{item.operateName}}
                              </el-col>
                              <el-col :span="8" class="item">
                                手术病理分期：pT{{item.ptnmPt}};N{{item.ptnmN}};M{{item.ptnmM}}
                              </el-col>
                              <el-col :span="8" class="item">
                                手术病理分型：{{item.pathologicalType}}
                              </el-col>
                              <el-col :span="8" class="item">
                                淋巴结转移：{{item.lymphTransfer === 1 ? '是': item.lymphTransfer === 0 ? '否' : ''}}
                              </el-col>
                              <el-col :span="8" class="item">
                                转移内容：{{item.lymphContent}}
                              </el-col>
                              <!--<el-col :span="8" class="item">-->
                              <!--是否随访：{{item.isFollowup === 1 ? '是': '否'}}-->
                              <!--</el-col>-->
                              <!--<el-col :span="8" class="item">-->
                              <!--随访日期：{{item.followupYear}}-{{item.followupMonth}}-{{item.followupDay}}-->
                              <!--</el-col>-->
                            </el-row>
                          </div>
                        </template>
                      </template>
                    </el-collapse-item>
                    <el-collapse-item title="放疗史" name="62">
                      <div style="display: flex;" class="item">
                        <div>放疗史：</div>
                        <div style="flex: 1;">
                          <!--<el-radio-group v-model="form.otherapy" @change="value => handleRadioChange(value, 'otherapy')">-->
                          <el-radio-group v-model="form.otherapy" :disabled="disabled">
                            <el-radio :label="1">有</el-radio>
                            <el-radio :label="0">无</el-radio>
                            <el-radio :label="2">不详</el-radio>
                          </el-radio-group>
                        </div>
                        <div v-if="form.otherapy === 1">
                          <el-button :disabled="disabled" type="primary" size="mini" @click="handleTreatmentSituationEdit(null, 'otherapy')">+添加</el-button>
                        </div>
                      </div>
                      <div v-if="form.otherapy === 1">
                        <div class="item">
                          <el-button :disabled="disabled" type="primary" size="mini" @click="handleTreatmentSituationEdit(radiotherapyTableSelectRow, 'otherapy')">修改</el-button>
                          <el-button :disabled="disabled" type="primary" size="mini" @click="handleTreatmentSituationDelete(radiotherapyTableSelectRow, 'otherapy')">刪除</el-button>
                        </div>
                        <el-table
                          ref="radiotherapyTable"
                          :data="radiotherapyTableData" class="radiotherapyTable"
                          border
                          style="width: 100%"
                          max-height="252"
                          size="mini" @row-click="(row, column, event) => handleRowClick(row, column, event, 'otherapy')" @select="(selection, row) => handleSelectTable(selection, row, 'otherapy')">
                          <el-table-column type="selection" fixed="left"></el-table-column>
                          <el-table-column prop="dosage" label="剂量" min-width="120" show-overflow-tooltip></el-table-column>
                          <el-table-column prop="position" label="部位" min-width="120" show-overflow-tooltip></el-table-column>
                          <el-table-column prop="course" label="疗程" min-width="120" show-overflow-tooltip></el-table-column>
                          <el-table-column prop="times" label="次数" min-width="120" show-overflow-tooltip></el-table-column>
                          <el-table-column prop="startTime" label="起始时间" min-width="120" show-overflow-tooltip></el-table-column>
                          <el-table-column prop="endTime" label="结束时间" min-width="120" show-overflow-tooltip></el-table-column>
                          <el-table-column prop="effect" label="疗效" min-width="120" show-overflow-tooltip></el-table-column>
                          <el-table-column prop="otherEffect" label="其他疗效" min-width="120" show-overflow-tooltip></el-table-column>
                          <el-table-column label="是否随访">
                            <template slot-scope="scope">
                              {{scope.row.isFollowup === 1 ? '是' : '否'}}
                            </template>
                          </el-table-column>
                        </el-table>
                      </div>
                    </el-collapse-item>
                    <el-collapse-item title="用药史" name="63">
                      <div style="display: flex;" class="item">
                        <div>用药史：</div>
                        <div style="flex: 1;">
                          <!--<el-radio-group v-model="form.drugHistory" @change="value => handleRadioChange(value, 'drugHistory')">-->
                          <el-radio-group v-model="form.drugHistory" :disabled="disabled">
                            <el-radio :label="1">有</el-radio>
                            <el-radio :label="0">无</el-radio>
                            <el-radio :label="2">不详</el-radio>
                          </el-radio-group>
                        </div>
                        <div v-if="form.drugHistory === 1">
                          <el-button :disabled="disabled" type="primary" size="mini" @click="handleTreatmentSituationEdit(null, 'drugHistory')">+添加</el-button>
                        </div>
                      </div>
                      <div v-if="form.drugHistory === 1">
                        <div class="item">
                          <el-button :disabled="disabled" type="primary" size="mini" @click="handleTreatmentSituationEdit(drugHistoryTableSelectRow, 'drugHistory')">修改</el-button>
                          <el-button :disabled="disabled" type="primary" size="mini" @click="handleTreatmentSituationDelete(drugHistoryTableSelectRow, 'drugHistory')">刪除</el-button>
                        </div>
                        <el-table
                          ref="drugHistoryTable"
                          :data="drugHistoryTableData"  class="drugHistoryTable"
                          border
                          style="width: 100%"
                          max-height="252"
                          size="mini" @row-click="(row, column, event) => handleRowClick(row, column, event, 'drugHistory')" @select="(selection, row) => handleSelectTable(selection, row, 'drugHistory')">
                          <el-table-column type="selection" fixed="left"></el-table-column>
                          <el-table-column prop="drugPlanType" label="方案类型" min-width="120" show-overflow-tooltip></el-table-column>
                          <el-table-column prop="startTime" label="起始时间" min-width="120" show-overflow-tooltip></el-table-column>
                          <el-table-column prop="endTime" label="结束时间" min-width="120" show-overflow-tooltip></el-table-column>
                          <el-table-column prop="planName" label="方案名称" min-width="120" show-overflow-tooltip></el-table-column>
                          <el-table-column prop="firstEffect" label="首次疗效" min-width="120" show-overflow-tooltip></el-table-column>
                          <el-table-column prop="firstEffectTime" label="评估时间" min-width="120" show-overflow-tooltip></el-table-column>
                          <el-table-column prop="effect" label="目前疗效" min-width="120" show-overflow-tooltip></el-table-column>
                          <el-table-column prop="effectTime" label="评估时间" min-width="120" show-overflow-tooltip></el-table-column>
                          <el-table-column prop="bestEffect" label="最佳疗效" min-width="120" show-overflow-tooltip></el-table-column>
                          <el-table-column prop="bestEffectTime" label="评估时间" min-width="120" show-overflow-tooltip></el-table-column>
                        </el-table>
                      </div>
                    </el-collapse-item>
                  </el-collapse>
                </div>
              </el-collapse-item>
              <el-collapse-item :class="{isOpen: activeNames.indexOf('7') !== -1}" title="疾病史" name="7">
                <template slot="title">
                  <div>疾病史</div>
                  <el-checkbox  v-model.trim="isAudit[8]" v-if="type == 2" class="title-checkbox" @change="handleAuditStatusChange">已审</el-checkbox>
                </template>
                <div style="display: flex;" class="item">
                  <div>疾病史：</div>
                  <div style="flex: 1;">
                    <!--<el-radio-group v-model="form.sickHistory" @change="value => handleRadioChange(value, 'sickHistory')">-->
                    <el-radio-group v-model="form.sickHistory" :disabled="disabled">
                      <el-radio :label="1">有</el-radio>
                      <el-radio :label="0">无</el-radio>
                      <el-radio :label="2">不详</el-radio>
                    </el-radio-group>
                  </div>
                  <div v-if="form.sickHistory === 1">
                    <el-button :disabled="disabled" type="primary" size="mini" @click="handleSickHistoryEdit(null)">+添加</el-button>
                  </div>
                </div>
                <template v-if="form.sickHistory === 1">
                  <template v-for="(item, index) in sickHistory">
                    <div :key="'sickHistory' + index">
                      <el-row :gutter="10">
                        <el-col :span="24" class="item" style="text-align: right;">
                          <el-button :disabled="disabled" size="mini" type="primary" @click="handleSickHistoryEdit(item)">编辑</el-button>
                          <el-button :disabled="disabled" size="mini" type="danger" @click="handleSickHistoryDelete(item)">删除</el-button>
                        </el-col>
                        <el-col :span="24" class="item">
                          疾病部位：{{item.illPlace}}
                        </el-col>
                        <el-col :span="24" class="item">
                          {{item.sickDetail}}
                        </el-col>
                      </el-row>
                    </div>
                  </template>
                </template>
              </el-collapse-item>
              <el-collapse-item :class="{isOpen: activeNames.indexOf('8') !== -1}" title="家族史" name="8">
                <template slot="title">
                  <div>家族史</div>
                  <el-checkbox  v-model.trim="isAudit[9]" v-if="type == 2" class="title-checkbox" @change="handleAuditStatusChange">已审</el-checkbox>
                </template>
                <div style="display: flex;" class="item">
                  <div>家族史：</div>
                  <div style="flex: 1;">
                    <el-radio-group v-model="form.familyHistory" :disabled="disabled">
                      <!--<el-radio-group v-model="form.familyHistory" @change="value => handleRadioChange(value, 'familyHistory')">-->
                      <el-radio :label="1">有</el-radio>
                      <el-radio :label="0">无</el-radio>
                      <el-radio :label="2">不详</el-radio>
                    </el-radio-group>
                    <span v-if="form.familyHistoryRemarks" style="padding-left: 20px;">
                      家族史(备注): {{form.familyHistoryRemarks}}
                    </span>
                  </div>
                  <div v-if="form.familyHistory === 1">
                    <el-button :disabled="disabled" type="primary" size="mini" @click="handleFamilyHistoryEdit(null)">+添加</el-button>
                  </div>
                </div>
                <template v-if="form.familyHistory === 1">
                  <template v-for="(item, index) in familyHistory">
                    <div :key="'familyHistory' + index">
                      <el-row :gutter="10">
                        <el-col :span="24" class="item" style="text-align: right;">
                          <el-button :disabled="disabled" size="mini" type="primary" @click="handleFamilyHistoryEdit(item)">编辑</el-button>
                          <el-button :disabled="disabled" size="mini" type="danger" @click="handleFamilyHistoryDelete(item)">删除</el-button>
                        </el-col>
                        <el-col :span="8" class="item">
                          亲属样本编号：{{item.relativesSampleNum}}
                        </el-col>
                        <el-col :span="8" class="item">
                          亲属关系：{{item.relationship}}
                        </el-col>
                        <el-col :span="8" class="item">
                          患病年龄：{{item.age}}
                        </el-col>
                        <el-col :span="8" class="item">
                          诊断癌种：{{item.cancerName}}
                        </el-col>
                      </el-row>
                    </div>
                  </template>
                </template>
              </el-collapse-item>
              <el-collapse-item :class="{isOpen: activeNames.indexOf('9') !== -1}" title="其他信息" name="9">
                <template slot="title">
                  <div>其他信息</div>
                  <el-checkbox  v-model.trim="isAudit[10]" v-if="type == 2" class="title-checkbox" @change="handleAuditStatusChange">已审</el-checkbox>
                </template>
                <el-row :gutter="10">
                  <el-col :span="8" class="colHeight">
                    <el-form-item label="其他疾病史" label-width="110px">
                      <el-input v-model.trim="form.familyOtherIllness" :disabled="disabled" placeholder="请输入"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8" class="colHeight">
                    <el-form-item label="身高" label-width="110px">
                      <el-input v-model.number="form.height" :disabled="disabled" placeholder="请输入"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8" class="colHeight">
                    <el-form-item label="体重" label-width="110px">
                      <el-input v-model.number="form.weight" :disabled="disabled" placeholder="请输入"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8" class="colHeight">
                    <el-form-item label="职业" label-width="110px">
                      <el-input v-model.trim="form.occupation" :disabled="disabled" placeholder="请输入"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8" class="colHeight">
                    <el-form-item label="婚否" label-width="110px">
                      <el-select v-model="form.marriage" :disabled="disabled" clearable placeholder="请选择" class="selectItem">
                        <el-option
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                          v-for="item in trueOrFalseList">
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8" class="colHeight">
                    <el-form-item label="初潮" label-width="110px">
                      <el-input v-model.trim="form.menarche" :disabled="disabled" placeholder="请输入">
                        <template slot="append">岁</template>
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8" class="colHeight">
                    <el-form-item label="绝经" label-width="110px">
                      <el-input v-model.trim="form.menopause" :disabled="disabled" placeholder="请输入">
                        <template slot="append">岁</template>
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8" class="colHeight">
                    <el-form-item label="生育史" label-width="110px">
                      <el-input v-model.trim="form.growthHistory" :disabled="disabled" placeholder="请输入"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8" class="colHeight">
                    <el-form-item label="首次生育" label-width="110px">
                      <el-input v-model.trim="form.birthFirst" :disabled="disabled" placeholder="请输入">
                        <template slot="append">岁</template>
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" class="colHeight">
                    <el-form-item label="是否使用激素代替治疗(>=5年)" label-width="210px">
                      <el-select v-model="form.isLongUsehormone" :disabled="disabled" clearable placeholder="请选择" class="selectItem">
                        <el-option
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                          v-for="item in trueOrFalseList">
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" class="colHeight">
                    <el-form-item label="是否服用口服避孕药(>=5年)" label-width="200px">
                      <el-select v-model="form.isLongUseacyeterion" :disabled="disabled" clearable placeholder="请选择" class="selectItem">
                        <el-option
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                          v-for="item in trueOrFalseList">
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8" class="colHeight">
                    <el-form-item label="疫苗接种史" label-width="110px">
                      <el-input v-model="form.vaccinationHistory" clearable maxlength="200" placeholder="请输入" class="selectItem">
                      </el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8" class="colHeight redStyle">
                    <el-form-item label="吸烟史" label-width="110px">
                      <el-select v-model="form.smoking" :disabled="disabled" clearable placeholder="请选择" class="selectItem">
                        <el-option
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                          v-for="item in beanList">
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8" class="colHeight">
                    <el-form-item label="吸烟史长" label-width="110px">
                      <el-input v-model.trim="form.smokingLength" :disabled="disabled" placeholder="请输入"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8" class="colHeight">
                    <el-form-item label="平均每日吸烟量" label-width="120px">
                      <el-input v-model.trim="form.smokingAmounts" :disabled="disabled" placeholder="请输入"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8" class="colHeight">
                    <el-form-item label="是否已戒烟" label-width="110px">
                      <el-select v-model="form.quitSmoke" :disabled="disabled" clearable placeholder="请选择" class="selectItem">
                        <el-option
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                          v-for="item in trueOrFalseList">
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8" class="colHeight">
                    <el-form-item label="已戒烟时间" label-width="110px">
                      <el-input v-model.trim="form.quitSmokeTime" :disabled="disabled" placeholder="请输入"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8" class="colHeight">
                    <el-form-item label="饮酒史" label-width="110px">
                      <el-select v-model="form.drink" :disabled="disabled" clearable placeholder="请选择" class="selectItem">
                        <el-option
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                          v-for="item in beanList">
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8" class="colHeight">
                    <el-form-item label="饮酒史长" label-width="110px">
                      <el-input v-model.trim="form.drinkLength" :disabled="disabled" placeholder="请输入"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8" class="colHeight">
                    <el-form-item label="饮酒量" label-width="110px">
                      <el-input v-model.trim="form.drinkIntake" :disabled="disabled" placeholder="请输入"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8" class="colHeight">
                    <el-form-item label="霉变食物食用史" label-width="120px">
                      <el-select v-model="form.mildewfoodEatHis" :disabled="disabled" clearable placeholder="请选择" class="selectItem">
                        <el-option
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                          v-for="item in hasList">
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8" class="colHeight">
                    <el-form-item label="体育锻炼史" label-width="110px">
                      <el-select v-model="form.exerciseHis" :disabled="disabled" clearable placeholder="请选择" class="selectItem">
                        <el-option
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                          v-for="item in hasList">
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8" class="colHeight">
                    <el-form-item label="进烫食/烫茶史" label-width="110px">
                      <el-select v-model="form.eatburnfoodHis" :disabled="disabled" clearable placeholder="请选择" class="selectItem">
                        <el-option
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                          v-for="item in hasList">
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8" class="colHeight">
                    <el-form-item label="食用红肉史" label-width="110px">
                      <el-select v-model="form.redmeatHis" :disabled="disabled" clearable placeholder="请选择" class="selectItem">
                        <el-option
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                          v-for="item in hasList">
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8" class="colHeight">
                    <el-form-item label="新鲜果蔬史" label-width="110px">
                      <el-select v-model="form.fruitsVegetablesHis" :disabled="disabled" clearable placeholder="请选择" class="selectItem">
                        <el-option
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                          v-for="item in hasList">
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="16" class="colHeight">
                    <el-form-item label="有毒有害放射性物质接触史" label-width="190px">
                      <el-input v-model.trim="form.toxicHistory" :disabled="disabled" placeholder="请输入"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" class="colHeight">
                    <el-form-item label="其他史" label-width="110px">
                      <el-input v-model.trim="form.otherHistory" :disabled="disabled" placeholder="请输入"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" class="colHeight">
                    <el-form-item label="是否参加临床研究" label-width="130px">
                      <el-select v-model="form.isInClinicalResearch" :disabled="disabled" placeholder="请选择" class="selectItem" @change="handleIsInClinicalResearchChange">
                        <el-option
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                          v-for="item in trueOrFalseList">
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" v-if="form.isInClinicalResearch === 1" class="colHeight">
                    <el-form-item key="projectNum" prop="projectNum" label="项目ID/审批号" label-width="130px">
                      <el-input v-model="form.projectNum" placeholder="项目ID/审批号"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" v-else class="colHeight">
                    <el-form-item label="项目ID/审批号" label-width="130px">
                      {{form.projectNum ? form.projectNum : '-'}}
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" class="colHeight">
                    <el-form-item label="是否终止/退出研究" label-width="140px">
                      <el-select v-model="form.isQuitResearch" :disabled="disabled" clearable placeholder="请选择" class="selectItem">
                        <el-option
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                          v-for="item in trueOrFalseList">
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" class="colHeight">
                    <el-form-item label="终止/退出时间" label-width="110px">
                      <my-date-picker v-model="form.quitTime" :is-disabled="disabled"></my-date-picker>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" class="colHeight">
                    <el-form-item label="终止/退出原因" label-width="110px">
                      <el-select v-model="form.quitReason" :disabled="disabled" placeholder="请选择" class="selectItem">
                        <el-option
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                          v-for="item in quitReasonList">
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item v-if="form.quitReason === 11" label="退出原因(其他)" label-width="110px">
                      <el-input v-model.trim="form.quitReasonDesc" :disabled="form.quitReason !== 11 || disabled" placeholder="请输入"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" class="colHeight">
                    <el-form-item label="随访时间" label-width="110px">
                      <my-date-picker v-model="form.followupTime" :is-disabled="disabled"></my-date-picker>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" class="colHeight">
                    <el-form-item label="随访生存结局" label-width="110px">
                      <el-select v-model="form.followupSurvivalResult" :disabled="disabled" placeholder="请选择" class="selectItem">
                        <el-option
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                          v-for="item in followupSurvivalResultList">
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" class="colHeight">
                    <el-form-item label="死亡时间" label-width="110px">
                      <my-date-picker v-model="form.deathTime" :is-disabled="disabled"></my-date-picker>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" class="colHeight">
                    <el-form-item label="死亡原因" label-width="110px">
                      <el-input v-model.trim="form.deathReason" :disabled="disabled" placeholder="请输入"></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-collapse-item>
            </el-collapse>
          </el-form>
        </div>
        <div class="right">
<!--          <div class="title">图片信息</div>-->
<!--          <el-tabs v-model="imgType" @tab-click="handleTabChange">-->
<!--            <el-tab-pane :label="`送检单${getImgIndexAndLength('sampleOrderImg')}`" name="sampleOrderImg"></el-tab-pane>-->
<!--            <el-tab-pane :label="`临床资料${getImgIndexAndLength('imgNames')}`" name="imgNames"></el-tab-pane>-->
<!--            <el-tab-pane :label="`随访资料${getImgIndexAndLength('followupImgNames')}`" name="followupImgNames"></el-tab-pane>-->
<!--            <el-tab-pane :label="`补充资料${getImgIndexAndLength('supplementaryImgs')}`" name="supplementaryImgs"></el-tab-pane>-->
<!--            <el-tab-pane :label="`修改信息${supplementaryNote.length <= 0 ? '' : '('+fixInfoLength + '/' + supplementaryNote.length +')'}`" name="fixInfo"></el-tab-pane>-->
<!--            <el-tab-pane label="送检单Excel" name="inspectionSheet"></el-tab-pane>-->
<!--          </el-tabs>-->
<!--          <div v-if="!['fixInfo', 'inspectionSheet'].includes(imgType)">-->
<!--            <el-button size="mini" @click="handlePreviousPicture">上一张</el-button>-->
<!--            <el-button size="mini" @click="handleNextPicture">下一张</el-button>-->
<!--            <el-button size="mini" @click="handleImageAction('zoomIn')">放大 <i class="el-icon-zoom-in"></i></el-button>-->
<!--            <el-button size="mini" @click="handleImageAction('zoomOut')">缩小<i class="el-icon-zoom-out"></i></el-button>-->
<!--            <el-button size="mini" @click="handleImageAction('anticlocelise')">旋转<i class="el-icon-refresh-left"></i></el-button>-->
<!--            <el-button size="mini" @click="handleImageReset">还原<i class="el-icon-full-screen"></i></el-button>-->
<!--            <el-button v-if="type !== 2 && imgType !== 'supplementaryImgs' && imgType !== 'followupImgNames'" size="mini" @click="handleEditPicture">编辑</el-button>-->
<!--          </div>-->
<!--          <div v-if="!['fixInfo', 'inspectionSheet'].includes(imgType)" class="picture">-->
<!--            <div v-if="imgSrc" class="image">-->
<!--              <template v-if="imgSrc.indexOf('.pdf') !== -1">-->
<!--                <div style="width: 100%; height: 100%;display: flex; justify-content: center;align-items: center;">-->
<!--                  <div>-->
<!--                    <icon-svg icon-class="icon-pdf" style="font-size: 150px;color: #409EFF;"></icon-svg>-->
<!--                    <div style="text-align: center;">-->
<!--                      <el-button type="text" size="mini" @click="handleViewPdf(imgSrc)">点击查看PDF文件</el-button>-->
<!--                    </div>-->
<!--                  </div>-->
<!--                </div>-->
<!--              </template>-->
<!--              <template v-else>-->
<!--                <div-->
<!--                  ref="imgDiv"-->
<!--                  v-loading="imgLoading"-->
<!--                  element-loading-text="拼命加载中"-->
<!--                  element-loading-spinner="el-icon-loading"-->
<!--                  element-loading-background="rgba(0, 0, 0, 0.8)">-->
<!--                  <img-->
<!--                    ref="img"-->
<!--                    :src="imgSrc"-->
<!--                    :style="imgStyle"-->
<!--                    alt=""-->
<!--                    style="display: block;object-fit: contain"-->
<!--                    @load="handleImgLoad"-->
<!--                    @error="handleImgError">-->
<!--                </div>-->
<!--              </template>-->
<!--            </div>-->
<!--          </div>-->
<!--          <div v-if="imgType === 'fixInfo'">-->
<!--            <el-table-->
<!--              ref="table"-->
<!--              :data="supplementaryNote"-->
<!--              :height="400"-->
<!--              style="width: 100%;"-->
<!--            >-->
<!--              <el-table-column type="index"></el-table-column>-->
<!--              <el-table-column prop="date"></el-table-column>-->
<!--              <el-table-column prop="content"></el-table-column>-->
<!--              <el-table-column>-->
<!--                <template slot-scope="scope">-->
<!--                  <el-button v-if="scope.row.status === 1" size="mini"  @click="handelStatus(0,scope.$index)">已处理</el-button>-->
<!--                  <el-button v-if="scope.row.status === 0" size="mini" type="primary" @click="handelStatus(1, scope.$index)">未处理</el-button>-->
<!--                </template>-->
<!--              </el-table-column>-->
<!--            </el-table>-->
<!--          </div>-->
<!--          <div v-if="imgType === 'inspectionSheet'">-->
<!--            <el-row class="row-style">-->
<!--              <el-col :span="12">样本编号：{{otherClinicalInfo.sampleNum}}</el-col>-->
<!--              <el-col :span="12">受检者姓名：{{otherClinicalInfo.patientName}}</el-col>-->
<!--            </el-row>-->
<!--            <el-row class="row-style">-->
<!--              <el-col :span="12">性别：{{otherClinicalInfo.sex === 0 ? '男' : otherClinicalInfo.sex === 1 ? '女' : ''}}</el-col>-->
<!--              <el-col :span="12">证件号码：{{otherClinicalInfo.idcard}}</el-col>-->
<!--            </el-row>-->
<!--            <el-row class="row-style">-->
<!--              <el-col :span="12">出生年月：{{otherClinicalInfo.birthday}}</el-col>-->
<!--              <el-col :span="12">产品名称：{{otherClinicalInfo.proName}}</el-col>-->
<!--            </el-row>-->
<!--            <el-row class="row-style">-->
<!--              <el-col :span="12">样本类型：{{otherClinicalInfo.sampleType}}</el-col>-->
<!--              <el-col :span="12">癌种： {{otherClinicalInfo.cancer}}</el-col>-->
<!--            </el-row>-->
<!--            <el-row class="row-style">-->
<!--              <el-col :span="12">样本采集时间：{{otherClinicalInfo.sampleCollectTime}}</el-col>-->
<!--              <el-col :span="12">组织样本采集时间：{{otherClinicalInfo.tissueCollectTime}}</el-col>-->
<!--            </el-row>-->
<!--            <el-row class="row-style">-->
<!--              <el-col :span="12">采集部位：{{otherClinicalInfo.samplingArea}}</el-col>-->
<!--              <el-col :span="12">临床诊断结果：{{otherClinicalInfo.clinicalDiagnose}}</el-col>-->
<!--            </el-row>-->
<!--            <el-row class="row-style">-->
<!--              <el-col :span="12">确诊年龄：{{otherClinicalInfo.diagnosisAge}}</el-col>-->
<!--              <el-col :span="12">家族史：{{otherClinicalInfo.familyHistory === 0 ? '无' : otherClinicalInfo.familyHistory === 1 ? '有' : '不详'}}</el-col>-->
<!--            </el-row>-->
<!--            <el-row class="row-style">-->
<!--              <el-col :span="12">家族史备注：{{otherClinicalInfo.familyHistoryRemarks}}</el-col>-->
<!--              <el-col :span="12">输血史：{{otherClinicalInfo.bloodTransHistory === 0 ? '无' : otherClinicalInfo.bloodTransHistory === 1 ? '有' : '不详'}}</el-col>-->
<!--            </el-row>-->
<!--            <el-row class="row-style">-->
<!--              <el-col :span="12">手术史：{{otherClinicalInfo.operateHis === 0 ? '无' : otherClinicalInfo.operateHis === 1 ? '有' : '不详'}}</el-col>-->
<!--              <el-col :span="12">用药史：{{otherClinicalInfo.drugHistory === 0 ? '无' : otherClinicalInfo.drugHistory === 1 ? '有' : '不详'}}</el-col>-->
<!--            </el-row>-->
<!--            <el-row class="row-style">-->
<!--              <el-col :span="12">手术史(银丰)：{{ otherClinicalInfo.operationHistoryText }}</el-col>-->
<!--              <el-col :span="12">用药史(银丰)：{{ otherClinicalInfo.medicineHistoryText }}</el-col>-->
<!--            </el-row>-->
<!--            <el-row class="row-style">-->
<!--              <el-col :span="12">放疗史：{{otherClinicalInfo.otherapy === 0 ? '无' : otherClinicalInfo.otherapy === 1 ? '有' : '不详'}}</el-col>-->
<!--              <el-col :span="12">目前临床分期：{{otherClinicalInfo.clinicalStages}}</el-col>-->
<!--            </el-row>-->
<!--            <el-row class="row-style">-->
<!--              <el-col :span="12">目前转移部位：{{otherClinicalInfo.cancerMetastasisSites}}</el-col>-->
<!--              <el-col :span="12">基因检测：{{otherClinicalInfo.geneTest === 0 ? '无' : otherClinicalInfo.geneTest === 1 ? '有' : '不详'}}</el-col>-->
<!--            </el-row>-->
<!--            <el-row class="row-style">-->
<!--              <el-col :span="12">影像检查：{{otherClinicalInfo.iconography === 0 ? '无' : otherClinicalInfo.iconography === 1 ? '有' : '不详'}}</el-col>-->
<!--              <el-col :span="12">肿瘤标记物：{{otherClinicalInfo.cancerTag === 0 ? '无' : otherClinicalInfo.cancerTag === 1 ? '有' : '不详'}}</el-col>-->
<!--            </el-row>-->
<!--            <el-row class="row-style">-->
<!--              <el-col :span="12">备注：{{otherClinicalInfo.remark}}</el-col>-->
<!--            </el-row>-->
<!--          </div>-->
          <sample-ocr
            :sample-basic-id="sampleBasicId"
            :img-src="imgSrc"
            :img-type="imgType"
            :img-names="imgNames"
            :sample-order-img="sampleOrderImg"
            :followup-img-names="followupImgNames"
            :supplementary-imgs="supplementaryImgs"
            :supplementary-note="supplementaryNote"
            :other-clinical-info="otherClinicalInfo"
            :title="title"
            :fix-info-length="fixInfoLength"
            @fixImgEvent="handleFixImg"
            @statusFixEvent="handleFixStatus"
          >

          </sample-ocr>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <template v-if="type === 1">
          <!--          <el-button type="primary" @click="handleConfirm(1, 1)" size="mini" :loading="saveLoading">信息同步</el-button>-->

          <el-button type="primary" size="mini" @click="handleInfoCallback">钉钉信息推送</el-button>
          <el-button :loading="saveLoading" type="primary" size="mini" @click="handleConfirm(1)">保存</el-button>
          <el-button :loading="saveLoading" type="primary" size="mini" @click="handleConfirm(3)">补录完成</el-button>
          <el-button type="danger" size="mini" @click="handleClose">关闭</el-button>
        </template>
        <template v-else-if="type === 2">
          <el-button :loading="saveLoading" type="primary" size="mini" @click="handleConfirm(4)">审核通过</el-button>
          <el-button type="danger" size="mini" @click="handleReject">驳回</el-button>
        </template>
        <!--修改存疑按钮组-->
        <template v-else-if="type === 3">
<!--          <el-button type="primary" @click="handleConfirm(3, 1)" size="mini" :loading="saveLoading">信息同步</el-button>-->
          <!--存在访字标签-->
          <el-button v-if="isFollowUps === 2" type="primary" size="mini" @click="handleFollowUpErrorTips">随访异常</el-button>
          <el-button :loading="saveLoading" type="primary" size="mini" @click="handleConfirm(3)">保存</el-button>
          <el-button type="danger" size="mini" @click="handleClose">关闭</el-button>
        </template>
      </span>
    </el-dialog>
    <diagnosed-cancer-save-dialog
      style="margin-left: 30px"
      :pvisible.sync="diagnosedCancerSaveDialogVisible" :pdata="diagnosedCancerSaveDialogData" @diagnosedCancerSaveDialogConfirmEvent="handleDiagnosedCancerSaveDialogConfirm"
    ></diagnosed-cancer-save-dialog>
    <clinical-pathology-save-dialog
      :pvisible.sync="clinicalPathologySaveDialogVisible" :pdata="clinicalPathologySaveDialogData" @clinicalPathologySaveDialogConfirmEvent="handleClinicalPathologySaveDialogConfirm"
    ></clinical-pathology-save-dialog>
    <detect-save-dialog
      :pvisible.sync="detectSaveDialogVisible" :pdata="detectSaveDialogData" @detectSaveDialogConfirmEvent="handleDetectSaveDialogConfirm"
    ></detect-save-dialog>
    <tgr-save-dialog
      :pvisible.sync="tgrSaveDialogVisible" :pdata="tgrSaveDialogData" @tgrSaveDialogConfirmEvent="handleTgrSaveDialogConfirm"
    ></tgr-save-dialog>
    <treatment-situation-save-dialog
      :pvisible.sync="treatmentSituationSaveDialogVisible" :pdata="treatmentSituationSaveDialogData" @treatmentSituationSaveDialogConfirmEvent="handleTreatmentSituationSaveDialogConfirm"
    ></treatment-situation-save-dialog>
    <sick-history-save-dialog
      :pvisible.sync="sickHistorySaveDialogVisible" :pdata="sickHistorySaveDialogData" @sickHistorySaveDialogConfirmEvent="handleSickHistorySaveDialogConfirm"
    ></sick-history-save-dialog>
    <family-history-save-dialog
      :pvisible.sync="familyHistorySaveDialogVisible" :pdata="familyHistorySaveDialogData" @familyHistorySaveDialogConfirmEvent="handleFamilyHistorySaveDialogConfirm"
    ></family-history-save-dialog>
    <sanger-save-dialog
      :pvisible.sync="sangerSaveDialogVisible" :pdata="sangerSaveDialogData" @sangerSaveDialogConfirmEvent="handleSangerSaveDialogConfirm"
    ></sanger-save-dialog>
    <picture-info-save-dialog
      :pvisible.sync="pictureInfoSaveDialogVisible" :pdata="pictureInfoSaveDialogData" @pictureInfoSaveDialogConfirmEvent="handlePictureInfoSaveDialogConfirm"
    ></picture-info-save-dialog>
    <clinical-info-management-save-info-callback-dialog
      :pvisible.sync="InfoCallbackDialogVisible" custom-class="not-in-left" :id="sampleBasicId" @callbackDialogConfirmEvent="handleCallbackDialogConfirm"
    ></clinical-info-management-save-info-callback-dialog>
    <follew-up-error-dialog
      :pvisible.sync="followupDialogVisible" custom-class="not-in-left" :id="sampleBasicId"
    ></follew-up-error-dialog>
  </div>
</template>

<script>
import axios from 'axios'
import util from '../../../util/util'
import mixins from '../../../util/mixins'
import myDatePicker from '../../common/myDatePicker'
import diagnosedCancerSaveDialog from './clinicalInfoManagementDiagnosedCancerSaveDialog'
import clinicalPathologySaveDialog from './clinicalInfoManagementClinicalPathologySaveDialog'
import detectSaveDialog from './clinicalInfoManagementDetectSaveDialog'
import tgrSaveDialog from './clinicalInfoManagementTgrSaveDialog'
import treatmentSituationSaveDialog from './clinicalInfoManagementTreatmentSituationSaveDialog'
import sickHistorySaveDialog from './clinicalInfoManagementSickHistorySaveDialog'
import familyHistorySaveDialog from './clinicalInfoManagementFamilyHistorySaveDialog'
import sangerSaveDialog from './clinicalInfoManagementSangerSaveDialog'
import pictureInfoSaveDialog from './clinicalInfoManagementPictureInfoSaveDialog'
import clinicalInfoManagementSaveInfoCallbackDialog from './clinicalInfoManagementSaveInfoCallbackDialog'
import follewUpErrorDialog from './clinicalInfoManagementSaveInfoFollowUpErrorDialog'
import sampleImgInfo from '../pathogenSample/sampleImgInfo.vue'
import sampleOcr from './sampleOcr.vue'
export default {
  name: 'clinicalInfoManagementSaveInfoDialog',
  mixins: [mixins.dialogBaseInfo, mixins.tablePaginationCommonData],
  components: {
    sampleImgInfo,
    sampleOcr,
    myDatePicker,
    diagnosedCancerSaveDialog,
    clinicalPathologySaveDialog,
    detectSaveDialog,
    tgrSaveDialog,
    treatmentSituationSaveDialog,
    familyHistorySaveDialog,
    sickHistorySaveDialog,
    sangerSaveDialog,
    pictureInfoSaveDialog,
    clinicalInfoManagementSaveInfoCallbackDialog,
    follewUpErrorDialog
  },
  props: {
    testingLink: {
      type: String,
      default: ''
    },
    ptype: {
      type: Number,
      default: 0,
      required: true
    },
    psampleBasicId: {
      type: [Number, String],
      default: '',
      required: true
    },
    psampleNum: {
      type: String,
      default: '',
      required: true
    },
    isFollowUps: {
      type: [Number, String],
      required: true
    },
    sampleProductId: {
      type: [Number, String],
      default: '',
      required: true
    }
  },
  watch: {
    imgSrc () {
      this.$nextTick(_ => {
        const $img = this.$refs.img
        if (!$img.complete) {
          this.imgLoading = true
        }
      })
    }
  },
  computed: {
    // 病历小结样本类型
    sampleTotalType () {
      let sampleTotalType = []
      // 拼接样本类型
      this.productList.forEach(v => {
        v = v || {}
        let part = ''
        let sampleType = v.sampleType || ''
        if (this.form.samplingArea && sampleType.includes('组织')) {
          part = `(${this.form.samplingArea})`
        }
        if (sampleType + part) {
          sampleTotalType.push(sampleType + part)
        }
      })
      return sampleTotalType.join(',')
    },
    fixInfoLength () {
      return this.supplementaryNote.filter(item => item.status === 0).length
    },
    sampleCollectTime () {
      if (this.form.sampleType) {
        if (this.form.sampleType === '组织' && this.form.sampleType === '石蜡包埋组织') {
          return this.form.tissueCollectTime && this.form.tissueCollectTime.slice(0, 10)
        } else {
          return this.form.bloodCollectTime && this.form.bloodCollectTime.slice(0, 10)
        }
      } else {
        return ''
      }
    },
    imgStyle () {
      const { scale, deg, offsetX, offsetY, enableTransition, maxLength } = this.transform
      return {
        transform: `rotate(${deg}deg)`,
        transition: enableTransition ? 'all .3s' : '',
        'margin-left': `${offsetX}px`,
        'margin-top': `${offsetY}px`,
        width: scale * maxLength + 'px',
        height: scale * maxLength + 'px'
      }
    }
  },
  data () {
    // 校验证件号
    const validateIdCard = (rule, value, callback) => {
      const rules = {
        1: /^[0-9a-zA-Z]*$/g,
        2: /(^[EeKkGgDdSsPpHh]\d{8}$)|(^(([Ee][a-fA-F])|([DdSsPp][Ee])|([Kk][Jj])|([Mm][Aa])|(1[45]))\d{7}$)/g,
        3: /^[a-zA-Z0-9]{7,21}$/g,
        4: /^[C]\d{8}$|^[C][a-hA-Hj-nJ-Np-zP-Z][0-9]{7}$/g
      }
      if ([0, 5].includes(this.form.cardType)) {
        callback()
        return
      }
      if (!rules[this.form.cardType].test(this.form.idcard)) {
        callback(new Error('证件类型错误'))
      }
      callback()
    }
    return {
      productList: [],
      isAudit: [],
      transform: {
        scale: 1,
        deg: 0,
        offsetX: 0,
        offsetY: 0,
        enableTransition: false,
        maxLength: 300 // 宽和高的两者比较长的那部分
      },
      isFix: false, // 弹窗是否保存数据
      otherClinicalInfo: {}, //  工业客户送检单
      defaultData: {}, // 保存特定字段初始数据
      imgLoading: true,
      CancelToken: null,
      axiosSource: null,
      saveLoading: false,
      followupDialogVisible: false,
      title: '',
      type: '',
      multiples: 100, // 放大或者缩小
      deg: 0, // 旋转的角度
      isDrag: false, // 是否开始拖拽
      startX: 0, // 鼠标的点击X轴
      startY: 0, // 鼠标的点击Y轴
      moveX: 0, // 鼠标移动的X轴
      moveY: 0, // 鼠标移动的Y轴
      endX: 0,
      endY: 0,
      imgWidth: 0,
      imgHeight: 0,
      activeNames: '1',
      sampleInfoActiveNames: '',
      detectInfoActiveNames: '',
      treatmentActiveNames: '',
      disabled: false,
      sampleBasicId: null,
      sampleNum: '',
      confirmText: '保存',
      sampleInspectionId: null,
      sampleClinicalId: null,
      form: {
        otherPhotoContent: '',
        radio: '',
        select: '',
        chiefComplaint: '',
        productNames: '',
        projectName: '',
        productName: '', // 项目名称
        detailAddr: '',
        clinicalDiagnose: '',
        treatmentHistory: '',
        operationHistory: '',
        medicationHistory: '',
        irrationalHistory: '',
        medicalRecord: '',
        evaluationMark: '',
        evaluationDate: '',
        chargeCode: '',
        outpatientService: '',
        performanceStatus: '',
        recordDate: '',
        diagnosisDate: '',
        diagnosisAge: '',
        recrudescenceDate: '',
        clinicalDiagnoseMedical: '',
        primarySite: '',
        tissueType: '',
        familyHistoryRemarks: '',
        isAll: '完整',
        clinDetail: [],
        mainSuit: '',
        photoContent: [],
        readCancerId: [],
        cancerDiagnosisDate: '',
        cancerLabel: '',
        immune: '',
        cancerTag: '',
        geneTest: '',
        iconography: '',
        operateHis: '',
        otherapy: '',
        drugHistory: '',
        familyHistory: '',
        biocheExamTag: '',
        sexhormoneTag: '',
        sampleNum: '', // 样本编号
        patientID: '',
        thirdpartySampleNum: '',
        name: '',
        sex: '',
        originNum: '',
        nation: '',
        nativePlace: '',
        illnessCaseStatus: '',
        idcard: '',
        cardType: '',
        birthday: '',
        email: '',
        sampleType: '',
        province: '',
        city: '',
        region: '',
        sampleCollectDate: '',
        sampleCollectMan: '',
        contactTel: '',
        familyContactType: '',
        contactAddr: '',
        recipient: '',
        recipientTel: '',
        samleUseType: '',
        projectNum: '',
        chargeType: '',
        educationLevel: '',
        cancertypeId: '',
        marriage: '',
        hreportType: '',
        height: '',
        weight: '',
        occupation: '',
        gestationalWeeks: '',
        growthHistory: '',
        vaccinationHistory: '',
        smoking: '',
        smokingLength: '',
        drink: '',
        smokingAmounts: '',
        drinkLength: '',
        quitSmoke: '',
        quitSmokeTime: '',
        drinkIntake: '',
        otherHistory: '',
        alcoholDrugHistory: '',
        toxicHistory: '',
        fresearchCenterCode: '',
        fresearchCenter: '',
        fvisitCycle: '',
        remark: '',
        sampleConfirmTime: '',
        createTime: '',
        makeupStatus: '',
        creator: '',
        makeupTime: '',
        makeuper: '',
        rejectRemark: '',
        auditTime: '',
        auditor: '',
        doubter: '',
        doubtTime: '',
        doubtRemark: '',
        basicStatus: '',
        clinicalMkSta: '',
        clinicalRejectRmk: '',
        clinicalMkMan: '',
        menarche: '',
        menopause: '',
        birthFirst: '',
        isLongUsehormone: '',
        isLongUseacyeterion: '',
        mildewfoodEatHis: '',
        exerciseHis: '',
        eatburnfoodHis: '',
        redmeatHis: '',
        fruitsVegetablesHis: '',
        inspectionUnitId: '',
        customerCode: '',
        doctorCode: '',
        doctor: '',
        inspectionDoctor: '',
        admissionNum: '',
        departments: '',
        cancer: '',
        bloodTransHistory: '',
        bloodTransDate: '',
        baselineSendTimes: '',
        bloodTransType: [],
        otherBloodTransType: '',
        lastSampleType: '',
        lastSampleNum: '',
        detectionTimes: '',
        sampleOrderImg: [],
        inspectionInformedUrl: '',
        otherIllness: '',
        personalTumorHistory: '',
        familyOtherIllness: '',
        familyTumorHistory: '',
        relationship: '',
        familyClinicalDiagnosis: '',
        familyDiagnoseAge: '',
        salesMan: '',
        salesManName: '',
        salesManPhone: '',
        factotum: '',
        factotumName: '',
        factotumPhone: '',
        inspectionTime: '',
        bloodSampleType: '',
        bloodOtherType: '',
        bloodCollectTime: '',
        bloodMl: '',
        bloodTube: '',
        fspecialTakeBloodPosition: '',
        humorSampleType: '',
        humorOtherType: '',
        humorCollectTime: '',
        humorMl: '',
        humorTube: '',
        tissueSampleType: '',
        fcryopreservation: [],
        tissueOtherType: '',
        tissueCollectTime: '',
        tissueTube: '',
        tissueMm: '',
        samplingArea: '',
        samplingMode: '',
        otherSamplingMode: '',
        cellSampleType: '',
        cellOtherType: '',
        cellCollectTime: '',
        cellMl: '',
        cellTube: '',
        nucleicAcidSampleType: '',
        nucleicAcidOtherType: '',
        nucleicAcidCollectTime: '',
        nucleicAcidMl: '',
        nucleicAcidTube: '',
        inspectionUnitName: '',
        isInClinicalResearch: '',
        isQuitResearch: '',
        quitYear: '',
        quitMonth: '',
        quitDay: '',
        quitTime: '',
        quitReason: '',
        quitReasonDesc: '',
        followupYear: '',
        followupMonth: '',
        followupDay: '',
        followupTime: '',
        followupSurvivalResult: '',
        deathYear: '',
        deathMonth: '',
        deathDay: '',
        deathTime: '',
        deathReason: '',
        sickHistory: ''
      },
      immuneHistos: [],
      geneTest: [],
      iconography: [],
      cancerTag: [],
      biocheExamTag: [],
      sexhormoneTag: [],
      HPV: {
        bytgrFlag: 3,
        unknownReason: '',
        data: []
      },
      HBV: {
        bytgrFlag: 3,
        unknownReason: '',
        data: []
      },
      HCV: {
        bytgrFlag: 3,
        unknownReason: '',
        data: []
      },
      EBV: {
        bytgrFlag: 3,
        unknownReason: '',
        data: []
      },
      HIV: {
        bytgrFlag: 3,
        unknownReason: '',
        data: []
      },
      HP: {
        bytgrFlag: 3,
        unknownReason: '',
        data: []
      },
      operateHis: [], // 手术史信息
      radiotherapyTableData: [], // 放疗史表格信息
      radiotherapyTableSelectRow: new Map(), // 放疗史表格选中信息,
      drugHistoryTableData: [], // 用药史表格信息
      drugHistoryTableSelectRow: new Map(), // 用药史表格选中数据
      sickHistory: [], // 疾病史信息
      familyHistory: [], // 家族史信息
      optionsList: [],
      radio: 1,
      sangerForm: {
        sampleNum: ''
      },
      sangerTableData: [],
      sangerTableSelectRows: new Map(),
      imgType: 'sampleOrderImg',
      imgNames: [], // 临床资料
      followupImgNames: [], // 随访资料
      sampleOrderImg: [], // 送检单资源
      supplementaryImgs: [], // 补充资料
      supplementaryNote: [], // 修改信息
      imgSrc: '',
      cancerInfoList: [],
      cancerList: [],
      sampleTypes: [{
        value: '外周血',
        label: '外周血'
      },
      {
        value: '组织',
        label: '组织'
      },
      {
        value: '石蜡包埋组织',
        label: '石蜡包埋组织'
      },
      {
        value: '胸水',
        label: '胸水'
      },
      {
        value: '脑脊液',
        label: '脑脊液'
      }],
      bloodSampleTypeList: [
        {
          value: '血液',
          label: '血液'
        },
        {
          value: '其他',
          label: '其他'
        }
      ],
      humorSampleTypeList: [
        {
          value: '胸水',
          label: '胸水'
        },
        {
          value: '腹水',
          label: '腹水'
        },
        {
          value: '脑脊液',
          label: '脑脊液'
        },
        {
          value: '唾液',
          label: '唾液'
        },
        {
          value: '尿液',
          label: '尿液'
        },
        {
          value: '其他',
          label: '其他'
        }
      ],
      bloodTransHistoryBeanList: [
        {
          value: 0,
          label: '无'
        },
        {
          value: 1,
          label: '有'
        },
        {
          value: 2,
          label: '不详'
        }
      ],
      beanList: [
        {
          value: 0,
          label: '无'
        },
        {
          value: 1,
          label: '有'
        }
      ],
      tissueSampleTypeList: [
        {
          value: '石蜡白片',
          label: '石蜡白片'
        },
        {
          value: '石蜡屑',
          label: '石蜡屑'
        },
        {
          value: '石蜡块',
          label: '石蜡块'
        },
        {
          value: '石蜡包埋组织',
          label: '石蜡包埋组织'
        },
        {
          value: '新鲜组织',
          label: '新鲜组织'
        },
        {
          value: '其他',
          label: '其他'
        }
      ],
      samplingModeList: [
        {
          value: '手术切除',
          label: '手术切除'
        },
        {
          value: '穿刺',
          label: '穿刺'
        },
        {
          value: '气管镜',
          label: '气管镜'
        },
        {
          value: '胃镜',
          label: '胃镜'
        },
        {
          value: '其他',
          label: '其他'
        }
      ],
      cellSampleTypeList: [
        // 待定
        {
          value: '腹水细胞',
          label: '腹水细胞'
        },
        {
          value: '胸水细胞',
          label: '胸水细胞'
        },
        {
          value: '脑脊液细胞',
          label: '脑脊液细胞'
        },
        {
          value: '细胞',
          label: '细胞'
        },
        {
          value: '尿液沉淀',
          label: '尿液沉淀'
        },
        {
          value: '口腔拭子',
          label: '口腔拭子'
        },
        {
          value: '其他拭子',
          label: '其他拭子'
        },
        {
          value: '其他',
          label: '其他'
        }
      ],
      nucleicAcidSampleTypeList: [
        {
          value: '全基因组DNA',
          label: '全基因组DNA'
        },
        {
          value: 'RNA',
          label: 'RNA'
        },
        {
          value: '游离DNA',
          label: '游离DNA'
        },
        {
          value: '其他',
          label: '其他'
        }
      ],
      performanceStatusList: [
        {
          value: '0',
          label: '0'
        },
        {
          value: '1',
          label: '1'
        },
        {
          value: '2',
          label: '2'
        },
        {
          value: '3',
          label: '3'
        },
        {
          value: '4',
          label: '4'
        },
        {
          value: '5',
          label: '5'
        }
      ],
      cancerLabelList: [
        {
          value: '单癌种',
          label: '单癌种'
        },
        {
          value: '多癌种',
          label: '多癌种'
        },
        {
          value: '单癌种转化',
          label: '单癌种转化'
        }
      ],
      trueOrFalseList: [
        {
          value: 0,
          label: '否'
        },
        {
          value: 1,
          label: '是'
        }
      ],
      followupSurvivalResultList: [
        {
          value: 1,
          label: '生存'
        },
        {
          value: 2,
          label: '死亡'
        },
        {
          value: 3,
          label: '不详'
        }
      ],
      hasList: [
        {
          value: 0,
          label: '几乎从不'
        },
        {
          value: 1,
          label: '偶尔'
        },
        {
          value: 2,
          label: '经常'
        }
      ],
      quitReasonList: [
        {
          value: 1,
          label: '给药延迟/中断大于14天'
        },
        {
          value: 2,
          label: '研究者判断症状加重'
        },
        {
          value: 3,
          label: '疾病客观进展'
        },
        {
          value: 4,
          label: '患者未能满足入组/排除标准'
        },
        {
          value: 5,
          label: '主动退出（患者可以在任何时间自由退出研究治疗或评估），不会影响随后的治疗'
        },
        {
          value: 6,
          label: '患者失访'
        },
        {
          value: 7,
          label: '死亡'
        },
        {
          value: 8,
          label: '研究者和/或北京吉因加科技有限公司认为的安全性原因'
        },
        {
          value: 9,
          label: '研究者和/或北京吉因加科技有限公司认为严重违背方案'
        },
        {
          value: 10,
          label: '不详'
        },
        {
          value: 11,
          label: '其他'
        }
      ],
      // 1.居民身份证
      // 2.护照
      // 3.军官证
      // 4.港澳台通行证
      // 5.社保卡
      // 其他
      cardTypeList: [
        {
          value: 1,
          label: '居民身份证'
        },
        {
          value: 2,
          label: '护照'
        },
        {
          value: 3,
          label: '军官证'
        },
        {
          value: 4,
          label: '港澳台通行证'
        },
        {
          value: 5,
          label: '社保卡'
        },
        {
          value: 0,
          label: '其他'
        }
      ],
      nationList: [
        {
          value: '汉族',
          label: '汉族'
        },
        {
          value: '满族',
          label: '满族'
        },
        {
          value: '回族',
          label: '回族'
        },
        {
          value: '壮族',
          label: '壮族'
        },
        {
          value: '蒙古族',
          label: '蒙古族'
        }
      ],
      departmentList: [
        {
          value: '肿瘤科',
          label: '肿瘤科'
        },
        {
          value: '呼吸科',
          label: '呼吸科'
        },
        {
          value: '胸内科',
          label: '胸内科'
        },
        {
          value: '胸外科',
          label: '胸外科'
        },
        {
          value: '甲乳科',
          label: '甲乳科'
        },
        {
          value: '肾黑科',
          label: '肾黑科'
        },
        {
          value: '放化疗科',
          label: '放化疗科'
        },
        {
          value: '胃肠外科',
          label: '胃肠外科'
        },
        {
          value: '健康管理科',
          label: '健康管理科'
        }
      ],
      rules: {
        readCancerId: [
          {required: true, message: '请选择解读匹配癌种', trigger: 'blur'}
        ],
        idcard: [
          { required: true, message: '请输入证件号', trigger: ['change', 'blur'] },
          {validator: validateIdCard, trigger: 'blur'}
        ],
        cardType: [
          { required: true, message: '请输入证件类型', trigger: ['change', 'blur'] }
        ],
        contactTel: [
          {validator: util.validatePhone, trigger: 'blur'}
        ],
        familyContactType: [
          {validator: util.validatePhone, trigger: 'blur'}
        ],
        projectNum: [
          {required: true, message: '请输入项目ID/审批号', trigger: 'blur'}
        ]
      },
      regionList: [],
      diagnosedCancerSaveDialogVisible: false,
      diagnosedCancerSaveDialogData: {},
      clinicalPathologySaveDialogVisible: false,
      clinicalPathologySaveDialogData: {},
      detectSaveDialogVisible: false,
      detectSaveDialogData: {},
      tgrSaveDialogVisible: false,
      tgrSaveDialogData: {},
      treatmentSituationSaveDialogVisible: false,
      treatmentSituationSaveDialogData: {},
      familyHistorySaveDialogVisible: false,
      familyHistorySaveDialogData: {},
      sickHistorySaveDialogVisible: false,
      sickHistorySaveDialogData: {},
      sangerSaveDialogVisible: false,
      sangerSaveDialogData: {},
      pictureInfoSaveDialogVisible: false,
      pictureInfoSaveDialogData: {},
      keepLoginTimer: null,
      InfoCallbackDialogVisible: false
    }
  },
  methods: {
    // 图片回调
    handleFixImg (data) {
      this.pictureInfoSaveDialogData = {
        type: data.pictureInfoSaveDialogData.type,
        sampleBasicId: data.pictureInfoSaveDialogData.sampleBasicId,
        tableData: data.pictureInfoSaveDialogData.tableData
      }
      this.pictureInfoSaveDialogVisible = data.pictureInfoSaveDialogVisible
    },
    handleFixStatus (supplementaryNote) {
      this.supplementaryNote = supplementaryNote
    },

    /**
     * 这个弹窗的数据会很多，所以用户填写时间会很长
     * 现在打开这个弹窗后就开始计时，20分钟发一次请求
     * 用来保证只要这个弹窗开着，登录永远不会失效
     */
    keepLogin () {
      this.keepLoginTimer = setInterval(() => {
        this.$ajax({url: '/user/live_token'})
      }, 20 * 60 * 1000)
    },
    handleClose () {
      if (this.isFix) {
        this.$message.error('当前样本已修改，无法关闭。请点击保存提交，并录入存疑备注')
      } else {
        if (!this.handleChecked()) return
        this.visible = false
        clearInterval(this.keepLoginTimer)
        this.keepLoginTimer = null
        this.$emit('dialogCloseEvent')
        if (this.axiosSource) {
          this.axiosSource.cancel('手动取消')
          this.axiosSource = null
          this.CancelToken = null
        }
      }
    },
    // 检查是否存在他人打开弹窗
    async handleChecked () {
      // 审核类型不是2直接关
      // if (this.type !== 2) {
      return true
      // }
      // let res = await this.$ajax({
      //   url: '/sample/basic/lock_or_unlock_current_window',
      //   method: 'post',
      //   data: {
      //     sampleNum: this.sampleNum,
      //     operation: 'unlock'
      //   }
      // })
      // if (res.code !== this.SUCCESS_CODE) {
      //   this.$message.error(res.message)
      //   return false
      // } else {
      //   return true
      // }
    },
    // 证件类型切换
    handleCardTypeChange () {
      this.rules.idcard[0].required = this.form.cardType !== 0
    },
    handleOpen () {
      this.keepLogin()
      this.$nextTick(() => {
        this.CancelToken = axios.CancelToken
        this.axiosSource = this.CancelToken.source()
        this.saveLoading = false
        this.activeNames = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11']
        this.sampleInfoActiveNames = ['111', '112', '113', '114', '115', '116', '117']
        this.detectInfoActiveNames = ['51', '52', '53', '54', '55', '56', '57']
        this.treatmentActiveNames = ['61', '62', '63']
        this.sampleBasicId = this.psampleBasicId
        this.sampleNum = this.psampleNum
        this.type = this.ptype
        let disabled = false
        switch (this.ptype) {
          case 1:
            this.title = '信息补录-' + this.psampleNum
            break
          case 2:
            this.title = '信息审核-' + this.psampleNum
            disabled = true
            break
          case 3:
            this.title = '存疑修改-' + this.psampleNum
            break
          case 4:
            this.title = '查看明细-' + this.psampleNum
            disabled = true
            break
        }
        this.disabled = disabled
        this.getAuditStatus()
        this.getCancerList()
        this.getRegionList()
        this.getBasicInfoData()
        this.getOtherClinicalInfo()
      })
    },
    handleAuditStatusChange () {
      this.$ajax({
        url: '/sample/basic/update_or_get_approval_status',
        data: {
          sampleBasicId: this.sampleBasicId,
          fapprovalStatus: JSON.stringify(this.isAudit)
        }
      })
    },
    // 获取审核状态
    getAuditStatus () {
      this.$ajax({
        url: '/sample/basic/update_or_get_approval_status',
        data: {
          sampleBasicId: this.sampleBasicId
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          let data = result.data || ''
          this.isAudit = JSON.parse(data) || []
          if (this.isAudit.length < 10) {
            this.isAudit = new Array(11).fill(false)
          }
        } else {
          this.$message.error(result.message)
        }
      })
    },
    // 获取工业客户样本送检单
    getOtherClinicalInfo () {
      this.$ajax({
        url: '/sample/basic/get_third_clinical_info',
        method: 'get',
        data: {
          sampleBasicId: this.sampleBasicId
        }
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          let data = result.data || {}
          this.otherClinicalInfo = {
            sampleBasicId: data.fsampleBasicId,
            id: data.fid,
            sampleNum: data.fsampleNum,
            patientName: data.fpatientName,
            sex: data.fsex,
            idcard: data.fidcard,
            proName: data.fproName,
            sampleType: data.fsampleType,
            cancer: data.fcancer,
            sampleCollectTime: data.fsampleCollectTime,
            tissueCollectTime: data.ftissueCollectTime,
            samplingArea: data.fsamplingArea,
            clinicalDiagnose: data.fclinicalDiagnose,
            diagnosisAge: data.fdiagnosisAge,
            familyHistory: data.ffamilyHistory,
            familyHistoryRemarks: data.ffamilyHistoryRemarks,
            bloodTransHistory: data.fbloodTransHistory,
            operateHis: data.foperateHis,
            otherapy: data.fotherapy,
            drugHistory: data.fdrugHistory,
            clinicalStages: data.fclinicalStages,
            cancerMetastasisSites: data.fcancerMetastasisSites,
            geneTest: data.fgeneTest,
            iconography: data.ficonography,
            cancerTag: data.fcancerTag,
            remark: data.fremark,
            birthday: data.fbirthday,
            operationHistoryText: data.foperationHistoryText,
            medicineHistoryText: data.fmedicineHistoryText
          }
        } else {
          this.$message.error(result.message)
        }
      })
    },

    // 图片的操作
    handleImageAction (action, options = {}) {
      console.log(this.$refs.img.complete)
      const { zoomRate, rotateDeg, enableTransition } = {
        zoomRate: 0.5,
        rotateDeg: 90,
        enableTransition: true,
        ...options
      }
      const { transform } = this
      switch (action) {
        case 'zoomOut':
          if (transform.scale > 0.5) {
            transform.scale = parseFloat((transform.scale - zoomRate).toFixed(3))
          }
          break
        case 'zoomIn':
          transform.scale = parseFloat((transform.scale + zoomRate).toFixed(3))
          break
        case 'clocelise':
          transform.deg += rotateDeg
          break
        case 'anticlocelise':
          transform.deg -= rotateDeg
          break
      }
      transform.enableTransition = enableTransition
    },
    // 还原图片
    handleImageReset () {
      this.transform = {
        ...this.transform,
        scale: 1,
        deg: 0,
        offsetX: 0,
        offsetY: 0,
        enableTransition: false
      }
    },
    isShowSanger (productName) {
      const products = [
        'OncoH-Sanger验证', 'KRAS-qPCR', 'NRAS-qPCR', 'BRAF-qPCR', 'KRAS/BRAF-qPCR',
        'KRAS/NRAS/BRAF-qPCR', 'EGFR 实时荧光定量PCR基因检测（药企）',
        'KRAS荧光PCR基因检测', 'OncoH-CNV验证'
      ]
      return products.includes(productName)
      // return productName.includes('OncoH-Sanger验证') || productName.includes('KRAS-qPCR') ||
      //   productName.includes('NRAS-qPCR') || productName.includes('BRAF-qPCR') || productName.includes('KRAS/BRAF-qPCR') ||
      //   productName.includes('KRAS/NRAS/BRAF-qPCR') || productName.includes('EGFR 实时荧光定量PCR基因检测（药企）') || productName.includes('KRAS荧光PCR基因检测')
    },
    getRegionList () {
      this.$ajax({
        url: '/sample/basic/get_all_provice_city_region',
        data: {},
        cancelToken: this.axiosSource.token
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.regionList = result.data
        } else {
          this.$message.error(result.message)
        }
      })
    },
    getChildrenRegion (node, resolve) {
      if (node.level && !node.isLeaf) {
        this.$ajax({
          method: 'get',
          url: '/tRegion/get_regions',
          data: {
            parentId: node.data.parentId
          },
          cancelToken: this.axiosSource.token
        }).then(result => {
          if (result.code === this.SUCCESS_CODE) {
            let data = result.data
            let item = {}
            let list = []
            data.forEach(v => {
              item = {
                label: node.data.type === 'province' ? v.city : v.region,
                value: v.regionId,
                parentId: v.regionId,
                type: node.data.type === 'province' ? 'city' : 'region',
                leaf: node.data.type === 'city'
              }
              list.push(item)
            })
            resolve(list)
          } else {
            this.$message.error(result.message)
          }
        })
      } else {
        resolve([])
      }
    },
    // 判断是否修改特定内容
    async handleJudgeFixed () {
      console.log('开始校验更')
      for (let key in this.defaultData) {
        if (key !== 'cancerInfo' && this.defaultData[key] !== this.form[key]) {
          await this.handleTipsFixed()
          return
        } else {
          if (this.defaultData.cancerInfo.length !== this.cancerInfoList.length) {
            await this.handleTipsFixed()
            return
          } else {
            for (const item of this.defaultData.cancerInfo) {
              const index = this.defaultData.cancerInfo.indexOf(item)
              if (item.diagnosCancer !== this.cancerInfoList[index].diagnosCancer || JSON.stringify(item.cancerMetastasisSites) !== JSON.stringify(this.cancerInfoList[index].cancerMetastasisSites)) {
                await this.handleTipsFixed()
                return
              }
            }
          }
        }
      }
    },
    // 信息提示
    async handleTipsFixed () {
      await this.$confirm('样本已流入解读相关环节，请确认是否更改信息', '温馨提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        dangerouslyUseHTMLString: true,
        type: 'warning'
      })
      this.sampleSign = '更'
    },
    // 设置保存产品
    setProductOption () {
      let productList = []
      productList = this.productList.map(v => {
        return {
          sampleProductId: v.sampleProductId,
          sampleType: v.sampleType
        }
      })
      return productList
    },
    async handleChangeTagField (data) {
      const result = await this.$ajax({
        url: '/sample/basic/is_update_sign',
        data: data,
        cancelToken: this.axiosSource.token,
        showErrorMessageBox: false
      })
      return result.code === this.SUCCESS_CODE
    },
    async handleConfirm (status = null, needSync = 0) {
      let productList = this.setProductOption()
      // 控制解读癌种是否校验 保存不校验 补录完成校验
      this.rules.readCancerId[0].required = !(this.type === 1 && status === 1)
      this.$refs.form.validate(async valid => {
        if (!await this.handleCheck()) return
        this.sampleSign = ''
        if (valid) {
          if (this.type !== 2 && status === 3) {
            await this.handleEmpty()
          }
          let url = ''
          let data = ''
          let followupTime = this.form.followupTime.split('-')
          let quitTime = this.form.quitTime.split('-')
          let deathTime = this.form.deathTime.split('-')
          if (this.type === 3 && needSync !== 1) {
            this.$prompt('请输入存疑备注', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              inputValidator: function (value) {
                if (!value || !value.trim()) {
                  return '请输入存疑备注'
                }
              }
            }).then(async ({value}) => {
              this.form.doubtRemark = value
              url = '/sample/basic/save_sample'
              if (this.form.photoContent.indexOf('其他') !== -1) {
                this.form.photoContent.splice(this.form.photoContent.indexOf('其他'), 1)
                this.form.photoContent.push('其他')
                this.form.photoContent.push(this.form.otherPhotoContent)
              }
              if (this.form.otherBloodTransType) {
                this.form.bloodTransType = this.form.bloodTransType.concat(this.form.otherBloodTransType.split(','))
              }
              data = {
                needSync: needSync,
                sampleSign: {
                  fupdate: this.sampleSign
                },
                sampleClinical: {
                  sampleClinicalId: this.sampleClinicalId,
                  sampleBasicId: this.sampleBasicId,
                  clinicalDiagnose: this.form.clinicalDiagnose,
                  familyHistoryRemarks: this.form.familyHistoryRemarks,
                  treatmentHistory: this.form.treatmentHistory,
                  operationHistory: this.form.operationHistory,
                  medicationHistory: this.form.medicationHistory,
                  irrationalHistory: this.form.irrationalHistory,
                  medicalRecord: this.form.medicalRecord,
                  evaluationMark: this.form.evaluationMark,
                  evaluationDate: this.form.evaluationDate,
                  performanceStatus: this.form.performanceStatus,
                  recordDate: this.form.recordDate,
                  diagnosisDate: this.form.diagnosisDate,
                  diagnosisAge: this.form.diagnosisAge,
                  recrudescenceDate: this.form.recrudescenceDate,
                  clinicalDiagnoseMedical: this.form.clinicalDiagnoseMedical,
                  primarySite: this.form.primarySite,
                  tissueType: this.form.tissueType,
                  isAll: this.form.isAll,
                  clinDetail: this.form.clinDetail.toString(),
                  mainSuit: this.form.mainSuit,
                  photoContent: this.form.photoContent.toString(),
                  cancerDiagnosisDate: this.form.cancerDiagnosisDate,
                  cancerLabel: this.form.cancerLabel,
                  immune: this.form.immune,
                  cancerTag: this.form.cancerTag,
                  geneTest: this.form.geneTest,
                  iconography: this.form.iconography,
                  operateHis: this.form.operateHis,
                  otherapy: this.form.otherapy,
                  drugHistory: this.form.drugHistory,
                  familyHistory: this.form.familyHistory,
                  biocheExamTag: this.form.biocheExamTag,
                  sexhormoneTag: this.form.sexhormoneTag,
                  sickHistory: this.form.sickHistory
                },
                saveClinicalOtherInfoRequest: {
                  otherIllness: this.form.otherIllness,
                  height: this.form.height,
                  weight: this.form.weight,
                  occupation: this.form.occupation,
                  marriage: this.form.marriage,
                  growthHistory: this.form.growthHistory,
                  vaccinationHistory: this.form.vaccinationHistory,
                  smoking: this.form.smoking,
                  smokingLength: this.form.smokingLength,
                  smokingAmounts: this.form.smokingAmounts,
                  quitSmoke: this.form.quitSmoke,
                  quitTime: this.form.quitSmokeTime,
                  drink: this.form.drink,
                  drinkLength: this.form.drinkLength,
                  drinkIntake: this.form.drinkIntake,
                  otherHistory: this.form.otherHistory,
                  toxicHistory: this.form.toxicHistory,
                  cancer: this.form.cancer,
                  readCancerId: this.form.readCancerId.toString(),
                  isInClinicalResearch: this.form.isInClinicalResearch,
                  isQuitResearch: this.form.isQuitResearch,
                  quitYear: quitTime[0] || '',
                  quitMonth: quitTime[1] || '',
                  quitDay: quitTime[2] || '',
                  quitReason: this.form.quitReason,
                  quitReasonDesc: this.form.quitReasonDesc,
                  followupYear: followupTime[0] || '',
                  followupMonth: followupTime[1] || '',
                  followupDay: followupTime[2] || '',
                  followupSurvivalResult: this.form.followupSurvivalResult,
                  deathYear: deathTime[0] || '',
                  deathMonth: deathTime[1] || '',
                  deathDay: deathTime[2] || '',
                  deathReason: this.form.deathReason,
                  bytgrHPVFlag: this.HPV.bytgrFlag,
                  bytgrHBVFlag: this.HBV.bytgrFlag,
                  bytgrHCVFlag: this.HCV.bytgrFlag,
                  bytgrEBVFlag: this.EBV.bytgrFlag,
                  bytgrHIVFlag: this.HIV.bytgrFlag,
                  bytgrHPFlag: this.HP.bytgrFlag,
                  bytgrHPVUnknownReason: this.HPV.unknownReason,
                  bytgrHBVUnknownReason: this.HBV.unknownReason,
                  bytgrHCVUnknownReason: this.HCV.unknownReason,
                  bytgrEBVUnknownReason: this.EBV.unknownReason,
                  clinicalMkSta: status,
                  doubtRemark: this.form.doubtRemark
                },
                supplementSampleInformationRequest: {
                  fbedNum: this.form.fbedNum, // 床号
                  fpathologyNumber: this.form.fpathologyNumber, // 病理号
                  fmedicalRecordNum: this.form.fmedicalRecordNum, // 病历号
                  fpatientSource: this.form.fpatientSource, // 患者来源
                  fsupplementaryNote: this.supplementaryNote.length !== 0 ? JSON.stringify(this.supplementaryNote) : null,
                  sampleBasicId: this.sampleBasicId,
                  sampleNum: this.sampleNum,
                  sampleType: this.form.sampleType,
                  doubtRemark: this.form.doubtRemark,
                  baselineSendTimes: this.form.baselineSendTimes,
                  inspectionUnit: this.form.inspectionUnitId,
                  departments: this.form.departments,
                  doctor: this.form.doctor,
                  inspectionDoctor: this.form.inspectionDoctor,
                  finspectionHospital: this.form.finspectionHospital,
                  fadmissionNum: this.form.admissionNum,
                  fchargeCode: this.form.chargeCode,
                  foutpatientService: this.form.outpatientService,
                  doctorCode: this.form.doctorCode,
                  inspectionTime: this.form.inspectionTime,
                  originNum: this.form.originNum,
                  name: this.form.name,
                  sex: this.form.sex,
                  fage: this.form.age,
                  sampleCancerType: this.form.sampleCancerType,
                  ctDnaType: this.form.ctDnaType,
                  nation: this.form.nation,
                  idcard: this.form.idcard,
                  cardType: this.form.cardType,
                  year: this.form.year,
                  month: this.form.month,
                  day: this.form.day,
                  email: this.form.email,
                  birthday: this.form.birthday,
                  contactTel: this.form.contactTel,
                  familyContactType: this.form.familyContactType,
                  province: this.form.detailAddr[0] || '',
                  city: this.form.detailAddr[1] || '',
                  region: this.form.detailAddr[2] || '',
                  recipient: this.form.recipient,
                  contactAddr: this.form.contactAddr,
                  recipientTel: this.form.recipientTel,
                  fresearchCenterCode: this.form.fresearchCenterCode,
                  fresearchCenter: this.form.fresearchCenter,
                  fvisitCycle: this.form.fvisitCycle,
                  bloodSampleType: this.form.bloodSampleType,
                  bloodOtherType: this.form.bloodOtherType,
                  bloodCollectTime: this.form.bloodCollectTime,
                  bloodMl: this.form.bloodMl,
                  bloodTube: this.form.bloodTube,
                  fspecialTakeBloodPosition: this.form.fspecialTakeBloodPosition,
                  humorSampleType: this.form.humorSampleType,
                  humorOtherType: this.form.humorOtherType,
                  humorCollectTime: this.form.humorCollectTime,
                  humorMl: this.form.humorMl,
                  humorTube: this.form.humorTube,
                  bloodTransHistory: this.form.bloodTransHistory,
                  transplantHistory: this.form.transplantHistory,
                  transplantTime: this.form.transplantTime,
                  bloodTransType: this.form.bloodTransType.toString(),
                  bloodTransDate: this.form.bloodTransDate,
                  tissueSampleType: this.form.tissueSampleType,
                  fcryopreservation: this.form.fcryopreservation.length === 1 ? this.form.fcryopreservation[0] : 0,
                  tissueOtherType: this.form.tissueOtherType,
                  tissueCollectTime: this.form.tissueCollectTime,
                  tissueTube: this.form.tissueTube,
                  tissueMm: this.form.tissueMm,
                  samplingArea: this.form.samplingArea,
                  samplingMode: this.form.samplingMode,
                  otherSamplingMode: this.form.otherSamplingMode,
                  cellSampleType: this.form.cellSampleType,
                  cellOtherType: this.form.cellOtherType,
                  cellCollectTime: this.form.cellCollectTime,
                  cellMl: this.form.cellMl,
                  cellTube: this.form.cellTube,
                  nucleicAcidSampleType: this.form.nucleicAcidSampleType,
                  nucleicAcidOtherType: this.form.nucleicAcidOtherType,
                  nucleicAcidCollectTime: this.form.nucleicAcidCollectTime,
                  nucleicAcidMl: this.form.nucleicAcidMl,
                  nucleicAcidTube: this.form.nucleicAcidTube,
                  detectionTimes: this.form.detectionTimes,
                  lastSampleType: this.form.lastSampleType,
                  lastSampleNum: this.form.lastSampleNum,
                  sampleCollectMan: this.form.sampleCollectMan,
                  remark: this.form.remark,
                  inspectionInformedUrl: this.form.inspectionInformedUrl,
                  sampleOrderUrl: JSON.stringify(this.sampleOrderImg),
                  makeupStatus: status,
                  menarche: this.form.menarche,
                  menopause: this.form.menopause,
                  birthFirst: this.form.birthFirst,
                  isLongUsehormone: this.form.isLongUsehormone,
                  isLongUseacyeterion: this.form.isLongUseacyeterion,
                  mildewfoodEatHis: this.form.mildewfoodEatHis,
                  exerciseHis: this.form.exerciseHis,
                  eatburnfoodHis: this.form.eatburnfoodHis,
                  redmeatHis: this.form.redmeatHis,
                  fruitsVegetablesHis: this.form.fruitsVegetablesHis,
                  isInClinicalResearch: this.form.isInClinicalResearch,
                  isQuitResearch: this.form.isQuitResearch,
                  followupSurvivalResult: this.form.followupSurvivalResult,
                  familyOtherIllness: this.form.familyOtherIllness,
                  thirdpartySampleNum: this.form.thirdpartySampleNum
                },
                productList: productList
              }
              this.saveLoading = true
              if (this.type === 3 && ['报告复核', '报告解读', '报告发放', '已发报告'].includes(this.testingLink)) {
                // 校验 是否跟新更标签
                if (await this.handleChangeTagField(data)) {
                  await this.$confirm('样本已流入解读相关环节，请确认是否更改信息', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                  })
                }
              }
              this.$ajax({
                url: url,
                data: data,
                cancelToken: this.axiosSource.token
              }).then(result => {
                if (result.code === this.SUCCESS_CODE) {
                  if (needSync === 1) {
                    this.$message.success('信息同步成功')
                  } else {
                    this.$message.success('保存成功')
                    this.$emit('saveInfoDialogConfirmEvent')
                  }
                } else {
                  this.$message.error(result.message)
                }
              }).catch().finally(() => {
                this.saveLoading = false
              })
            }).catch(() => {
            })
          } else {
            if (this.type === 1 || needSync === 1) {
              url = '/sample/basic/save_sample'
              if (this.form.photoContent.indexOf('其他') !== -1) {
                this.form.photoContent.push(this.form.otherPhotoContent)
              }
              if (this.form.otherBloodTransType) {
                this.form.bloodTransType = this.form.bloodTransType.concat(this.form.otherBloodTransType.split(','))
              }
              data = {
                needSync: needSync,
                sampleSign: {
                  fupdate: this.sampleSign
                },
                sampleClinical: {
                  sampleClinicalId: this.sampleClinicalId,
                  sampleBasicId: this.sampleBasicId,
                  clinicalDiagnose: this.form.clinicalDiagnose,
                  familyHistoryRemarks: this.form.familyHistoryRemarks,
                  treatmentHistory: this.form.treatmentHistory,
                  operationHistory: this.form.operationHistory,
                  medicationHistory: this.form.medicationHistory,
                  irrationalHistory: this.form.irrationalHistory,
                  medicalRecord: this.form.medicalRecord,
                  evaluationMark: this.form.evaluationMark,
                  evaluationDate: this.form.evaluationDate,
                  performanceStatus: this.form.performanceStatus,
                  recordDate: this.form.recordDate,
                  diagnosisDate: this.form.diagnosisDate,
                  diagnosisAge: this.form.diagnosisAge,
                  recrudescenceDate: this.form.recrudescenceDate,
                  clinicalDiagnoseMedical: this.form.clinicalDiagnoseMedical,
                  primarySite: this.form.primarySite,
                  tissueType: this.form.tissueType,
                  isAll: this.form.isAll,
                  clinDetail: this.form.clinDetail.toString(),
                  mainSuit: this.form.mainSuit,
                  photoContent: this.form.photoContent.toString(),
                  cancerDiagnosisDate: this.form.cancerDiagnosisDate,
                  cancerLabel: this.form.cancerLabel,
                  immune: this.form.immune,
                  cancerTag: this.form.cancerTag,
                  geneTest: this.form.geneTest,
                  iconography: this.form.iconography,
                  operateHis: this.form.operateHis,
                  otherapy: this.form.otherapy,
                  drugHistory: this.form.drugHistory,
                  familyHistory: this.form.familyHistory,
                  biocheExamTag: this.form.biocheExamTag,
                  sexhormoneTag: this.form.sexhormoneTag,
                  sickHistory: this.form.sickHistory
                },
                saveClinicalOtherInfoRequest: {
                  otherIllness: this.form.otherIllness,
                  height: this.form.height,
                  weight: this.form.weight,
                  occupation: this.form.occupation,
                  marriage: this.form.marriage,
                  growthHistory: this.form.growthHistory,
                  vaccinationHistory: this.form.vaccinationHistory,
                  smoking: this.form.smoking,
                  smokingLength: this.form.smokingLength,
                  smokingAmounts: this.form.smokingAmounts,
                  quitSmoke: this.form.quitSmoke,
                  quitTime: this.form.quitSmokeTime,
                  drink: this.form.drink,
                  drinkLength: this.form.drinkLength,
                  drinkIntake: this.form.drinkIntake,
                  otherHistory: this.form.otherHistory,
                  toxicHistory: this.form.toxicHistory,
                  cancer: this.form.cancer,
                  readCancerId: this.form.readCancerId.toString(),
                  isInClinicalResearch: this.form.isInClinicalResearch,
                  isQuitResearch: this.form.isQuitResearch,
                  quitYear: quitTime[0] || '',
                  quitMonth: quitTime[1] || '',
                  quitDay: quitTime[2] || '',
                  quitReason: this.form.quitReason,
                  quitReasonDesc: this.form.quitReasonDesc,
                  followupYear: followupTime[0] || '',
                  followupMonth: followupTime[1] || '',
                  followupDay: followupTime[2] || '',
                  followupSurvivalResult: this.form.followupSurvivalResult,
                  deathYear: deathTime[0] || '',
                  deathMonth: deathTime[1] || '',
                  deathDay: deathTime[2] || '',
                  deathReason: this.form.deathReason,
                  bytgrHPVFlag: this.HPV.bytgrFlag,
                  bytgrHBVFlag: this.HBV.bytgrFlag,
                  bytgrHCVFlag: this.HCV.bytgrFlag,
                  bytgrEBVFlag: this.EBV.bytgrFlag,
                  bytgrHIVFlag: this.HIV.bytgrFlag,
                  bytgrHPFlag: this.HP.bytgrFlag,
                  bytgrHPVUnknownReason: this.HPV.unknownReason,
                  bytgrHBVUnknownReason: this.HBV.unknownReason,
                  bytgrHCVUnknownReason: this.HCV.unknownReason,
                  bytgrEBVUnknownReason: this.EBV.unknownReason,
                  clinicalMkSta: status,
                  doubtRemark: this.form.doubtRemark
                },
                supplementSampleInformationRequest: {
                  fbedNum: this.form.fbedNum, // 床号
                  fpathologyNumber: this.form.fpathologyNumber, // 病理号
                  fmedicalRecordNum: this.form.fmedicalRecordNum, // 病历号
                  fpatientSource: this.form.fpatientSource, // 患者来源
                  fsupplementaryNote: this.supplementaryNote.length !== 0 ? JSON.stringify(this.supplementaryNote) : null,
                  sampleBasicId: this.sampleBasicId,
                  sampleNum: this.sampleNum,
                  sampleType: this.form.sampleType,
                  doubtRemark: this.form.doubtRemark,
                  baselineSendTimes: this.form.baselineSendTimes,
                  inspectionUnit: this.form.inspectionUnitId,
                  departments: this.form.departments,
                  doctor: this.form.doctor,
                  inspectionDoctor: this.form.inspectionDoctor,
                  finspectionHospital: this.form.finspectionHospital,
                  fadmissionNum: this.form.admissionNum,
                  fchargeCode: this.form.chargeCode,
                  foutpatientService: this.form.outpatientService,
                  doctorCode: this.form.doctorCode,
                  inspectionTime: this.form.inspectionTime,
                  originNum: this.form.originNum,
                  name: this.form.name,
                  sex: this.form.sex,
                  fage: this.form.age,
                  sampleCancerType: this.form.sampleCancerType,
                  ctDnaType: this.form.ctDnaType,
                  nation: this.form.nation,
                  idcard: this.form.idcard,
                  cardType: this.form.cardType,
                  year: this.form.year,
                  month: this.form.month,
                  day: this.form.day,
                  email: this.form.email,
                  birthday: this.form.birthday,
                  contactTel: this.form.contactTel,
                  familyContactType: this.form.familyContactType,
                  province: this.form.detailAddr[0] || '',
                  city: this.form.detailAddr[1] || '',
                  region: this.form.detailAddr[2] || '',
                  recipient: this.form.recipient,
                  contactAddr: this.form.contactAddr,
                  recipientTel: this.form.recipientTel,
                  fresearchCenterCode: this.form.fresearchCenterCode,
                  fresearchCenter: this.form.fresearchCenter,
                  fvisitCycle: this.form.fvisitCycle,
                  bloodSampleType: this.form.bloodSampleType,
                  bloodOtherType: this.form.bloodOtherType,
                  bloodCollectTime: this.form.bloodCollectTime,
                  bloodMl: this.form.bloodMl,
                  bloodTube: this.form.bloodTube,
                  fspecialTakeBloodPosition: this.form.fspecialTakeBloodPosition,
                  humorSampleType: this.form.humorSampleType,
                  humorOtherType: this.form.humorOtherType,
                  humorCollectTime: this.form.humorCollectTime,
                  humorMl: this.form.humorMl,
                  humorTube: this.form.humorTube,
                  bloodTransHistory: this.form.bloodTransHistory,
                  transplantHistory: this.form.transplantHistory,
                  transplantTime: this.form.transplantTime,
                  bloodTransType: this.form.bloodTransType.toString(),
                  bloodTransDate: this.form.bloodTransDate,
                  tissueSampleType: this.form.tissueSampleType,
                  fcryopreservation: this.form.fcryopreservation.length === 1 ? this.form.fcryopreservation[0] : 0,
                  tissueOtherType: this.form.tissueOtherType,
                  tissueCollectTime: this.form.tissueCollectTime,
                  tissueTube: this.form.tissueTube,
                  tissueMm: this.form.tissueMm,
                  samplingArea: this.form.samplingArea,
                  samplingMode: this.form.samplingMode,
                  otherSamplingMode: this.form.otherSamplingMode,
                  cellSampleType: this.form.cellSampleType,
                  cellOtherType: this.form.cellOtherType,
                  cellCollectTime: this.form.cellCollectTime,
                  cellMl: this.form.cellMl,
                  cellTube: this.form.cellTube,
                  nucleicAcidSampleType: this.form.nucleicAcidSampleType,
                  nucleicAcidOtherType: this.form.nucleicAcidOtherType,
                  nucleicAcidCollectTime: this.form.nucleicAcidCollectTime,
                  nucleicAcidMl: this.form.nucleicAcidMl,
                  nucleicAcidTube: this.form.nucleicAcidTube,
                  detectionTimes: this.form.detectionTimes,
                  lastSampleType: this.form.lastSampleType,
                  lastSampleNum: this.form.lastSampleNum,
                  sampleCollectMan: this.form.sampleCollectMan,
                  remark: this.form.remark,
                  inspectionInformedUrl: this.form.inspectionInformedUrl,
                  sampleOrderUrl: JSON.stringify(this.sampleOrderImg),
                  makeupStatus: status,
                  menarche: this.form.menarche,
                  menopause: this.form.menopause,
                  birthFirst: this.form.birthFirst,
                  isLongUsehormone: this.form.isLongUsehormone,
                  isLongUseacyeterion: this.form.isLongUseacyeterion,
                  mildewfoodEatHis: this.form.mildewfoodEatHis,
                  exerciseHis: this.form.exerciseHis,
                  eatburnfoodHis: this.form.eatburnfoodHis,
                  redmeatHis: this.form.redmeatHis,
                  fruitsVegetablesHis: this.form.fruitsVegetablesHis,
                  isInClinicalResearch: this.form.isInClinicalResearch,
                  isQuitResearch: this.form.isQuitResearch,
                  followupSurvivalResult: this.form.followupSurvivalResult,
                  familyOtherIllness: this.form.familyOtherIllness,
                  thirdpartySampleNum: this.form.thirdpartySampleNum
                },
                productList: productList
              }
            } else {
              url = '/sample/basic/audit_sample_info'
              data = {
                sampleBasicId: this.sampleBasicId,
                clinicalMkSta: 4,
                makeupStatus: 4,
                rejectRemark: ''
              }
            }
            this.saveLoading = true
            if (this.type === 3 && ['报告复核', '报告解读', '报告发放', '已发报告'].includes(this.testingLink)) {
              // 校验 是否跟新更标签
              if (await this.handleChangeTagField(data)) {
                await this.$confirm('样本已流入解读相关环节，请确认是否更改信息', '提示', {
                  confirmButtonText: '确定',
                  cancelButtonText: '取消',
                  type: 'warning'
                })
              }
            }
            this.$ajax({
              url: url,
              data: data,
              cancelToken: this.axiosSource.token
            }).then(result => {
              if (result.code === this.SUCCESS_CODE) {
                this.$message.success('保存成功')
                if (status === 1) {
                  this.getCancerList()
                  this.getRegionList()
                  this.getBasicInfoData()
                } else {
                  if (!this.handleChecked()) return
                  this.$emit('saveInfoDialogConfirmEvent')
                }
              } else {
                this.$message.error(result.message)
              }
            }).catch().finally(() => {
              this.saveLoading = false
            })
          }
        }
      })
    },
    async handleCheck () {
      const isSupplement = this.type === 1 || this.type === 3
      let message = ''
      isSupplement ? message = '有补充信息未处理，无法完成补录<br>请继续补录信息' : message = '有补充信息未处理，无法审核通过<br>，请继续补录。'
      let status = this.supplementaryNote.some(v => !v.status)
      let res = await this.$ajax({
        url: '/sample/basic/check_supplementary_information',
        data: {
          sampleBasicId: this.sampleBasicId,
          supplementaryInformationTotal: this.supplementaryNote.length
        }
      })
      // 是否有新修改信息
      let hasNewBackendRecords = false
      // 如果有新修改信息，则提示并覆盖
      if (res.code !== this.SUCCESS_CODE) {
        if (Array.isArray(res.data)) {
          let supplementaryNote = res.data || []
          supplementaryNote = supplementaryNote.map(item => {
            const records = this.supplementaryNote.find(v => v.date === item.date) || {}
            if (records.status) {
              item.status = records.status
            }
            return item
          })
          this.supplementaryNote = supplementaryNote
          hasNewBackendRecords = true
        }
      }
      let result = true
      if (status || hasNewBackendRecords) {
        try {
          await this.$confirm(message, '重要提示', {
            cancelButtonText: '确定',
            dangerouslyUseHTMLString: true,
            showConfirmButton: false,
            type: 'error'
          })
          result = false
        } catch (e) {
          console.log(e)
          result = false
          if (!isSupplement) {
            this.handleRejectConfirm('有补充信息未处理')
          }
        }
      }
      return result
    },
    // 字段非空帕努单
    async handleEmpty () {
      let errorMsg = ''
      let {
        name, sex, idcard, samplingArea, birthday,
        bloodSampleType, bloodCollectTime,
        humorSampleType, humorCollectTime,
        tissueSampleType, tissueCollectTime,
        cellSampleType, cellCollectTime,
        nucleicAcidSampleType, nucleicAcidCollectTime
      } = this.form
      if (!name) errorMsg += '姓名为空</br>'
      if ((sex + '') === '') errorMsg += '性别为空</br>'
      if (!idcard) errorMsg += '证件号为空</br>'
      if (!birthday) errorMsg += '出生年月为空</br>'
      if (bloodSampleType && !bloodCollectTime) errorMsg += '血液样本采集时间为空</br>'
      if (humorSampleType && !humorCollectTime) errorMsg += '体液样本采集时间为空</br>'
      if (tissueSampleType && !tissueCollectTime) errorMsg += '组织样本采集时间为空</br>'
      if (tissueSampleType && !samplingArea) errorMsg += '组织样本采集部位为空</br>'
      if (cellSampleType && !cellCollectTime) errorMsg += '细胞样本采集时间为空</br>'
      if (nucleicAcidSampleType && !nucleicAcidCollectTime) errorMsg += '核酸样本采集时间为空</br>'
      if (errorMsg !== '') {
        await this.$confirm(errorMsg, '温馨提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          dangerouslyUseHTMLString: true,
          type: 'warning'
        })
      }
    },
    handleChange (val) {},
    handleTabChange () {
      this.deg = 0
      this.multiples = 100
      if (this.imgType === 'fixInfo') {
        this.supplementaryNote = this.form.supplementaryNote
      }
      switch (this.imgType) {
        case 'imgNames':
          if (this.imgNames.length !== 0) {
            this.imgSrc = this.imgNames[0].fileAbsolutePath || ''
          } else {
            this.imgSrc = ''
          }
          break
        case 'followupImgNames':
          if (this.followupImgNames.length !== 0) {
            this.imgSrc = this.followupImgNames[0].fileAbsolutePath || ''
          } else {
            this.imgSrc = ''
          }
          break
        case 'sampleOrderImg':
          if (this.sampleOrderImg.length !== 0) {
            this.imgSrc = this.sampleOrderImg[0].fileAbsolutePath || ''
          } else {
            this.imgSrc = ''
          }
          break
        case 'supplementaryImgs':
          if (this.supplementaryImgs.length !== 0) {
            this.imgSrc = this.supplementaryImgs[0].fileAbsolutePath || ''
          } else {
            this.imgSrc = ''
          }
          break
      }
    },

    // 信息反馈
    handleInfoCallback () {
      this.InfoCallbackDialogVisible = true
    },
    handleCallbackDialogConfirm () {},

    // 随访异常提示框
    handleFollowUpErrorTips () {
      this.followupDialogVisible = true
    },
    // 全选
    handleSelectAll (selection, type) {
      if (type === 'sanger') {
        this.sangerTableSelectRows.clear()
        selection.forEach((row) => {
          this.sangerTableSelectRows.set(row.id, row)
        })
      }
    },
    // 点击行
    handleRowClick (row, column, event, type) {
      if (type === 'otherapy') {
        this.$refs.radiotherapyTable.toggleRowSelection(row, !this.radiotherapyTableSelectRow.has(row.radiotherapyId))
      } else if (type === 'drugHistory') {
        this.$refs.drugHistoryTable.toggleRowSelection(row, !this.drugHistoryTableSelectRow.has(row.useDrugId))
      } else {
        this.$refs.sangerTable.toggleRowSelection(row, !this.sangerTableSelectRows.has(row.id))
      }
      this.handleSelectTable(undefined, row, type)
    },
    // 选中行
    handleSelectTable (selection, row, type) {
      if (type === 'otherapy') {
        this.radiotherapyTableSelectRow.has(row.radiotherapyId) ? this.radiotherapyTableSelectRow.delete(row.radiotherapyId) : this.radiotherapyTableSelectRow.set(row.radiotherapyId, row)
      } else if (type === 'drugHistory') {
        this.drugHistoryTableSelectRow.has(row.useDrugId) ? this.drugHistoryTableSelectRow.delete(row.useDrugId) : this.drugHistoryTableSelectRow.set(row.useDrugId, row)
      } else {
        this.sangerTableSelectRows.has(row.id) ? this.sangerTableSelectRows.delete(row.id) : this.sangerTableSelectRows.set(row.id, row)
      }
    },

    // 新增诊断癌种信息
    handleAddCancerInfo () {
      this.diagnosedCancerSaveDialogData = {
        sampleClinicalCancerInfoId: null,
        sampleBasicId: this.sampleBasicId,
        birthday: this.form.birthday ? this.form.birthday : '',
        diagnosTime: '',
        recrudescenceTime: ''
      }
      this.diagnosedCancerSaveDialogVisible = true
    },
    // 合并产品表格
    spanMethod ({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        let length = row.isSample ? 1 : 0
        return {
          rowspan: row.childProList.length === 0 ? length : row.childProList.length,
          colspan: 1
        }
      }
    },

    // 获取信息补录基本字段
    getBasicInfoData () {
      this.$ajax({
        loadingDom: '.infoContent',
        url: '/sample/basic/get_clinical_info',
        data: {
          sampleBasicId: this.sampleBasicId,
          sampleNum: this.sampleNum
        },
        cancelToken: this.axiosSource.token
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          let data = result.data
          if (data && JSON.stringify(data) !== '{}') {
            try {
              let otherBloodTransType = []
              this.form = {
                supplementaryNote: JSON.parse(data.basic.fsupplementaryNote) || [],
                productNames: data.productNames,
                projectName: data.projectName,
                productName: data.productNames || data.projectName,
                detailAddr: [],
                familyHistoryRemarks: data.clinical.familyHistoryRemarks,
                detailAddrText: data.detailAddr ? data.detailAddr.replace(/ /g, ' / ') : '请选择',
                clinicalDiagnose: data.clinical.clinicalDiagnose,
                treatmentHistory: data.clinical.treatmentHistory,
                operationHistory: data.clinical.operationHistory,
                medicationHistory: data.clinical.medicationHistory,
                irrationalHistory: data.clinical.irrationalHistory,
                medicalRecord: data.clinical.medicalRecord,
                evaluationMark: data.clinical.evaluationMark,
                evaluationDate: data.clinical.evaluationDate || '',
                performanceStatus: data.clinical.performanceStatus,
                recordDate: data.clinical.recordDate || '',
                diagnosisDate: data.clinical.diagnosisDate,
                diagnosisAge: data.clinical.diagnosisAge,
                recrudescenceDate: data.clinical.recrudescenceDate,
                clinicalDiagnoseMedical: data.clinical.clinicalDiagnoseMedical,
                primarySite: data.clinical.primarySite,
                tissueType: data.clinical.tissueType,
                isAll: data.clinical.isAll ? data.clinical.isAll : '完整',
                clinDetail: data.clinical.clinDetail ? data.clinical.clinDetail.split(',') : [],
                mainSuit: data.clinical.mainSuit,
                photoContent: data.clinical.photoContent ? data.clinical.photoContent.split(',') : [],
                otherPhotoContent: '',
                readCancerId: data.basic.cancertypeId ? data.basic.cancertypeId.split(',').map(v => Number(v)) : [],
                cancerDiagnosisDate: data.clinical.cancerDiagnosisDate,
                cancerLabel: data.clinical.cancerLabel,
                immune: data.clinical.immune,
                cancerTag: data.clinical.cancerTag,
                geneTest: data.clinical.geneTest,
                iconography: data.clinical.iconography,
                operateHis: data.clinical.operateHis,
                otherapy: data.clinical.otherapy,
                drugHistory: data.clinical.drugHistory,
                familyHistory: data.clinical.familyHistory,
                biocheExamTag: data.clinical.biocheExamTag,
                sexhormoneTag: data.clinical.sexhormoneTag,
                sickHistory: data.clinical.sickHistory,
                sampleNum: data.basic.sampleNum,
                patientID: data.basic.patientID,
                thirdpartySampleNum: data.basic.thirdpartySampleNum,
                name: data.basic.name,
                sex: data.basic.sex,
                age: data.basic.fage || util.getAge(data.basic.birthday),
                originNum: data.basic.originNum,
                nation: data.basic.nation,
                nativePlace: data.basic.nativePlace,
                illnessCaseStatus: data.basic.illnessCaseStatus,
                idcard: data.basic.idcard,
                cardType: data.basic.cardType,
                birthday: data.basic.birthday,
                email: data.basic.email,
                sampleType: data.basic.sampleType,
                province: data.basic.province,
                city: data.basic.city,
                region: data.basic.region,
                sampleCancerType: data.basic.sampleCancerType,
                ctDnaType: data.basic.ctDnaType,
                sampleCollectDate: data.basic.sampleCollectDate,
                sampleCollectMan: data.basic.sampleCollectMan,
                contactTel: data.basic.contactTel,
                familyContactType: data.basic.familyContactType,
                contactAddr: data.basic.contactAddr,
                recipient: data.basic.recipient,
                recipientTel: data.basic.recipientTel,
                samleUseType: data.basic.samleUseType,
                projectNum: data.basic.projectNum,
                chargeType: data.basic.chargeType,
                educationLevel: data.basic.educationLevel,
                cancertypeId: data.basic.cancertypeId,
                marriage: data.basic.marriage,
                hreportType: data.basic.hreportType,
                height: data.basic.height,
                weight: data.basic.weight,
                occupation: data.basic.occupation,
                gestationalWeeks: data.basic.gestationalWeeks,
                growthHistory: data.basic.growthHistory,
                vaccinationHistory: data.basic.vaccinationHistory,
                smoking: data.basic.smoking ? Number(data.basic.smoking) : '',
                smokingLength: data.basic.smokingLength,
                drink: data.basic.drink ? Number(data.basic.drink) : '',
                smokingAmounts: data.basic.smokingAmounts,
                drinkLength: data.basic.drinkLength,
                quitSmoke: data.basic.quitSmoke ? Number(data.basic.quitSmoke) : '',
                quitSmokeTime: data.basic.quitTime,
                drinkIntake: data.basic.drinkIntake,
                otherHistory: data.basic.otherHistory,
                alcoholDrugHistory: data.basic.alcoholDrugHistory,
                toxicHistory: data.basic.toxicHistory,
                fresearchCenterCode: data.basic.fresearchCenterCode,
                fresearchCenter: data.basic.fresearchCenter,
                fvisitCycle: data.basic.fvisitCycle,
                remark: data.basic.remark,
                sampleConfirmTime: data.basic.sampleConfirmTime,
                createTime: data.basic.createTime,
                makeupStatus: data.basic.makeupStatus,
                creator: data.basic.creator,
                makeupTime: data.basic.makeupTime,
                makeuper: data.basic.makeuper,
                rejectRemark: data.basic.rejectRemark,
                auditTime: data.basic.auditTime,
                auditor: data.basic.auditor,
                doubter: data.basic.doubter,
                doubtTime: data.basic.doubtTime,
                doubtRemark: data.basic.doubtRemark,
                basicStatus: data.basic.clinicalStatus,
                clinicalMkSta: data.basic.clinicalMkSta,
                clinicalRejectRmk: data.basic.clinicalRejectRmk,
                clinicalMkMan: data.basic.clinicalMkMan,
                menarche: data.basic.menarche,
                menopause: data.basic.menopause,
                birthFirst: data.basic.birthFirst,
                isLongUsehormone: data.basic.isLongUsehormone,
                isLongUseacyeterion: data.basic.isLongUseacyeterion,
                mildewfoodEatHis: data.basic.mildewfoodEatHis,
                exerciseHis: data.basic.exerciseHis,
                eatburnfoodHis: data.basic.eatburnfoodHis,
                redmeatHis: data.basic.redmeatHis,
                isInClinicalResearch: data.basic.isInClinicalResearch ? data.basic.isInClinicalResearch : '',
                isQuitResearch: data.basic.isQuitResearch ? data.basic.isQuitResearch : '',
                fruitsVegetablesHis: data.basic.fruitsVegetablesHis,
                quitReason: data.basic.quitReason ? data.basic.quitReason : '',
                quitReasonDesc: data.basic.quitReasonDesc,
                followupSurvivalResult: data.basic.followupSurvivalResult,
                deathReason: data.basic.deathReason,
                quitYear: data.basic.quitYear,
                quitMonth: data.basic.quitMonth,
                quitDay: data.basic.quitDay,
                quitTime: `${data.basic.quitYear || ''}${data.basic.quitMonth ? '-' + data.basic.quitMonth : ''}${data.basic.quitDay ? '-' + data.basic.quitDay : ''}`,
                followupYear: data.basic.followupYear,
                followupMonth: data.basic.followupMonth,
                followupDay: data.basic.followupDay,
                followupTime: `${data.basic.followupYear || ''}${data.basic.followupMonth ? '-' + data.basic.followupMonth : ''}${data.basic.followupDay ? '-' + data.basic.followupDay : ''}`,
                deathYear: data.basic.deathYear,
                deathMonth: data.basic.deathMonth,
                deathDay: data.basic.deathDay,
                deathTime: `${data.basic.deathYear || ''}${data.basic.deathMonth ? '-' + data.basic.deathMonth : ''}${data.basic.deathDay ? '-' + data.basic.deathDay : ''}`,
                fbedNum: data.basic.fbedNum, // 床号
                fpathologyNumber: data.basic.fpathologyNumber, // 病理号
                fmedicalRecordNum: data.basic.fmedicalRecordNum, // 病历号
                fpatientSource: data.basic.fpatientSource, // 患者来源
                inspectionUnitId: data.inspection.inspectionUnitId,
                customerCode: data.inspection.customerCode,
                doctorCode: data.inspection.doctorCode,
                doctor: data.inspection.doctor,
                inspectionDoctor: data.inspection.inspectionDoctor,
                admissionNum: data.basic.fadmissionNum,
                chargeCode: data.basic.fchargeCode,
                outpatientService: data.basic.foutpatientService,
                departments: data.inspection.departments,
                cancer: data.inspection.cancer,
                bloodTransHistory: data.inspection.bloodTransHistory,
                transplantHistory: data.inspection.transplantHistory,
                transplantTime: data.inspection.transplantTime,
                bloodTransDate: data.inspection.bloodTransDate,
                baselineSendTimes: data.inspection.baselineSendTimes,
                finspectionHospital: data.inspection.finspectionHospital,
                bloodTransType: [],
                otherBloodTransType: '',
                lastSampleType: data.inspection.lastSampleType,
                lastSampleNum: data.inspection.lastSampleNum,
                detectionTimes: data.inspection.detectionTimes,
                sampleOrderUrl: data.inspection.sampleOrderUrl,
                inspectionInformedUrl: data.inspection.inspectionInformedUrl,
                otherIllness: data.inspection.otherIllness,
                personalTumorHistory: data.inspection.personalTumorHistory,
                familyOtherIllness: data.inspection.familyOtherIllness,
                familyTumorHistory: data.inspection.familyTumorHistory,
                relationship: data.inspection.relationship,
                familyClinicalDiagnosis: data.inspection.familyClinicalDiagnosis,
                familyDiagnoseAge: data.inspection.familyDiagnoseAge,
                salesMan: data.inspection.salesMan,
                salesManName: data.inspection.salesManName,
                salesManPhone: data.inspection.salesManPhone,
                factotum: data.inspection.factotum,
                factotumName: data.inspection.factotumName,
                factotumPhone: data.inspection.factotumPhone,
                inspectionTime: data.inspection.inspectionTime,
                bloodSampleType: data.inspection.bloodSampleType,
                bloodOtherType: data.inspection.bloodOtherType,
                bloodCollectTime: data.inspection.bloodCollectTime,
                bloodMl: data.inspection.bloodMl,
                bloodTube: data.inspection.bloodTube,
                fspecialTakeBloodPosition: data.inspection.fspecialTakeBloodPosition,
                humorSampleType: data.inspection.humorSampleType,
                humorOtherType: data.inspection.humorOtherType,
                humorCollectTime: data.inspection.humorCollectTime,
                humorMl: data.inspection.humorMl,
                humorTube: data.inspection.humorTube,
                tissueSampleType: data.inspection.tissueSampleType,
                fcryopreservation: data.inspection.fcryopreservation === 1 ? [data.inspection.fcryopreservation] : [],
                tissueOtherType: data.inspection.tissueOtherType,
                tissueCollectTime: data.inspection.tissueCollectTime,
                tissueTube: data.inspection.tissueTube,
                tissueMm: data.inspection.tissueMm,
                samplingArea: data.inspection.samplingArea,
                samplingMode: data.inspection.samplingMode,
                otherSamplingMode: data.inspection.otherSamplingMode,
                cellSampleType: data.inspection.cellSampleType,
                cellOtherType: data.inspection.cellOtherType,
                cellCollectTime: data.inspection.cellCollectTime,
                cellMl: data.inspection.cellMl,
                cellTube: data.inspection.cellTube,
                nucleicAcidSampleType: data.inspection.nucleicAcidSampleType,
                nucleicAcidOtherType: data.inspection.nucleicAcidOtherType,
                nucleicAcidCollectTime: data.inspection.nucleicAcidCollectTime,
                nucleicAcidMl: data.inspection.nucleicAcidMl,
                nucleicAcidTube: data.inspection.nucleicAcidTube,
                inspectionUnitName: data.inspection.inspectionUnitName
              }
              this.defaultData = {
                name: data.basic.name, // 姓名
                sex: data.basic.sex, // 性别
                idcard: data.basic.idcard, // 证件号
                birthday: data.basic.birthday, // 出生年月
                contactTel: data.basic.contactTel, // 联系方式
                drugHistory: data.clinical.drugHistory, // 药物史
                familyContactType: data.basic.familyContactType, // 家庭联系方式 联系方式2
                bloodSampleType: data.inspection.bloodSampleType, // 血液样本类型
                bloodCollectTime: data.inspection.bloodCollectTime, // 血液样本采集时间
                humorSampleType: data.inspection.humorSampleType, // 体液样本类型
                humorCollectTime: data.inspection.humorCollectTime, // 体液样本采集时间
                tissueSampleType: data.inspection.tissueSampleType, // 组织样本类型
                tissueCollectTime: data.inspection.tissueCollectTime, // 组织样本采集时间
                cellSampleType: data.inspection.cellSampleType, // 细胞样本类型
                cellCollectTime: data.inspection.cellCollectTime, // 细胞样本采集时间
                nucleicAcidSampleType: data.inspection.nucleicAcidSampleType, // 核酸样本类型
                nucleicAcidCollectTime: data.inspection.nucleicAcidCollectTime, // 核酸样本采集时间
                operationHistory: data.clinical.operationHistory, // 手术史
                irrationalHistory: data.clinical.irrationalHistory, // 放疗史
                familyHistory: data.clinical.familyHistory // 家族史
              }
              if (this.form.photoContent.indexOf('其他') !== -1) {
                this.form.otherPhotoContent = this.form.photoContent.pop()
              }
              if (data.inspection.bloodTransType) {
                data.inspection.bloodTransType.split(',').forEach(v => {
                  if (v === '红细胞' || v === '血浆') {
                    this.form.bloodTransType.push(v)
                  } else {
                    otherBloodTransType.push(v)
                  }
                })
              }
              // 获取一样多检产品列表
              this.productList = []
              let productList = data.productList || []
              productList.forEach(v => {
                let item = {
                  productName: v.proName,
                  childProList: v.childProList || [],
                  childProName: '-',
                  sampleProductId: v.sampleProductId,
                  sampleType: v.sampleType,
                  isSample: true
                }
                if (item.childProList.length > 0) {
                  item.childProList.forEach((vv, index) => {
                    this.productList.push({
                      sampleProductId: vv.sampleProductId,
                      productName: item.productName,
                      childProList: index === 0 ? item.childProList : [],
                      childProName: vv.proName,
                      sampleType: vv.sampleType,
                      isSample: false
                    })
                  })
                } else {
                  this.productList.push(item)
                }
              })
              this.rules.idcard[0].required = this.form.cardType !== 0
              this.form.otherBloodTransType = otherBloodTransType.toString()
              this.sampleClinicalId = data.clinical.sampleClinicalId
              this.sampleBasicId = data.basic.sampleBasicId
              this.sampleInspectionId = data.inspection.sampleInspectionId
              this.imgNames = data.imgNames
              this.isFollowUps !== 3 ? this.followupImgNames = data.followupImgNames : this.followupImgNames = []
              this.sampleOrderImg = data.inspection.fileInfo || []
              this.supplementaryImgs = data.supplementaryImgs || []
              this.supplementaryNote = this.form.supplementaryNote || []
              this.imgSrc = this.sampleOrderImg.length === 0 ? '' : this.sampleOrderImg[0].fileAbsolutePath
              this.imgType = 'sampleOrderImg'
              // data.basic.province, data.basic.city, data.basic.region
              if (data.basic.province) {
                this.form.detailAddr.push(data.basic.province)
              }
              if (data.basic.city) {
                this.form.detailAddr.push(data.basic.city)
              }
              if (data.basic.region) {
                this.form.detailAddr.push(data.basic.region)
              }
            } catch (e) {
              console.log(e)
            }
          }
          this.getCancerInfo()
          this.getImmuneInfo()
          this.getTgrInfo()
          this.getGeneTestInfo()
          this.getIconographyInfo()
          this.getCancerTagInfo()
          this.getBiocheExamTagInfo()
          this.getSampleSangerList()
          this.getSexhormoneTagInfo()
          this.getOperateHistorys()
          this.getRadiotherapies()
          this.getUseDrugs()
          this.getSickHistories()
          this.getFamilyHistories()
          this.handleAutoGetData()
        } else {
          this.$message.error(result.message)
        }
      })
    },

    // 获取免疫组化信息
    getImmuneInfo () {
      this.$ajax({
        method: 'get',
        url: '/sample/clinical/get_immune_histos',
        data: {
          sampleBasicId: this.sampleBasicId
        },
        cancelToken: this.axiosSource.token
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.immuneHistos = []
          let item = {}
          result.data.forEach(v => {
            item = {
              immuneHistoId: v.immuneHistoId,
              sampleBasicId: v.sampleBasicId,
              detectTime: `${v.detectYear}${v.detectMonth ? '-' + v.detectMonth : ''}${v.detectDay ? '-' + v.detectDay : ''}`,
              detectYear: v.detectYear,
              detectMonth: v.detectMonth,
              detectDay: v.detectDay,
              focusPosition: v.focusPosition,
              erValue: v.erValue,
              erPercent: v.erPercent,
              prValue: v.prValue,
              prPercent: v.prPercent,
              hre2Value: v.hre2Value,
              fishResult: v.fishResult,
              testProject: v.testProject,
              fupdateTime: v.fupdateTime,
              projects: []
            }
            if (v.testProject) {
              let project = v.testProject.split(',')
              let result = v.prPercent.split(',')
              project.forEach((vv, ii) => {
                item.projects.push({
                  project: vv,
                  result: result[ii]
                })
              })
            }
            this.immuneHistos.push(item)
          })
        } else {
          this.$message.error(result.message)
        }
      })
    },

    // 获取基因检测信息
    getGeneTestInfo () {
      this.$ajax({
        url: '/sample/clinical/get_clinical_pres',
        method: 'get',
        data: {
          sampleBasicId: this.sampleBasicId
        },
        cancelToken: this.axiosSource.token
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.geneTest = []
          let item = {}
          result.data.forEach(v => {
            item = {
              preGeneDetectionId: v.preGeneDetectionId,
              sampleBasicId: v.sampleBasicId,
              detectYear: v.detectYear,
              detectMonth: v.detectMonth,
              detectDay: v.detectDay,
              detectTime: `${v.detectYear ? v.detectYear : ''}${v.detectMonth ? '-' + v.detectMonth : ''}${v.detectDay ? '-' + v.detectDay : ''}`,
              detectMethod: v.detectMethod,
              sourceClinicals: v.sourceClinicals,
              fupdateTime: v.fupdateTime,
              pregenes: v.pregenes,
              detail: []
            }
            let detectGene = v.pregenes[0].detectGene.split(',')
            let detectResult = v.pregenes[0].detectResult.split(',')
            detectGene.forEach((vv, ii) => {
              item.detail.push({
                gene: vv,
                result: detectResult[ii]
              })
            })
            this.geneTest.push(item)
          })
        } else {
          this.$message.error(result.message)
        }
      })
    },

    // 获取影像检查信息
    getIconographyInfo () {
      this.$ajax({
        url: '/sample/clinical/get_clinical_iconographies',
        method: 'get',
        data: {
          sampleBasicId: this.sampleBasicId
        },
        cancelToken: this.axiosSource.token
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.iconography = []
          let item = {}
          result.data.forEach(v => {
            item = {
              iconographyId: v.iconographyId,
              sampleBasicId: v.sampleBasicId,
              checkArea: v.checkArea,
              checkYear: v.checkYear,
              checkMonth: v.checkMonth,
              checkDay: v.checkDay,
              checkTime: `${v.checkYear}${v.checkMonth ? '-' + v.checkMonth : ''}${v.checkDay ? '-' + v.checkDay : ''}`,
              checkProject: v.checkProject,
              otherProject: v.otherProject,
              checkPostion: v.checkPostion,
              checkPostion1: v.checkPostion1,
              sourceImgs: v.sourceImgs,
              targetSpot: v.targetSpot,
              targetSpotLength: v.targetSpotLength,
              targetSpotDes: v.targetSpotDes,
              checkResult: v.checkResult,
              checkOtherResult: v.checkOtherResult,
              lesionPos: v.lesionPos,
              num: v.num,
              protrudingLesions: v.protrudingLesions,
              size: v.size,
              location: v.location,
              form: v.form,
              margin: v.margin,
              nodeLung: v.nodeLung,
              density: v.density,
              densityValue: v.densityValue,
              element: v.element
            }
            this.iconography.push(item)
          })
        } else {
          this.$message.error(result.message)
        }
      })
    },

    // 获取肿瘤标记物信息
    getCancerTagInfo () {
      this.$ajax({
        method: 'get',
        url: '/sample/clinical/get_clinical_cancer_tags',
        data: {
          sampleBasicId: this.sampleBasicId
        },
        cancelToken: this.axiosSource.token
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.cancerTag = []
          let item = {}
          result.data.forEach(v => {
            item = {
              judgeNum: v.judgeNum,
              checkTime: `${v.details[0].checkYear ? v.details[0].checkYear : ''}${v.details[0].checkMonth ? '-' + v.details[0].checkMonth : ''}${v.details[0].checkDay ? '-' + v.details[0].checkDay : ''}`,
              details: v.details
            }
            this.cancerTag.push(item)
          })
        } else {
          this.$message.error(result.message)
        }
      })
    },

    // 获取生化检查信息
    getBiocheExamTagInfo () {
      this.$ajax({
        method: 'get',
        url: '/sample/clinical/get_bioche_exam_list',
        data: {
          sampleBasicId: this.sampleBasicId
        },
        cancelToken: this.axiosSource.token
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.biocheExamTag = []
          let item = {}
          result.data.forEach(v => {
            item = {
              biocheExamId: v.biocheExamId,
              sampleBasicId: v.sampleBasicId,
              detectYear: v.detectYear,
              detectMonth: v.detectMonth,
              detectDay: v.detectDay,
              detectTime: `${v.detectYear ? v.detectYear : ''}${v.detectMonth ? '-' + v.detectMonth : ''}${v.detectDay ? '-' + v.detectDay : ''}`,
              detectMethod: v.detectMethod,
              biocheDtl: v.biocheDtl
            }
            this.biocheExamTag.push(item)
          })
        } else {
          this.$message.error(result.message)
        }
      })
    },

    // 获取Sanger验证位点列表
    getSampleSangerList () {
      this.$ajax({
        url: '/sample/sanger/get_sample_sanger_list',
        method: 'get',
        data: {
          sampleNum: this.form.sampleNum
        },
        cancelToken: this.axiosSource.token
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.sangerTableData = []
          let item = {}
          result.data.forEach(v => {
            item = {
              id: v.id,
              referName: v.referName,
              referSampleNum: v.referSampleNum,
              sangerName: v.sangerName,
              sangerSampleNum: v.sangerSampleNum,
              report: v.report,
              gene: v.gene,
              nucleotideMutation: v.nucleotideMutation,
              aminoAcidMutation: v.aminoAcidMutation,
              referenceSequence: v.referenceSequence,
              exon: v.exon,
              status: v.status,
              statusRatio: v.statusRatio,
              testResult: v.testResult,
              fileName: v.fileName,
              filePath: v.filePath,
              updateTime: v.updateTime
            }
            this.sangerTableData.push(item)
          })
        } else {
          this.$message.error(result.message)
        }
      })
    },

    // 获取性激素接口
    getSexhormoneTagInfo () {
      this.$ajax({
        method: 'get',
        url: '/sample/clinical/get_sexhormone_list',
        data: {
          sampleBasicId: this.sampleBasicId
        },
        cancelToken: this.axiosSource.token
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.sexhormoneTag = []
          let item = {}
          result.data.forEach(v => {
            item = {
              sexhormoneId: v.sexhormoneId,
              sampleBasicId: v.sampleBasicId,
              detectTime: `${v.detectYear ? v.detectYear : ''}${v.detectMonth ? '-' + v.detectMonth : ''}${v.detectDay ? '-' + v.detectDay : ''}`,
              detectYear: v.detectYear,
              detectMonth: v.detectMonth,
              detectDay: v.detectDay,
              detectMethod: v.detectMethod,
              sexhorDtl: v.sexhorDtl
            }
            this.sexhormoneTag.push(item)
          })
        } else {
          this.$message.error(result.message)
        }
      })
    },

    // 获取病原体感染信息
    getTgrInfo (type = null, addUnknownReason = false) {
      this.$ajax({
        url: '/sample/clinical/get_bytgr_list',
        data: {
          sampleBasicId: this.sampleBasicId,
          bytgrType: type
        },
        cancelToken: this.axiosSource.token
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          let item = {}
          switch (type) {
            case 1:
              this.HPV = {
                bytgrFlag: result.data.HPV.length > 0 ? result.data.HPV[0].bytgrFlag : 3,
                unknownReason: result.data.HPV.length > 0 ? result.data.HPV[0].unknownReason : '',
                data: []
              }
              result.data.HPV.forEach(v => {
                item = {
                  bytgrId: v.bytgrId,
                  sampleBasicId: v.sampleBasicId,
                  bytgrType: v.bytgrType,
                  bytgrFlag: v.bytgrFlag,
                  unknownReason: v.unknownReason,
                  detectTime: `${v.detectYear ? v.detectYear : ''}${v.detectMonth ? '-' + v.detectMonth : ''}${v.detectDay ? '-' + v.detectDay : ''}`,
                  detectYear: v.detectYear,
                  detectMonth: v.detectMonth,
                  detectDay: v.detectDay,
                  detectMethod: v.detectMethod,
                  resultDesc: v.resultDesc,
                  testResult: v.testResult,
                  bytgrDtl: v.bytgrDtl
                }
                // if (item.detectTime || item.detectMethod || item.resultDesc || item.testResult) {
                this.HPV.data.push(item)
                // }
              })
              if (this.HPV.bytgrFlag === 2 && addUnknownReason) {
                this.handleAddUnknownReason('HPV')
              }
              break
            case 2:
              this.HBV = {
                bytgrFlag: result.data.HBV.length > 0 ? result.data.HBV[0].bytgrFlag : 3,
                unknownReason: result.data.HBV.length > 0 ? result.data.HBV[0].unknownReason : '',
                data: []
              }
              result.data.HBV.forEach(v => {
                item = {
                  bytgrId: v.bytgrId,
                  sampleBasicId: v.sampleBasicId,
                  bytgrType: v.bytgrType,
                  bytgrFlag: v.bytgrFlag,
                  unknownReason: v.unknownReason,
                  detectTime: `${v.detectYear ? v.detectYear : ''}${v.detectMonth ? '-' + v.detectMonth : ''}${v.detectDay ? '-' + v.detectDay : ''}`,
                  detectYear: v.detectYear,
                  detectMonth: v.detectMonth,
                  detectDay: v.detectDay,
                  detectMethod: v.detectMethod,
                  resultDesc: v.resultDesc,
                  testResult: v.testResult,
                  bytgrDtl: v.bytgrDtl
                }
                // if (item.detectTime || item.detectMethod || item.testResult) {
                this.HBV.data.push(item)
                // }
              })
              if (this.HBV.bytgrFlag === 2 && addUnknownReason) {
                this.handleAddUnknownReason('HBV')
              }
              break
            case 3:
              this.HCV = {
                bytgrFlag: result.data.HCV.length > 0 ? result.data.HCV[0].bytgrFlag : 3,
                unknownReason: result.data.HCV.length > 0 ? result.data.HCV[0].unknownReason : '',
                data: []
              }
              result.data.HCV.forEach(v => {
                item = {
                  bytgrId: v.bytgrId,
                  sampleBasicId: v.sampleBasicId,
                  bytgrType: v.bytgrType,
                  bytgrFlag: v.bytgrFlag,
                  unknownReason: v.unknownReason,
                  detectTime: `${v.detectYear ? v.detectYear : ''}${v.detectMonth ? '-' + v.detectMonth : ''}${v.detectDay ? '-' + v.detectDay : ''}`,
                  detectYear: v.detectYear,
                  detectMonth: v.detectMonth,
                  detectDay: v.detectDay,
                  detectMethod: v.detectMethod,
                  resultDesc: v.resultDesc,
                  testResult: v.testResult,
                  bytgrDtl: v.bytgrDtl
                }
                // if (item.detectTime || item.detectMethod || item.testResult) {
                this.HCV.data.push(item)
                // }
              })
              if (this.HCV.bytgrFlag === 2 && addUnknownReason) {
                this.handleAddUnknownReason('HCV')
              }
              break
            case 4:
              this.EBV = {
                bytgrFlag: result.data.EBV.length > 0 ? result.data.EBV[0].bytgrFlag : 3,
                unknownReason: result.data.EBV.length > 0 ? result.data.EBV[0].unknownReason : '',
                data: []
              }
              result.data.EBV.forEach(v => {
                item = {
                  bytgrId: v.bytgrId,
                  sampleBasicId: v.sampleBasicId,
                  bytgrType: v.bytgrType,
                  bytgrFlag: v.bytgrFlag,
                  unknownReason: v.unknownReason,
                  detectTime: `${v.detectYear ? v.detectYear : ''}${v.detectMonth ? '-' + v.detectMonth : ''}${v.detectDay ? '-' + v.detectDay : ''}`,
                  detectYear: v.detectYear,
                  detectMonth: v.detectMonth,
                  detectDay: v.detectDay,
                  detectMethod: v.detectMethod,
                  resultDesc: v.resultDesc,
                  testResult: v.testResult,
                  bytgrDtl: v.bytgrDtl
                }
                // if (item.detectTime || item.detectMethod || item.testResult) {
                this.EBV.data.push(item)
                // }
              })
              if (this.EBV.bytgrFlag === 2 && addUnknownReason) {
                this.handleAddUnknownReason('EBV')
              }
              break
            case 5:
              this.HIV = {
                bytgrFlag: result.data.HIV.length > 0 ? result.data.HIV[0].bytgrFlag : 3,
                unknownReason: result.data.HIV.length > 0 ? result.data.HIV[0].unknownReason : '',
                data: []
              }
              result.data.HIV.forEach(v => {
                item = {
                  bytgrId: v.bytgrId,
                  sampleBasicId: v.sampleBasicId,
                  bytgrType: v.bytgrType,
                  bytgrFlag: v.bytgrFlag,
                  unknownReason: v.unknownReason,
                  detectTime: `${v.detectYear ? v.detectYear : ''}${v.detectMonth ? '-' + v.detectMonth : ''}${v.detectDay ? '-' + v.detectDay : ''}`,
                  detectYear: v.detectYear,
                  detectMonth: v.detectMonth,
                  detectDay: v.detectDay,
                  detectMethod: v.detectMethod,
                  resultDesc: v.resultDesc,
                  testResult: v.testResult,
                  bytgrDtl: v.bytgrDtl
                }
                this.HIV.data.push(item)
              })
              break
            case 6:
              this.HP = {
                bytgrFlag: result.data.HP.length > 0 ? result.data.HP[0].bytgrFlag : 3,
                unknownReason: result.data.HP.length > 0 ? result.data.HP[0].unknownReason : '',
                data: []
              }
              result.data.HP.forEach(v => {
                item = {
                  bytgrId: v.bytgrId,
                  sampleBasicId: v.sampleBasicId,
                  bytgrType: v.bytgrType,
                  bytgrFlag: v.bytgrFlag,
                  unknownReason: v.unknownReason,
                  detectTime: `${v.detectYear ? v.detectYear : ''}${v.detectMonth ? '-' + v.detectMonth : ''}${v.detectDay ? '-' + v.detectDay : ''}`,
                  detectYear: v.detectYear,
                  detectMonth: v.detectMonth,
                  detectDay: v.detectDay,
                  detectMethod: v.detectMethod,
                  resultDesc: v.resultDesc,
                  testResult: v.testResult,
                  bytgrDtl: v.bytgrDtl
                }
                this.HP.data.push(item)
              })
              break
            default:
              this.HPV = {
                bytgrFlag: result.data.HPV.length > 0 ? result.data.HPV[0].bytgrFlag : 3,
                unknownReason: result.data.HPV.length > 0 ? result.data.HPV[0].unknownReason : '',
                data: []
              }
              this.HP = {
                bytgrFlag: result.data.HP.length > 0 ? result.data.HP[0].bytgrFlag : 3,
                unknownReason: result.data.HP.length > 0 ? result.data.HP[0].unknownReason : '',
                data: []
              }
              this.HIV = {
                bytgrFlag: result.data.HIV.length > 0 ? result.data.HIV[0].bytgrFlag : 3,
                unknownReason: result.data.HIV.length > 0 ? result.data.HIV[0].unknownReason : '',
                data: []
              }
              this.HCV = {
                bytgrFlag: result.data.HCV.length > 0 ? result.data.HCV[0].bytgrFlag : 3,
                unknownReason: result.data.HCV.length > 0 ? result.data.HCV[0].unknownReason : '',
                data: []
              }
              this.EBV = {
                bytgrFlag: result.data.EBV.length > 0 ? result.data.EBV[0].bytgrFlag : 3,
                unknownReason: result.data.EBV.length > 0 ? result.data.EBV[0].unknownReason : '',
                data: []
              }
              this.HBV = {
                bytgrFlag: result.data.HBV.length > 0 ? result.data.HBV[0].bytgrFlag : 3,
                unknownReason: result.data.HBV.length > 0 ? result.data.HBV[0].unknownReason : '',
                data: []
              }
              for (let key in result.data) {
                result.data[key].forEach(v => {
                  item = {
                    bytgrId: v.bytgrId,
                    sampleBasicId: v.sampleBasicId,
                    bytgrType: v.bytgrType,
                    bytgrFlag: v.bytgrFlag,
                    unknownReason: v.unknownReason,
                    detectTime: `${v.detectYear ? v.detectYear : ''}${v.detectMonth ? '-' + v.detectMonth : ''}${v.detectDay ? '-' + v.detectDay : ''}`,
                    detectYear: v.detectYear,
                    detectMonth: v.detectMonth,
                    detectDay: v.detectDay,
                    detectMethod: v.detectMethod,
                    resultDesc: v.resultDesc,
                    testResult: v.testResult,
                    bytgrDtl: v.bytgrDtl
                  }
                  if (item.detectTime || item.detectMethod || item.resultDesc || item.testResult) {
                    this[key].data.push(item)
                  }
                })
              }
          }
        } else {
          this.$message.error(result.message)
        }
      })
    },

    // 获取手术史信息
    getOperateHistorys () {
      this.$ajax({
        method: 'get',
        url: '/sample/clinical/get_operate_historys',
        data: {
          sampleBasicId: this.sampleBasicId
        },
        cancelToken: this.axiosSource.token
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.operateHis = []
          let item = {}
          result.data.forEach(v => {
            item = {
              operateHistoryId: v.operateHistoryId,
              sampleBasicId: v.sampleBasicId,
              times: v.times,
              operateYear: v.operateYear,
              operateMonth: v.operateMonth,
              operateDay: v.operateDay,
              operateTime: `${v.operateYear}${v.operateMonth ? '-' + v.operateMonth : ''}${v.operateDay ? '-' + v.operateDay : ''}`,
              operateName: v.operateName,
              ptnmPt: v.ptnmPt,
              ptnmN: v.ptnmN,
              ptnmM: v.ptnmM,
              pathologicalType: v.pathologicalType,
              lymphTransfer: v.lymphTransfer,
              lymphContent: `${v.lymphNum1 ? v.lymphNum1 + ';' : ''}${v.lymphNum2 ? v.lymphNum2 + ';' : ''}${v.lymphRange ? v.lymphRange : ''}`,
              lymphNum1: v.lymphNum1,
              lymphNum2: v.lymphNum2,
              lymphRange: v.lymphRange,
              isFollowup: v.isFollowup,
              followupYear: v.followupYear,
              followupMonth: v.followupMonth,
              followupDay: v.followupDay,
              fupdateTime: v.fupdateTime
            }
            this.operateHis.push(item)
          })
        } else {
          this.$message.error(result.message)
        }
      })
    },

    // 获取放疗史信息
    getRadiotherapies () {
      this.$ajax({
        method: 'get',
        url: '/sample/clinical/get_radiotherapies',
        data: {
          sampleBasicId: this.sampleBasicId
        },
        cancelToken: this.axiosSource.token
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.radiotherapyTableData = []
          this.radiotherapyTableSelectRow.clear()
          let item = {}
          result.data.forEach(v => {
            item = {
              radiotherapyId: v.radiotherapyId,
              sampleBasicId: v.sampleBasicId,
              dosage: v.dosage,
              position: v.position,
              course: v.course,
              times: v.times,
              startYear: v.startYear,
              startMonth: v.startMonth,
              startDay: v.startDay,
              startTime: v.startYear && `${v.startYear}${v.startMonth ? '-' + v.startMonth : ''}${v.startDay ? '-' + v.startDay : ''}`,
              endYear: v.endYear,
              endMonth: v.endMonth,
              endDay: v.endDay,
              endTime: v.endYear && `${v.endYear}${v.endMonth ? '-' + v.endMonth : ''}${v.endDay ? '-' + v.endDay : ''}`,
              effect: v.effect,
              otherEffect: v.otherEffect,
              isFollowup: v.isFollowup,
              followupYear: v.followupYear,
              followupMonth: v.followupMonth,
              followupDay: v.followupDay,
              followupTime: `${v.followupYear}${v.followupMonth ? '-' + v.followupMonth : ''}${v.followupDay ? '-' + v.followupDay : ''}`,
              fupdateTime: v.fupdateTime
            }
            this.radiotherapyTableData.push(item)
          })
        } else {
          this.$message.error(result.message)
        }
      })
    },

    // 获取用药史信息
    getUseDrugs () {
      this.$ajax({
        url: '/sample/clinical/get_use_drugs',
        data: {
          sampleBasicId: this.sampleBasicId,
          type: null
        },
        cancelToken: this.axiosSource.token
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.drugHistoryTableData = []
          this.drugHistoryTableSelectRow.clear()
          let item = {}
          result.data.forEach(v => {
            item = {
              schemeType: v.schemeType,
              useDrugId: v.useDrugId,
              sampleBasicId: v.sampleBasicId,
              planName: v.planName,
              treatCourse: v.treatCourse,
              startYear: v.startYear,
              startMonth: v.startMonth,
              startDay: v.startDay,
              startTime: v.startYear && `${v.startYear}${v.startMonth ? '-' + v.startMonth : ''}${v.startDay ? '-' + v.startDay : ''}`,
              endYear: v.endYear,
              endMonth: v.endMonth,
              endDay: v.endDay,
              endTime: v.endYear && `${v.endYear}${v.endMonth ? '-' + v.endMonth : ''}${v.endDay ? '-' + v.endDay : ''}`,
              effect: v.effect,
              otherEffect: v.otherEffect,
              type: v.type,
              drugPlanType: v.drugPlanType,
              effectTime: v.effectTime || '',
              bestEffect: v.bestEffect,
              bestEffectTime: v.bestEffectTime || '',
              otherBestEffect: v.otherBestEffect,
              firstEffect: v.firstEffect,
              firstEffectTime: v.firstEffectTime || '',
              otherfirstEffect: v.otherfirstEffect,
              isFollowup: v.isFollowup,
              followupYear: v.followupYear,
              followupMonth: v.followupMonth,
              followupDay: v.followupDay,
              followupDayTime: `${v.followupYear}${v.followupMonth ? '-' + v.followupMonth : ''}${v.followupDay ? '-' + v.followupDay : ''}`,
              fupdateTime: v.fupdateTime
            }
            this.drugHistoryTableData.push(item)
          })
        } else {
          this.$message.error(result.message)
        }
      })
    },

    // 获取疾病史信息
    getSickHistories () {
      this.$ajax({
        method: 'get',
        url: '/sample/clinical/find_sick_histories',
        data: {
          sampleBasicId: this.sampleBasicId
        },
        cancelToken: this.axiosSource.token
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.sickHistory = []
          let item = {}
          result.data.forEach(v => {
            item = {
              sickDiseaseHisid: v.sickDiseaseHisid,
              sampleBasicId: this.sampleBasicId,
              illPlace: v.illPlace,
              sickDetail: v.sickDetail,
              illDetail: v.illDetail,
              illList: v.illDetail ? v.illDetail.split(',').map(v => Number(v)) : []
            }
            this.sickHistory.push(item)
          })
        } else {
          this.$message.error(result.message)
        }
      })
    },

    // 获取家族史信息
    getFamilyHistories () {
      this.$ajax({
        method: 'get',
        url: '/sample/clinical/get_family_histories',
        data: {
          sampleBasicId: this.sampleBasicId
        },
        cancelToken: this.axiosSource.token
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.familyHistory = []
          let item = {}
          result.data.forEach(v => {
            item = {
              familyHistoryId: v.familyHistoryId,
              sampleBasicId: v.sampleBasicId,
              relationship: v.relationship,
              age: v.age,
              cancer: v.cancer,
              cancerName: v.cancerName,
              cancerOther: v.cancerOther,
              relativesSampleNum: v.relativesSampleNum
            }
            this.familyHistory.push(item)
          })
        } else {
          this.$message.error(result.message)
        }
      })
    },

    // 自动获取功能
    handleAutoGetData () {
      let familyChecked = null
      let operateHisChecked = null
      let otherapyChecked = null
      let drugHistoryChecked = null
      if (this.form.familyHistory !== null) {
        familyChecked = this.form.familyHistory.toString()
      }
      if (this.form.operateHis !== null) {
        operateHisChecked = this.form.operateHis.toString()
      }
      if (this.form.otherapy !== null) {
        otherapyChecked = this.form.otherapy.toString()
      }
      if (this.form.drugHistory !== null) {
        drugHistoryChecked = this.form.drugHistory.toString()
      }
      let jsonParam = {
        familyChecked: familyChecked,
        operateHisChecked: operateHisChecked,
        otherapyChecked: otherapyChecked,
        drugHistoryChecked: drugHistoryChecked
      }
      this.$ajax({
        url: '/sample/clinical/get_clinical_detial_info',
        data: {
          sampleBasicId: this.sampleBasicId,
          jsonParam: JSON.stringify(jsonParam)
        },
        cancelToken: this.axiosSource.token
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          let data = result.data
          this.form.clinicalDiagnose = data.clinicalDiagnose
          this.form.treatmentHistory = ''
          this.$set(this.form, 'operationHistory', data.operationHistory.split('\r\n').join('<br/>'))
          this.$set(this.form, 'medicationHistory', data.medicationHistory.split('\r\n').join('<br/>'))
          this.$set(this.form, 'irrationalHistory', data.irrationalHistory.split('\r\n').join('<br/>'))
          // 基本信息不填充
          // this.form.name = data.name
          // this.form.sampleNum = data.sampleNum
          // this.form.inspectionUnitName = data.inspectionUnitName
          // this.form.sex = data.sex
          // this.form.sampleType = data.sampleType
          // this.form.doctor = data.doctor
          // this.form.birthday = data.birthday
          // this.form.productNames = data.productNames
          // this.form.projectName = data.projectName
          // this.form.productName = data.productNames || data.projectName
          // this.form.idcard = data.idcard
          // this.form.telephone = data.telephone
        } else {
          this.$message.error(result.message)
        }
      })
    },

    // 获取癌种列表
    getCancerList () {
      this.$ajax({
        url: '/cancertype/list_cancers_by_cancer_class',
        data: {
          cancerClass: 0
        },
        cancelToken: this.axiosSource.token
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.cancerList = []
          result.data.forEach(v => {
            this.cancerList.push({
              value: v.cancerTypeId,
              label: v.cancerTypeName
            })
          })
        } else {
          this.$message.error(result.message)
        }
      })
    },

    // 获取样例癌种信息+病理列表
    getCancerInfo () {
      this.$ajax({
        url: '/sample/clinical/get_all_cancer_pathology_info',
        method: 'get',
        data: {
          sampleBasicId: this.sampleBasicId
        },
        cancelToken: this.axiosSource.token
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.cancerInfoList = []
          let item = {}
          let clinicalPathologyItem = {}
          result.data.forEach(v => {
            if (!this.defaultData.cancerInfo) {
              this.defaultData.cancerInfo = []
              this.defaultData.cancerInfo.push({
                diagnosCancer: v.diagnosCancer,
                cancerMetastasisSites: v.cancerMetastasisSites ? v.cancerMetastasisSites.split(',') : []
              })
            }
            item = {
              sampleClinicalCancerInfoId: v.sampleClinicalCancerInfoId,
              sampleBasicId: v.sampleBasicId,
              firstcancerNameId: v.firstcancerNameId,
              secondcancerNameId: v.secondcancerNameId,
              thirdcancerNameId: v.thirdcancerNameId,
              fourthcancerNameId: v.fourthcancerNameId,
              fivecancerNameId: v.fivecancerNameId,
              sixcancerNameId: v.sixcancerNameId,
              cancerTypeAllName: v.cancerTypeAllName,
              popularCancerTypeId: v.popularCancerTypeId,
              popularCancerType: v.popularCancerType,
              recrudescenceTime: v.recrudescenceTime,
              diagnosCancer: v.diagnosCancer,
              pathologicDiagnosis: v.fpathologicDiagnosis,
              diagnosAge: v.diagnosAge,
              diagnosTime: v.diagnosTime,
              cancerTypeInfo: v.cancerTypeInfo,
              otherCancer: v.otherCancer,
              pathology: v.pathology,
              clinicalStages: v.clinicalStages,
              clinicalStages1: v.clinicalStages ? v.clinicalStages.split(' ')[0] : '',
              clinicalStages2: v.clinicalStages ? v.clinicalStages.split(' ')[1] : '',
              tnmTstatges: v.tnmTstatges,
              tnmNstatges: v.tnmNstatges,
              tnmMstatges: v.tnmMstatges,
              cancerMetastasisSites: v.cancerMetastasisSites ? v.cancerMetastasisSites.split(',') : [],
              otherCancerMetastasisSites: '',
              fupdateTime: v.fupdateTime,
              clinicalPathologyList: []
            }
            if (item.cancerMetastasisSites.indexOf('其他') !== -1) {
              item.otherCancerMetastasisSites = item.cancerMetastasisSites.pop()
            }
            v.clinicalPathologyList.forEach(vv => {
              clinicalPathologyItem = {
                clinicalPathologyId: vv.clinicalPathologyId,
                sampleBasicId: vv.sampleBasicId,
                checkTime: `${vv.checkYear ? vv.checkYear : ''}${vv.checkMonth ? '-' + vv.checkMonth : ''}${vv.checkDay ? vv.checkDay : ''}`,
                checkYear: vv.checkYear,
                checkMonth: vv.checkMonth,
                checkDay: vv.checkDay,
                checkPosition: vv.checkPosition,
                checkResult: vv.checkResult,
                sourceImgs: vv.sourceImgs,
                lymphTransfer: vv.lymphTransfer,
                lymphNum1: vv.lymphNum1,
                lymphNum2: vv.lymphNum2,
                lymphRange: vv.lymphRange,
                breastCancerMoleculeSubtype: vv.breastCancerMoleculeSubtype,
                sampleClinicalCancerInfoId: vv.sampleClinicalCancerInfoId,
                fupdateTime: vv.fupdateTime
              }
              item.clinicalPathologyList.push(clinicalPathologyItem)
            })
            this.cancerInfoList.push(item)
          })
        } else {
          this.$message.error(result.message)
        }
      })
    },

    // 编辑确诊癌种信息
    handleCancerEdit (item = null) {
      this.diagnosedCancerSaveDialogData = item
        ? JSON.parse(JSON.stringify({...item,
          birthday: this.form.birthday
            ? this.form.birthday : ''}))
        : {sampleBasicId: this.sampleBasicId,
          birthday: this.form.birthday
            ? this.form.birthday : ''}
      this.diagnosedCancerSaveDialogVisible = true
    },

    // 删除确诊癌种
    handleCancerDelete (item) {
      this.$ajax({
        url: '/sample/clinical/del_clinical_cancer_info',
        method: 'get',
        data: {
          sampleClinicalCancerInfoId: item.sampleClinicalCancerInfoId
        },
        cancelToken: this.axiosSource.token
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.$message.success('删除成功')
          this.getCancerInfo()
          this.handleAutoGetData()
        } else {
          this.$message.error(result.message)
        }
      })
    },

    // 保存确诊癌种信息后重新获取癌种信息
    handleDiagnosedCancerSaveDialogConfirm () {
      this.handleDialogInfoFix()
      this.diagnosedCancerSaveDialogVisible = false
      this.getCancerInfo()
      this.handleAutoGetData()
    },

    // 编辑病理信息
    handleClinicalPathologyEdit (item = null, sampleClinicalCancerInfoId = null) {
      this.clinicalPathologySaveDialogData = item ? JSON.parse(JSON.stringify(item)) : {sampleBasicId: this.sampleBasicId, sampleClinicalCancerInfoId: sampleClinicalCancerInfoId}
      this.clinicalPathologySaveDialogVisible = true
    },

    // 删除病理信息
    handleClinicalPathologyDelete (item) {
      this.$ajax({
        url: '/sample/clinical/delete_clinical_pathology',
        method: 'get',
        data: {
          clinicalPathologyId: item.clinicalPathologyId
        },
        cancelToken: this.axiosSource.token
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.$message.success('删除成功')
          this.getCancerInfo()
        } else {
          this.$message.error(result.message)
        }
      })
    },
    // 修改存疑弹窗是否修改信息
    handleDialogInfoFix () {
      if (this.type === 3) {
        this.isFix = true
      }
    },

    // 保存病理信息后重新获取病理信息
    handleClinicalPathologySaveDialogConfirm () {
      this.handleDialogInfoFix()
      this.clinicalPathologySaveDialogVisible = false
      this.getCancerInfo()
    },
    // 保存病原体感染后重新获取病理信息
    handleTgrSaveDialogConfirm (type) {
      this.handleDialogInfoFix()
      this.tgrSaveDialogVisible = false
      let typeNum = null
      switch (type) {
        case 'HPV':
          typeNum = 1
          break
        case 'HBV':
          typeNum = 2
          break
        case 'HCV':
          typeNum = 3
          break
        case 'EBV':
          typeNum = 4
          break
      }
      this.getTgrInfo(typeNum)
    },

    // 保存治疗情况后重新获取病理信息
    handleTreatmentSituationSaveDialogConfirm (type) {
      this.handleDialogInfoFix()
      this.treatmentSituationSaveDialogVisible = false
      switch (type) {
        case 'operateHis':
          this.getOperateHistorys()
          break
        case 'otherapy':
          this.getRadiotherapies()
          break
        case 'drugHistory':
          this.getUseDrugs()
          break
      }
      this.handleAutoGetData()
    },

    // 编辑免检测情况信息（病原感染除外）
    handleDetectInfoEdit (item = null, type) {
      if (type === 'iconography' && item) {
        let realdata = JSON.parse(JSON.stringify(item))
        this.detectSaveDialogData = {...realdata}
        if (realdata.targetSpot === 2) {
          this.detectSaveDialogData.lesionPos.push({
            iconographyId: realdata.iconographyId,
            sampleBasicId: realdata.sampleBasicId,
            targetSpot: 2,
            checkArea: realdata.checkArea,
            checkYear: realdata.checkYear,
            checkMonth: realdata.checkMonth,
            checkDay: realdata.checkDay,
            checkTime: realdata.checkTime,
            checkProject: realdata.checkProject,
            checkResult: realdata.checkResult,
            otherProject: realdata.otherProject,
            checkOtherResult: realdata.checkOtherResult,
            diseaseTail: realdata.diseaseTail,
            num: realdata.num,
            protrudingLesions: realdata.protrudingLesions,
            size: realdata.size,
            location: realdata.location,
            form: realdata.form,
            margin: realdata.margin,
            nodeLung: realdata.nodeLung,
            density: realdata.density,
            densityValue: realdata.densityValue,
            element: realdata.element
          })
        }
      } else {
        this.detectSaveDialogData = item ? {sampleBasicId: this.sampleBasicId, ...JSON.parse(JSON.stringify(item))} : {sampleBasicId: this.sampleBasicId}
      }
      this.detectSaveDialogData.type = type
      this.detectSaveDialogVisible = true
    },

    // 完成修改检测情况信息后
    handleDetectSaveDialogConfirm (type) {
      this.handleDialogInfoFix()
      this.detectSaveDialogVisible = false
      switch (type) {
        case 'immuneHistos':
          this.getImmuneInfo()
          this.handleAutoGetData()
          break
        case 'geneTest':
          this.getGeneTestInfo()
          this.handleAutoGetData()
          break
        case 'iconography':
          this.getIconographyInfo()
          break
        case 'cancerTag':
          this.getCancerTagInfo()
          break
        case 'biocheExamTag':
          this.getBiocheExamTagInfo()
          break
        case 'sexhormoneTag':
          this.getSexhormoneTagInfo()
          break
      }
    },

    // 删除检测情况信息
    handleDetectDelete (item, type) {
      let url = ''
      let data = {}
      switch (type) {
        case 'immuneHistos':
          url = '/sample/clinical/delete_immune_histo'
          data = {
            immuneHistoId: item.immuneHistoId
          }
          break
        case 'geneTest':
          url = '/sample/clinical/delete_clinical_predect'
          data = {
            predectId: item.preGeneDetectionId
          }
          break
        case 'iconography':
          url = '/sample/clinical/delete_icongraphy'
          data = {
            iconographyId: item.iconographyId
          }
          break
        case 'cancerTag':
          url = '/sample/clinical/delete_cancer_tag'
          data = {
            cancerTagId: item.details[0].cancerTagId
          }
          break
        case 'biocheExamTag':
          url = '/sample/clinical/delete_bioche_exam_by_id'
          data = {
            biocheExamId: item.biocheExamId
          }
          break
        case 'sexhormoneTag':
          url = '/sample/clinical/delete_sexhormone_by_id'
          data = {
            sexhormoneId: item.sexhormoneId
          }
          break
      }
      this.$ajax({
        method: 'get',
        url: url,
        data: data,
        cancelToken: this.axiosSource.token
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.$message.success('删除成功')
          switch (type) {
            case 'immuneHistos':
              this.getImmuneInfo()
              this.handleAutoGetData()
              break
            case 'geneTest':
              this.getGeneTestInfo()
              this.handleAutoGetData()
              break
            case 'iconography':
              this.getIconographyInfo()
              break
            case 'cancerTag':
              this.getCancerTagInfo()
              break
            case 'biocheExamTag':
              this.getBiocheExamTagInfo()
              break
            case 'sexhormoneTag':
              this.getSexhormoneTagInfo()
              break
          }
        } else {
          this.$message.error(result.message)
        }
      })
    },

    // 新增/编辑病原体感染信息
    handleTgrEdit (item = null, type) {
      this.tgrSaveDialogData = item ? JSON.parse(JSON.stringify(item)) : {sampleBasicId: this.sampleBasicId, detectTime: ''}
      this.tgrSaveDialogData.type = type
      this.tgrSaveDialogVisible = true
    },

    // 删除病原体感染信息
    handleTgrDelete (item, type) {
      let typeNum = null
      switch (type) {
        case 'HPV':
          typeNum = 1
          break
        case 'HBV':
          typeNum = 2
          break
        case 'HCV':
          typeNum = 3
          break
        case 'EBV':
          typeNum = 4
          break
        case 'HIV':
          typeNum = 5
          break
        case 'HP':
          typeNum = 6
          break
      }
      this.$ajax({
        method: 'get',
        url: '/sample/clinical/delete_bytgr_by_id',
        data: {
          bytgrId: item.bytgrId
        },
        cancelToken: this.axiosSource.token
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.$message.success('删除成功')
          this.getTgrInfo(typeNum)
        } else {
          this.$message.error(result.message)
        }
      })
    },

    // 新增/编辑治疗情况信息
    handleTreatmentSituationEdit (item = null, type) {
      if (type === 'otherapy' || type === 'drugHistory') {
        if (item === null) {
          this.treatmentSituationSaveDialogData = {
            sampleBasicId: this.sampleBasicId
          }
          this.treatmentSituationSaveDialogData.type = type
          this.treatmentSituationSaveDialogVisible = true
        } else {
          if (item.size === 1) {
            let data = [...item.values()][0]
            if (type === 'drugHistory') {
              data.planName = data.planName.split(',').map(v => { return {value: v} })
            }
            this.treatmentSituationSaveDialogData = JSON.parse(JSON.stringify(data))
            this.treatmentSituationSaveDialogData.type = type
            this.treatmentSituationSaveDialogVisible = true
          } else {
            this.$message.error('请选择一条数据')
          }
        }
      } else {
        this.treatmentSituationSaveDialogData = item ? JSON.parse(JSON.stringify(item)) : {sampleBasicId: this.sampleBasicId}
        this.treatmentSituationSaveDialogData.type = type
        this.treatmentSituationSaveDialogVisible = true
      }
    },

    // 保存疾病史信息后
    handleSickHistorySaveDialogConfirm () {
      this.handleDialogInfoFix()
      this.sickHistorySaveDialogVisible = false
      this.getSickHistories()
    },

    // 保存疾病史信息后更新数据
    handleTreatmentSituationDelete (item, type) {
      if (type === 'operateHis') {
        this.$ajax({
          method: 'get',
          url: '/sample/clinical/delete_operate_his',
          data: {
            operateHistoryId: item.operateHistoryId
          },
          cancelToken: this.axiosSource.token
        }).then(result => {
          if (result.code === this.SUCCESS_CODE) {
            this.$message.success('删除成功')
            this.getOperateHistorys()
            this.handleAutoGetData()
          } else {
            this.$message.error(result.message)
          }
        })
      } else {
        if (item.size !== 1) {
          this.$message.error('请选择一条数据')
        } else {
          this.$confirm('是否要删除该记录?', '删除确认', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            let row = [...item.values()][0]
            let url = ''
            let data = {}
            switch (type) {
              case 'otherapy':
                url = '/sample/clinical/delete_radiotherapy'
                data = {
                  radiotherapyId: row.radiotherapyId
                }
                break
              case 'drugHistory':
                url = '/sample/clinical/delete_use_drug'
                data = {
                  useDrugId: row.useDrugId
                }
                break
            }
            this.$ajax({
              method: 'get',
              url: url,
              data: data,
              cancelToken: this.axiosSource.token
            }).then(result => {
              if (result.code === this.SUCCESS_CODE) {
                this.$message.success('删除成功')
                switch (type) {
                  case 'operateHis':
                    this.getOperateHistorys()
                    break
                  case 'otherapy':
                    this.getRadiotherapies()
                    break
                  case 'drugHistory':
                    this.getUseDrugs()
                    break
                }
                this.handleAutoGetData()
              } else {
                this.$message.error(result.message)
              }
            })
          }).catch(() => {})
        }
      }
    },

    // 新建/编辑疾病史情况信息
    handleSickHistoryEdit (item = null) {
      this.sickHistorySaveDialogData = item ? JSON.parse(JSON.stringify(item)) : {
        sickDiseaseHisid: null,
        sampleBasicId: this.sampleBasicId,
        illPlace: '',
        sickDetail: '',
        illDetail: '',
        illList: []
      }
      this.sickHistorySaveDialogVisible = true
    },

    // 删除疾病史情况信息
    handleSickHistoryDelete (item) {
      this.$ajax({
        method: 'get',
        url: '/sample/clinical/delete_sick_history_by_id',
        data: {
          sickDiseaseHisid: item.sickDiseaseHisid
        },
        cancelToken: this.axiosSource.token
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.$message.success('删除成功')
          this.getSickHistories()
          this.handleAutoGetData()
        } else {
          this.$message.error(result.message)
        }
      })
    },

    // 新建/编辑家族史情况信息
    handleFamilyHistoryEdit (item = null) {
      if (item) {
        let caner = []
        if (item.cancer) {
          caner = (item.cancer.split(',').map(v => Number(v))).concat(item.cancerOther.split(','))
        } else {
          caner = item.cancerOther.split(',')
        }
        this.familyHistorySaveDialogData = {
          familyHistoryId: item.familyHistoryId,
          sampleBasicId: this.sampleBasicId,
          relationship: item.relationship,
          age: item.age,
          cancer: caner,
          cancerName: item.cancerName.split(','),
          cancerOther: item.cancerOther,
          relativesSampleNum: item.relativesSampleNum
        }
      } else {
        this.familyHistorySaveDialogData = {
          familyHistoryId: null,
          sampleBasicId: this.sampleBasicId,
          relationship: '',
          age: '',
          cancer: [],
          cancerName: [],
          cancerOther: '',
          relativesSampleNum: ''
        }
      }
      this.familyHistorySaveDialogVisible = true
    },

    // 保存家族史信息后更新数据
    handleFamilyHistorySaveDialogConfirm () {
      this.handleDialogInfoFix()
      this.familyHistorySaveDialogVisible = false
      this.getFamilyHistories()
      this.handleAutoGetData()
    },

    // 删除家族史情况信息
    handleFamilyHistoryDelete (item) {
      this.$ajax({
        method: 'get',
        url: '/sample/clinical/delete_family_history',
        data: {
          familyHistoryId: item.familyHistoryId
        },
        cancelToken: this.axiosSource.token
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.$message.success('删除成功')
          this.getFamilyHistories()
          this.handleAutoGetData()
        } else {
          this.$message.error(result.message)
        }
      })
    },

    // 查询Sanger位点
    handleSearchSanger () {
      this.$ajax({
        url: '/sample/sanger/get_refer_sample_info_by_sanger_sample_num',
        method: 'get',
        data: {
          sampleNum: this.sangerForm.sampleNum
        },
        cancelToken: this.axiosSource.token
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          let data = result.data
          if (data && data.length !== 0) {
            this.sangerSaveDialogData = {
              sangerSampleNum: this.form.sampleNum, // sanger检测样例编号
              referSampleNum: this.sangerForm.sampleNum, // 先证者样例编号
              tableData: data,
              type: 0
            }
            this.sangerSaveDialogVisible = true
          } else {
            this.$message.error('样例号无法识别')
          }
        } else {
          this.$message.error(result.message)
        }
      })
    },

    // 提交Sanger数据处理
    handleSangerSaveDialogConfirm () {
      this.handleDialogInfoFix()
      this.sangerSaveDialogVisible = false
      this.getSampleSangerList()
    },

    // 重置Sanger位点
    handleReset () {
      this.sangerForm.sampleNum = ''
      this.getSampleSangerList()
    },

    // 改变sanger验证位点状态
    handleChangeReport (row) {
      this.$ajax({
        url: '/sample/sanger/update_sanger_sample',
        data: {
          id: row.id,
          report: row.report === 'Y' ? 'N' : 'Y'
        },
        cancelToken: this.axiosSource.token
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          this.$message.success('状态更新成功')
          this.getSampleSangerList()
        } else {
          this.$message.error(result.message)
        }
      })
    },

    // sanger复制位点
    handleCopy () {
      this.sangerSaveDialogData = {
        sampleNum: this.form.sampleNum, // sanger检测样例编号
        sangerSampleNum: '', // 选择复制的样例编号
        type: 2
      }
      this.sangerSaveDialogVisible = true
    },

    // 新增sanger位点
    handleAddSanger () {
      this.sangerSaveDialogData = {
        sampleNum: this.form.sampleNum, // sanger检测样例编号
        sampleSangers: [
          {
            gene: '',
            nucleotideMutation: '',
            aminoAcidMutation: '',
            referenceSequence: '',
            exon: '',
            chr: '',
            start: '',
            end: '',
            ref: '',
            alt: '',
            status: '',
            statusRatio: ''
          }
        ],
        type: 1
      }
      this.sangerSaveDialogVisible = true
    },

    // 删除sanger位点
    handleDeleteSanger () {
      if (this.sangerTableSelectRows.size > 0) {
        let data = [...this.sangerTableSelectRows.values()]
        let ids = data.map(v => v.id)
        this.$ajax({
          method: 'get',
          url: '/sample/sanger/multi_delete',
          data: {
            ids: ids.join(',')
          },
          cancelToken: this.axiosSource.token
        }).then(result => {
          if (result.code === this.SUCCESS_CODE) {
            this.$message.success('保存成功')
            this.getSampleSangerList()
          } else {
            this.$message.error(result.message)
          }
        })
      } else {
        this.$message.error('请选择一条数据')
      }
    },

    // 切换上一张图片
    handlePreviousPicture () {
      this.deg = 0
      this.multiples = 100
      let index = -1
      this.handleImageReset()
      switch (this.imgType) {
        case 'imgNames':
          index = this.imgNames.findIndex(v => this.imgSrc === v.fileAbsolutePath)
          if (index !== -1) {
            if (index === 0) {
              this.imgSrc = this.imgNames[this.imgNames.length - 1].fileAbsolutePath
            } else {
              this.imgSrc = this.imgNames[index - 1].fileAbsolutePath
            }
          }
          break
        case 'followupImgNames':
          index = this.followupImgNames.findIndex(v => this.imgSrc === v.fileAbsolutePath)
          if (index !== -1) {
            if (index === 0) {
              this.imgSrc = this.followupImgNames[this.followupImgNames.length - 1].fileAbsolutePath
            } else {
              this.imgSrc = this.followupImgNames[index - 1].fileAbsolutePath
            }
          }
          break
        case 'sampleOrderImg':
          index = this.sampleOrderImg.findIndex(v => this.imgSrc === v.fileAbsolutePath)
          if (index !== -1) {
            if (index === 0) {
              this.imgSrc = this.sampleOrderImg[this.sampleOrderImg.length - 1].fileAbsolutePath
            } else {
              this.imgSrc = this.sampleOrderImg[index - 1].fileAbsolutePath
            }
          }
          break
        case 'supplementaryImgs':
          index = this.supplementaryImgs.findIndex(v => this.imgSrc === v.fileAbsolutePath)
          if (index !== -1) {
            if (index === 0) {
              this.imgSrc = this.supplementaryImgs[this.supplementaryImgs.length - 1].fileAbsolutePath
            } else {
              this.imgSrc = this.supplementaryImgs[index - 1].fileAbsolutePath
            }
          }
          break
      }
    },

    // 切换下一张图片
    handleNextPicture () {
      this.deg = 0
      this.multiples = 100
      let index = -1
      this.handleImageReset()
      switch (this.imgType) {
        case 'imgNames':
          index = this.imgNames.findIndex(v => this.imgSrc === v.fileAbsolutePath)
          if (index !== -1) {
            if (index === this.imgNames.length - 1) {
              this.imgSrc = this.imgNames[0].fileAbsolutePath
            } else {
              this.imgSrc = this.imgNames[index + 1].fileAbsolutePath
            }
          }
          break
        case 'followupImgNames':
          index = this.followupImgNames.findIndex(v => this.imgSrc === v.fileAbsolutePath)
          if (index !== -1) {
            if (index === this.followupImgNames.length - 1) {
              this.imgSrc = this.followupImgNames[0].fileAbsolutePath
            } else {
              this.imgSrc = this.followupImgNames[index + 1].fileAbsolutePath
            }
          }
          break
        case 'sampleOrderImg':
          index = this.sampleOrderImg.findIndex(v => this.imgSrc === v.fileAbsolutePath)
          if (index !== -1) {
            if (index === this.sampleOrderImg.length - 1) {
              this.imgSrc = this.sampleOrderImg[0].fileAbsolutePath
            } else {
              this.imgSrc = this.sampleOrderImg[index + 1].fileAbsolutePath
            }
          }
          break
        case 'supplementaryImgs':
          index = this.supplementaryImgs.findIndex(v => this.imgSrc === v.fileAbsolutePath)
          if (index !== -1) {
            if (index === this.supplementaryImgs.length - 1) {
              this.imgSrc = this.supplementaryImgs[0].fileAbsolutePath
            } else {
              this.imgSrc = this.supplementaryImgs[index + 1].fileAbsolutePath
            }
          }
          break
      }
    },

    // 编辑图片信息
    handleEditPicture () {
      this.pictureInfoSaveDialogData = {
        type: this.imgType,
        sampleBasicId: this.sampleBasicId,
        tableData: this.imgType === 'sampleOrderImg' ? JSON.parse(JSON.stringify(this.sampleOrderImg)) : [] // 送检单与知情同意书专用
      }
      this.pictureInfoSaveDialogVisible = true
    },

    // 编辑完成图片信息后函数
    handlePictureInfoSaveDialogConfirm (type, data = []) {
      this.handleDialogInfoFix()
      this.pictureInfoSaveDialogVisible = false
      // if (data) {
      //   this.sampleOrderImg = data
      //   this.imgSrc = this.sampleOrderImg.length > 0 ? this.sampleOrderImg[0].fileAbsolutePath : ''
      // } else {
      //   this.getBasicInfoData()
      // }
      this[type] = data
      this.imgSrc = this[type].length > 0 ? this[type][0].fileAbsolutePath : ''
    },

    // 单选改变后自动触发清空功能
    handleRadioChange (value, type) {
      if (value === 0 || value === 2) {
        let url = ''
        let data = {
          sampleBasicId: this.sampleBasicId
        }
        switch (type) {
          case 'immune':
            url = '/sample/clinical/delete_all_immune_histo'
            break
          case 'geneTest':
            url = '/sample/clinical/delete_all_clinical_predect'
            break
          case 'iconography':
            url = '/sample/clinical/delete_all_iconography'
            break
          case 'cancerTag':
            url = '/sample/clinical/delete_all_cancer_tag'
            break
          case 'biocheExamTag':
            url = '/sample/clinical/delete_bioche_Exam'
            break
          case 'sexhormoneTag':
            url = '/sample/clinical/delete_all_sexhormone'
            break
          case 'operateHis':
            url = '/sample/clinical/delete_all_operate_his'
            break
          case 'otherapy':
            url = '/sample/clinical/delete_all_otherapy'
            break
          case 'drugHistory':
            url = '/sample/clinical/delete_all_drug_history'
            break
          case 'sickHistory':
            url = '/sample/clinical/delete_all_sick_history'
            break
          case 'familyHistory':
            url = '/sample/clinical/delete_all_family_history'
            break
        }
        this.$ajax({
          method: 'get',
          url: url,
          data: data,
          cancelToken: this.axiosSource.token
        }).then(result => {
          if (result.code === this.SUCCESS_CODE) {
            switch (type) {
              case 'immune':
                this.getImmuneInfo()
                this.handleAutoGetData()
                break
              case 'geneTest':
                this.getGeneTestInfo()
                this.handleAutoGetData()
                break
              case 'iconography':
                this.getIconographyInfo()
                break
              case 'cancerTag':
                this.getCancerTagInfo()
                break
              case 'biocheExamTag':
                this.getBiocheExamTagInfo()
                break
              case 'sexhormoneTag':
                this.getSexhormoneTagInfo()
                break
              case 'operateHis':
                this.getOperateHistorys()
                this.handleAutoGetData()
                break
              case 'otherapy':
                this.getRadiotherapies()
                this.handleAutoGetData()
                break
              case 'drugHistory':
                this.getUseDrugs()
                this.handleAutoGetData()
                break
              case 'sickHistory':
                this.getSickHistories()
                break
              case 'familyHistory':
                this.getFamilyHistories()
                this.handleAutoGetData()
                break
            }
          } else {
            this.$message.error(result.message)
          }
        })
      }
    },

    // 审核驳回
    handleReject () {
      this.$prompt('请输入驳回备注', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValidator: function (value) {
          if (value) {
            if (value.length > 50) {
              return '驳回备注50字以内'
            }
          } else {
            return '请输入驳回备注'
          }
        }
      }).then(({ value }) => {
        this.handleRejectConfirm(value)
      }).catch()
    },
    handleRejectConfirm (rejectRemark) {
      this.$ajax({
        url: '/sample/basic/audit_sample_info',
        data: {
          sampleBasicId: this.sampleBasicId,
          clinicalMkSta: 2,
          makeupStatus: 2,
          supplementaryInformationTotal: this.supplementaryNote.length,
          rejectRemark: rejectRemark
        },
        cancelToken: this.axiosSource.token
      }).then(result => {
        if (result.code === this.SUCCESS_CODE) {
          if (!this.handleChecked()) return
          this.$message.success('操作成功！')
          this.$emit('saveInfoDialogConfirmEvent')
        } else {
          this.$message.error(result.message)
        }
      })
    },

    handleSampleTypeChange (value) {
      if (value.indexOf('其他') === -1) {
        this.form.chiefComplaint = ''
      }
    },
    handleIdCardBlur () {
      let pass = util.identityNumberValid(this.form.idcard).pass
      if (pass) {
        let year = this.form.idcard.substring(6, 10)
        let month = this.form.idcard.substring(10, 12)
        let day = this.form.idcard.substring(12, 14)
        let sexType = Number(this.form.idcard.substring(16, 17))
        let sex = 0
        if (sexType % 2 === 0) {
          sex = 1
        }
        this.$set(this.form, 'birthday', `${year}-${month}-${day}`)
        this.$set(this.form, 'age', util.getAge(`${year}-${month}-${day}`))
        this.$set(this.form, 'sex', sex)
      }
    },
    handleBirthdayChange () {
      this.$set(this.form, 'age', util.getAge(this.form.birthday))
    },
    // 这些废弃
    handleRadioClick (bytgrFlag, type) {
      // console.log(type)
      // let url = '/sample/clinical/sign_bytgr'
      // let bytgrType = ''
      // switch (type) {
      //   case 'HPV':
      //     bytgrType = 1
      //     break
      //   case 'HBV':
      //     bytgrType = 2
      //     break
      //   case 'HCV':
      //     bytgrType = 3
      //     break
      //   case 'EBV':
      //     bytgrType = 4
      //     break
      //   case 'HIV':
      //     bytgrType = 5
      //     break
      //   case 'HP':
      //     bytgrType = 6
      //     break
      // }
      // if (bytgrFlag === this[type].bytgrFlag) {
      //   if (this[type].data.length !== 0) {
      //     return false
      //   } else {
      //     bytgrFlag = 3
      //   }
      // }
      // this.$ajax({
      //   url: url,
      //   data: {
      //     sampleBasicId: this.sampleBasicId,
      //     bytgrType: bytgrType,
      //     signType: bytgrFlag
      //   },
      //   cancelToken: this.axiosSource.token
      // }).then(result => {
      //   if (result.code === this.SUCCESS_CODE) {
      //     this.getTgrInfo(bytgrType, bytgrFlag === 2)
      //   } else {
      //     this.$message.error(result.message)
      //   }
      // })
    },
    getImgIndexAndLength (type) {
      let index = 0
      let length = 0
      switch (type) {
        case 'sampleOrderImg':
          length = this.sampleOrderImg.length
          if (this.imgType === type) {
            index = this.sampleOrderImg.findIndex(v => v.fileAbsolutePath === this.imgSrc)
          }
          break
        case 'imgNames':
          length = this.imgNames.length
          if (this.imgType === type) {
            index = this.imgNames.findIndex(v => v.fileAbsolutePath === this.imgSrc)
          }
          break
        case 'followupImgNames':
          length = this.followupImgNames.length
          if (this.imgType === type) {
            index = this.followupImgNames.findIndex(v => v.fileAbsolutePath === this.imgSrc)
          }
          break
        case 'supplementaryImgs':
          length = this.supplementaryImgs.length
          if (this.imgType === type) {
            index = this.supplementaryImgs.findIndex(v => v.fileAbsolutePath === this.imgSrc)
          }
          break
      }
      return length === 0 ? '' : `(${index + 1} / ${length})`
    },
    // 病原体感染的部分单选变化后,如果是不祥则需要输入原因
    handlePathogenRadioChange (v, type) {
      if (v === 2) {
        this.handleAddUnknownReason(type)
      } else {
        this.$set(this[type], 'unknownReason', '')
      }
    },
    handleAddUnknownReason (type) {
      this.$prompt('请输入不详原因', '提示', {
        confirmButtonText: '确定',
        showClose: false,
        showCancelButton: false,
        closeOnClickModal: false,
        closeOnPressEscape: false,
        beforeClose: (action, instance, done) => {
          console.log(instance)
          if (!instance.inputValue || !instance.inputValue.trim()) {
            this.$message.error('请输入')
          } else {
            this.$set(this[type], 'unknownReason', instance.inputValue)
            done()
          }
        }
      })
    },
    handleIsInClinicalResearchChange () {
      this.form.projectNum = ''
    },
    // 图片放大
    handleMagnify () {
      if (this.multiples >= 300) {
        this.multiples = 300
      } else {
        this.multiples += 25
      }
      this.$refs.img.style.width = this.imgWidth * (this.multiples / 100) + 'px'
      this.$refs.img.style.maxHeight = '100%'
      this.$refs.img.style.height = ''
      this.$refs.img.style.maxWidth = ''
      // if (this.deg === 90 || this.deg === 270) {
      //   this.$refs.img.style.height = this.imgWidth * (this.multiples / 100) + 'px'
      //   this.$refs.img.style.width = ''
      //   this.$refs.img.style.maxWidth = '100%'
      //   this.$refs.img.style.maxHeight = ''
      // } else {
      //   this.$refs.img.style.width = this.imgWidth * (this.multiples / 100) + 'px'
      //   this.$refs.img.style.maxHeight = '100%'
      //   this.$refs.img.style.height = ''
      //   this.$refs.img.style.maxWidth = ''
      // }
    },

    // 图片缩小
    handleShrink () {
      if (this.multiples <= 25) {
        this.multiples = 25
      } else {
        this.multiples -= 25
      }
      this.$refs.img.style.width = this.imgWidth * (this.multiples / 100) + 'px'
      this.$refs.img.style.maxHeight = '100%'
      this.$refs.img.style.height = ''
      this.$refs.img.style.maxWidth = ''
      // if (this.deg === 90 || this.deg === 270) {
      //   this.$refs.img.style.height = this.imgWidth * (this.multiples / 100) + 'px'
      //   this.$refs.img.style.width = ''
      //   this.$refs.img.style.maxWidth = '100%'
      //   this.$refs.img.style.maxHeight = ''
      // } else {
      //   this.$refs.img.style.width = this.imgWidth * (this.multiples / 100) + 'px'
      //   this.$refs.img.style.maxHeight = '100%'
      //   this.$refs.img.style.height = ''
      //   this.$refs.img.style.maxWidth = ''
      // }
    },

    // 图片旋转
    handleRotate () {
      this.deg += 90
      if (this.deg >= 360) {
        this.deg = 0
      }
      // if (this.deg === 90 || this.deg === 270) {
      //   this.$refs.img.style.height = this.imgWidth * (this.multiples / 100) + 'px'
      //   this.$refs.img.style.width = ''
      //   this.$refs.img.style.maxWidth = '100%'
      //   this.$refs.img.style.maxHeight = ''
      // } else {
      //   this.$refs.img.style.width = this.imgWidth * (this.multiples / 100) + 'px'
      //   this.$refs.img.style.maxHeight = '100%'
      //   this.$refs.img.style.height = ''
      //   this.$refs.img.style.maxWidth = ''
      // }
    },
    handleViewPdf (url) {
      window.open(url, '_blank')
    },
    handleImgLoad () {
      this.imgLoading = false
      this.transform.maxLength = this.$refs.imgDiv.clientWidth
    },
    handleImgError (e) {
      this.imgLoading = false
      e.target.alt = '加载失败'
    }
  }
}
</script>

<style scoped lang="scss">
.row-style {
  margin: 15px 25px 0;
}
.title-checkbox {
  margin: 0 8px 0 auto
}
/deep/ .el-collapse-item__arrow {
  margin: 0;
}
.infoContent{
  height: calc(100vh - 20px - 40px - 44px - 53px);
  display: flex;
  .colHeight{
    height: 40px;
  }
  .left{
    padding: 0 5px;
    width: 55%;
    overflow-y: auto;
    .selectItem{
      width: 100%;
    }
    .redStyle >>>.el-form-item__label{
      color: #F56C6C;
    }
    .item{
      min-height: 40px;
    }
    >>>.el-input__inner{
      color: #353535;
    }
    .radiotherapyTable >>>.el-table__header .el-checkbox, .drugHistoryTable >>>.el-table__header .el-checkbox {
      display: none;
    }
    >>>.el-collapse-item{
      margin-bottom: 10px;
    }
    >>>.el-collapse-item__wrap{
      padding-top: 10px;
    }
    >>>.el-collapse-item__header{
      padding: 0 10px;
      height: 38px;
      line-height: 38px;
      background-color: #e9f3ff;
      border-radius: 5px;
    }
    .isOpen >>>.el-collapse-item__header{
      background-color: #b8d8fe;
    }
    .productName{
      width: 100%;
      overflow: hidden;
      text-overflow:ellipsis;
      white-space: nowrap;
    }
    .resultTitle{
      height: 20px;
      line-height: 20px;
      font-size: 14px;
    }
    table.resultTable{
      width: 100%;
      background-color: #daeef3;
      border-collapse: collapse;
    }
    table.resultTable th{
      text-align: left;
      font-size: 14px;
      color: #438fa3;
      padding: 0 4px;
      background-color: #ffffff;
      border: 1px solid #438fa3;
    }
    table.resultTable td{
      padding: 2px 4px;
      border: 1px solid #438fa3;
    }
  }
  .right{
    border-left: 1px solid #DCDFE6;
    padding: 0 5px;
    flex: 1;
    overflow-y: auto;
    height: 100%;
    .picture{
      margin: 10px auto;
      width: 90%;
      height: calc(100% - 54px - 28px - 20px - 40px);
      .image{
        top: 0;
        left: 0;
        height: 100%;
        width: 100%;
        overflow: auto;
      }
    }
    .title{
      height: 30px;
      line-height: 30px;
      font-size: 13px;
    }
  }
}
>>>.el-dialog{
  margin: 15px auto !important;
}
>>>.el-dialog__header{
  padding: 10px;
}
>>>.el-dialog__body{
  padding: 10px!important;
}
>>>.el-dialog__footer{
  padding: 10px;
}
>>>.el-table th{
  background: #ffffff;
}
>>>.el-table, >>>.el-table thead{
  font-size: 12px;
  font-weight: normal;
}
>>>.el-input-group__append{
  padding: 0 10px;
}
/deep/ .el-textarea.is-disabled .el-textarea__inner {
  color: #606266;
  background-color: #fff;
}
/deep/ .el-radio__input.is-disabled+span.el-radio__label {
  color: #606266;
}
/deep/ .el-radio__input.is-checked+.el-radio__label {
  color: #409EFF!important;
}
/deep/ .el-checkbox__input.is-disabled+span.el-checkbox__label {
  color: #606266;
}
/deep/ .el-checkbox__input.is-checked+.el-checkbox__label {
  color: #409EFF!important;
}

/deep/ .el-form-item__label,.el-input--mini {
  font-size: 12px;
}

/deep/ .el-input--mini .el-input__inner {
  height: 28px;
  line-height: 28px;
}
</style>
