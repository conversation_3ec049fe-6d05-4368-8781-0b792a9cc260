<template>
  <div>
    <el-dialog
      :title="title"
      :visible.sync="visible"
      :close-on-click-modal="false"
      width="850px"
      top="calc((40vh - 64px - 73px - 20px - 50px)/2)"
      :before-close="handleClose"
      @opened="handleOpen">
      <el-form ref="form" :model="form" class="form" inline :rules="rules" label-width="120px">
        <el-form-item prop="postoperativeSampleNumber" label="术后样例编号">
          <el-input
            v-model.trim="form.postoperativeSampleNumber"
            size="mini"
            maxlength="50"
            clearable
            placeholder="请输入"></el-input>
        </el-form-item>

        <el-form-item prop="lockTissueNumber" label="锁定组织编号">
          <el-input
            v-model.trim="form.lockTissueNumber"
            size="mini"
            type="textarea"
            clearable
            placeholder="请输入"></el-input>
        </el-form-item>

        <el-form-item prop="readCancerSpecies" label="解读匹配癌种">
          <el-select
            v-model.trim="form.readCancerSpecies"
            size="mini"
            multiple
            filterable
            collapse-tags
            clearable
            placeholder="请选择">
            <el-option v-for="(item, index) in cancerList" :label="item" :value="item" :key="index"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item prop="sampleCancerType" label="样本癌种类型">
          <el-select
            v-model.trim="form.sampleCancerType"
            size="mini"
            maxlength="50"
            filterable
            clearable
            placeholder="请选择">
            <el-option v-for="(item, index) in cancerTypes" :label="item" :value="item" :key="index"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item prop="coreProbeName" label="核心探针名称">
          <el-select
            v-model.trim="form.coreProbeName"
            size="mini"
            filterable
            clearable
            placeholder="请选择">
            <el-option v-for="(item, index) in probeNames" :label="item" :value="item" :key="index"></el-option>
          </el-select>
        </el-form-item>

        <template v-if="isRepeat">
          <el-form-item prop="reason" label="重订购原因">
            <el-select
              v-model.trim="form.reason"
              size="mini"
              filterable
              clearable
              placeholder="请选择">
              <el-option v-for="(item, index) in reasonList" :label="item" :value="item" :key="index"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item prop="reasonDetail" label="具体原因描述">
            <el-input
              v-model.trim="form.reasonDetail"
              size="mini"
              type="textarea"
              clearable
              maxlength="200"
              placeholder="请输入"></el-input>
          </el-form-item>
        </template>

        <el-form-item prop="probeTag" label="探针标签">
          <el-radio-group v-model="form.probeTag">
            <el-radio :label="1">院内组织</el-radio>
            <el-radio :label="0">院外组织</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-row :gutter="10">
          <el-col :span="24">
            <el-form-item prop="fileList" label="位点数据上传">
              <el-upload
                class="upload-demo"
                :action="action"
                ref="upload"
                :limit="1"
                :on-success="handleOnSuccess"
                :before-upload="handleBeforeUpload"
                :before-remove="handleRemove"
                :file-list="form.fileList">
                <div>
                  <el-button size="mini" type="primary">点击上传</el-button>
                  <span slot="tip" class="el-upload__tip">可上传excel文件</span>
                </div>
              </el-upload>
              <el-button type="text" @click="handleDownloadTemplate">模版下载</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <span slot="footer">
        <el-button size="mini" @click="handleClose">取消</el-button>
        <el-button size="mini" type="primary" :loading="loading" @click="handleConfirm">确定</el-button>
      </span>

    </el-dialog>
  </div>
</template>

<script>
import mixins from '../../../util/mixins'
import constants from '../../../util/constants'
import util, {awaitWrap, setGroupData} from '../../../util/util'

export default {
  mixins: [mixins.dialogBaseInfo],
  props: {
    isRepeat: {
      type: Boolean,
      default: false
    },
    formData: { // 解构和form一致
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data () {
    return {
      loading: false,
      title: '',
      action: constants.JS_CONTEXT + '/system/probe/site_data_upload',
      cancerList: [],
      cancerTypes: ['乳腺癌', '肠癌', '肺癌', '泛癌', '胰腺癌', '小细胞肺癌', '黑色素瘤'],
      probeNames: ['kf', 'kc', 'kb', 'ks', 'mf', 'mc', 'mb', 'ms', 'mp', 'mx', 'mm'],
      reasonList: ['合成原因', '定制错误', '前端要求', '其他'],
      form: {
        postoperativeSampleNumber: '',
        readCancerSpecies: '',
        sampleCancerType: '',
        coreProbeName: '',
        probeTag: 1,
        lockTissueNumber: '',
        isCustomMadeProbe: 1,
        fileList: [],
        reason: null,
        reasonDetail: null,
        group: '',
        path: ''
      },
      searchDialogVisible: false,
      rules: {
        postoperativeSampleNumber: [
          {required: true, message: '请输入术后样例编号', trigger: ['blur', 'change']}
        ],
        readCancerSpecies: [
          {required: true, message: '请选择解读匹配癌种', trigger: ['blur', 'change']}
        ],
        sampleCancerType: [
          {required: true, message: '请选择样本癌种类型', trigger: ['blur', 'change']}
        ],
        coreProbeName: [
          {required: true, message: '请选择样本癌种类型', trigger: ['blur', 'change']}
        ],
        probeTag: [
          {required: true, message: '请选择探针标签', trigger: ['blur', 'change']}
        ],
        isCustomMadeProbe: [
          {required: true, message: '请选择是否定制', trigger: ['blur', 'change']}
        ],
        reason: [
          {required: true, message: '请选择重订购原因', trigger: ['blur', 'change']}
        ]
      }
    }
  },
  methods: {
    // 初始化表单数据
    async initFormData () {
      if (!this.isRepeat) {
        this.form = this.$options.data().form
        return
      }
      this.form = Object.assign(this.form, this.formData)
      console.log(this.form)
    },
    async handleOpen () {
      this.$refs.upload && this.$refs.upload.clearFiles()
      this.$refs.form.resetFields()
      this.title = this.isRepeat ? '探针重订购' : '探针订购'
      await this.getCancerInfo()
      this.initFormData()
    },
    // 获取癌种信息
    async getCancerInfo () {
      const {res} = await awaitWrap(this.$ajax({
        url: '/system/probe/read_cancer_types',
        method: 'get'
      }))
      if (res && res.code === this.SUCCESS_CODE) {
        this.cancerList = res.data || []
      } else {
        this.$message.error(res.message)
      }
    },
    // 下载模版文件
    handleDownloadTemplate () {
      this.$ajax({
        url: `/system/probe/download_site_data_template`,
        method: 'get',
        responseType: 'blob'
      }).then(res => {
        util.readBlob(res.data).then(() => {
          util.downloadFile(res, true)
        }).catch(msg => {
          this.$message.error(msg)
        })
      })
    },
    async handleValidate () {
      const {code, message, data = {}} = await this.$ajax({
        url: '/system/probe/before_submission_data_check',
        data: {
          postoperativeSampleNumber: this.form.postoperativeSampleNumber,
          lockTissueNumber: this.form.lockTissueNumber
        }
      })
      if (code !== this.SUCCESS_CODE) {
        this.$message.error(message)
        return false
      }
      if (data.noSamePatientIdNum) {
        await this.$confirm(`以下锁定组织编号与术后样例编号的Patient ID不一致，请确认是否继续订购探针：<span style="color: deepskyblue">${data.noSamePatientIdNum}</span>`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          showClose: false,
          dangerouslyUseHTMLString: true,
          closeOnClickModal: false,
          closeOnPressEscape: false,
          type: 'warning'
        })
        return true
      }
      if (data.patientid) {
        // 2.相同Patient ID多次订购探针时新增提示
        await this.$confirm(`当前Patient ID（${data.patientid}）已订购过探针。<br/> 请确认是否继续订购？`, '提示', {
          confirmButtonText: '继续订购',
          cancelButtonText: '取消',
          dangerouslyUseHTMLString: true,
          closeOnClickModal: false,
          closeOnPressEscape: false,
          type: 'warning'
        })
        return true
      }
      return true
    },
    // 保存配置
    handleConfirm () {
      this.$refs.form.validate(async valid => {
        if (valid) {
          this.form.lockTissueNumber = this.form.lockTissueNumber || ''
          this.form.lockTissueNumber = setGroupData(this.form.lockTissueNumber, ',', true)

          let validate = await this.handleValidate()
          if (!validate) return
          let data = {
            postoperativeSampleNumber: this.form.postoperativeSampleNumber,
            readCancerSpecies: this.form.readCancerSpecies.join(','),
            sampleCancerType: this.form.sampleCancerType,
            coreProbeName: this.form.coreProbeName,
            group: this.form.group,
            path: this.form.path,
            probeTag: this.form.probeTag,
            lockTissueNumber: this.form.lockTissueNumber
          }
          if (this.isRepeat) {
            data = {freorderReason: this.form.reason, ffailureReorderReason: this.form.reasonDetail, ...data}
          }

          this.loading = true
          this.$ajax({
            url: !this.isRepeat ? '/system/probe/information_submission' : '/system/probe/information_reorder_submission',
            data: data
          }).then(result => {
            if (result.code === this.SUCCESS_CODE) {
              this.$message.success('订购成功')
              this.visible = false
              this.$emit('dialogConfirmEvent')
            }
          }).finally(() => {
            this.loading = false
          })
        }
      })
    },
    handleOnSuccess (res) {
      this.loading = false
      if (res && res.code === this.SUCCESS_CODE) {
        this.form.group = res.data.group
        this.form.path = res.data.path
        this.form.fileList = [res.data.absolutePath]
      } else {
        this.$message.error(res.message)
      }
    },
    handleRemove () {
      this.form.fileList = null
      this.$refs.upload.clearFiles()
    },
    // 上传前校验文件名和文件大小
    handleBeforeUpload (file) {
      let name = file.name
      let size = file.size
      if (/\.(xlsx|xls|zip|rar)$/i.test(name)) {
        if (size > constants.FILE_SIZE_LIMIT * 1024 * 1024 * 500) {
          this.loading = false
          this.$message.error(`文件: ${name} ,大小超过500M，无法上传`)
          return false
        } else {
          return true
        }
      } else {
        this.loading = false
        this.$message.error('只能上传Excel文件')
        return false
      }
    }
  }
}
</script>

<style scoped>
/deep/ .el-input {
  width: 250px;
}

/deep/ .el-select-dropdown {
  width: 250px;
}

/deep/ .el-textarea__inner {
  width: 250px;
}
</style>
